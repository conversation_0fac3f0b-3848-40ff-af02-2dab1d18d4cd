﻿// <copyright file="FeatureControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Common.Core.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.FeatureStore.Contracts.Mfe;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230601Preview.Controllers.Workspaces;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Clients;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Configuration;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Services;
using Moq;
using Xunit;
using Feature = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.Feature;
using FeatureDtoResource = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Resource<Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.Feature>;
using FeatureResource = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.ArmCommon.Resource<Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Featurestore.Feature>;
using FeatureSetVersion = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturesetVersion;
using FeatureSetVersionDtoResource = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Resource<Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturesetVersion>;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230601Preview
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    [MfeTest(TestCategory.Unit, ScenarioScope.Features)]
    public class FeatureControllerTests : IDisposable
    {
        private const string _featureSetName = "myFeatureSet";
        private const string _featureSetVersion = "1";

        private readonly FeatureController _featureController;
        private readonly WorkspaceContext2 _workspaceContext;

        public FeatureControllerTests()
        {
            Mock<ILoggerFactory> loggerFactory = new Mock<ILoggerFactory>();
            Mock<ILogger> logger = new Mock<ILogger>();
            loggerFactory.Setup(m => m.CreateLogger(It.IsAny<string>())).Returns(logger.Object);

            var hostConfig = new Mock<IOptionsMonitor<ExperimentationHostConfiguration>>();
            hostConfig.Setup(m => m.CurrentValue).Returns(new ExperimentationHostConfiguration
            {
                FeatureStoreHostUri = "http://test",
            });

            var featureStoreConfig = new Mock<IOptionsMonitor<FeaturestoreConfiguration>>();
            featureStoreConfig.Setup(m => m.CurrentValue).Returns(new FeaturestoreConfiguration());

            var mockDto = new FeatureSetVersionDtoResource()
            {
                Name = "Name",
                Properties = new FeatureSetVersion()
                {
                    Description = "Description",
                }
            };

            var mockFeatureDto = new FeatureDtoResource()
            {
                Properties = new Feature
                {
                    FeatureName = "Name",
                    Description = "Description",
                }
            };

            var mockFeatureDtoList = new PaginatedResult<FeatureDtoResource>()
            {
                Value = new List<FeatureDtoResource>() { mockFeatureDto }
            };

            var controller = new Mock<IFeatureSetVersionController>();
            controller.Setup(x => x.GetFeatureAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockFeatureDto);
            controller.Setup(x => x.ListFeaturesAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<ListViewType>(),
                It.IsAny<string>(),
                It.IsAny<int>())).ReturnsAsync(mockFeatureDtoList);

            Mock<IHttpContextAccessor> httpContextAccessor = new Mock<IHttpContextAccessor>();
            Mock<IS2SClientWrapper> s2sClientFactory = new Mock<IS2SClientWrapper>();
            s2sClientFactory.Setup(x => x.CreateS2SClient<IFeatureSetVersionController>(
                It.IsAny<Uri>(),
                false,
                It.IsAny<Dictionary<string, string>>(),
                null,
                It.IsAny<TimeSpan>(),
                null,
                null,
                null)).Returns(controller.Object);

            IFeaturesetVersionService featureSetVersionService = new FeaturesetVersionService(
                                loggerFactory.Object,
                                s2sClientFactory.Object,
                                hostConfig.Object,
                                featureStoreConfig.Object,
                                httpContextAccessor.Object);

            Mock<IArmContextAccessor> armContextAccessor = new Mock<IArmContextAccessor>();
            armContextAccessor.Setup(x => x.GetResourceUri()).Returns(new Uri("http://localhost"));

            var mapperProfile = new Api.V20230601Preview.MapperProfile();
            var mapperConfig = new MapperConfiguration(config =>
            {
                config.AddProfile(mapperProfile);
            });
            mapperConfig.AssertConfigurationIsValid();
            mapperConfig.CompileMappings();

            var mapper = new Mapper(mapperConfig);

            _featureController = new FeatureController(
                armContextAccessor.Object,
                featureSetVersionService,
                mapper);

            _workspaceContext = new WorkspaceContext2(
                Guid.NewGuid(),
                "ws",
                "rg",
                Guid.NewGuid(),
                "eastus",
                "tenantid",
                null);
        }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        public async Task GetFeatureTest()
        {
            var actionResult = await _featureController.GetEntity(
                _workspaceContext,
                _featureSetName,
                _featureSetVersion,
                "Name",
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var feature = okResult.Value as FeatureResource;
            Assert.Equal("Name", feature.Properties.FeatureName);
            Assert.Equal("Description", feature.Properties.Description);
        }

        [Fact]
        public async Task ListFeaturesTest()
        {
            var actionResult = await _featureController.List(
                _workspaceContext,
                _featureSetName,
                _featureSetVersion,
                null,
                null,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var features = okResult.Value as ArmPaginatedResult<FeatureResource>;
            Assert.Single(features.Value);
            Assert.Equal("Name", features.Value.First().Properties.FeatureName);
            Assert.Equal("Description", features.Value.First().Properties.Description);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _featureController.Dispose();
            }
        }
    }
}