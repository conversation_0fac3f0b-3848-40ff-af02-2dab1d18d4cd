﻿// <copyright file="ModelControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Asset;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230601Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230601Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230601Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Models)]
    public class ModelControllerTests : IStaticFixture<TestFixtureManagementFrontEnd>, IAsyncLifetime
    {
        private const string _modelTestContainerNamePrefix = "test_mfe";

        private readonly IModelVersionController _modelVersionController;

        private readonly IModelContainerController _modelContainerController;

        private readonly IDataStoreController _datastoreController;

        public ModelControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            Fixture = fixture;
            _modelVersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();
            _modelContainerController = Fixture.ManagementFrontEndClient.GetController<IModelContainerController>();
            _datastoreController = ServiceInvoker.Create<IDataStoreController>(
                fixture.Instance.HttpClient,
                new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));
        }

        public TestFixtureManagementFrontEnd Fixture { get; }

        public static string GenerateContainerName(DateTime timestamp)
        {
            return $"{_modelTestContainerNamePrefix}_{timestamp:yyyyMMddHHmmss}_{Guid.NewGuid().ToString()[..8]}";
        }

        public static async Task<Resource<ModelVersion>> GenerateVersion(IDataStoreController datastoreController, TestFixtureManagementFrontEnd fixture, ModelType modelType = ModelType.custom_model)
        {
            // Make sure we have a blob to use as the model artifact.
            var datastore = await datastoreController.Get(fixture.SubscriptionId.ToString(), fixture.ResourceGroupName, fixture.WorkspaceName, "workspaceblobstore").ConfigureAwait(false);
            var storageConnectionDto = await fixture.GetStorageConnection().ConfigureAwait(false);
            await JobHelpers.CreateJob(fixture, "TestExperiment", "TestJob");
            var path = string.Empty;
            switch (modelType)
            {
                case ModelType.mlflow_model:
                    await BlobHelpers.UploadBlobFromLocalFilepath(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionMlflowBlobPath, "Data/mlflowModel").ConfigureAwait(false);
                    path = MfeTestingConstants.ModelVersionMlflowBlobPath;
                    break;
                default:
                    await BlobHelpers.UploadBlob(storageConnectionDto.ConnectionString, datastore.AzureStorageSection.ContainerName, MfeTestingConstants.ModelVersionBlobPath, "some text").ConfigureAwait(false);
                    path = MfeTestingConstants.ModelVersionBlobPath[..MfeTestingConstants.ModelVersionBlobPath.LastIndexOf('/')];
                    break;
            }

            return new Resource<ModelVersion>
            {
                Properties = new ModelVersion
                {
                    Description = "description",
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    ModelType = modelType.ToString(),
                    ModelUri = $"azureml://subscriptions/{fixture.SubscriptionId}/resourceGroups/{fixture.ResourceGroupName}/workspaces/{fixture.WorkspaceName}/datastores/workspaceblobstore/paths/{path}",
                    Properties = new Dictionary<string, string> { { "property-name-1", "property-value-1" } },
                    Tags = new Dictionary<string, string> { { "tag-name-1", "tag-value-1" } },
                    JobName = "TestJob"
                },
            };
        }

        public Resource<ModelVersion> CreateVersion(
           string jobName,
           string stage)
        {
            return new Resource<ModelVersion>()
            {
                Properties = new ModelVersion()
                {
                    IsArchived = stage == AssetStage.Archived,
                    IsAnonymous = stage == AssetStage.Logged,
                    Stage = stage,
                    JobName = jobName,
                    ModelUri = $"azureml://jobs/{jobName}/outputs/hello_output/paths/helloworld.txt"
                }
            };
        }

        [Fact]
        public async Task CreateModelVersionUsingJobOutputs()
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var version = "123";
            var jobName = "modelregistryintegtestfileuploadnamedoutputdata02";
            var request = GenerateModelVersion(new Uri($"azureml://jobs/{jobName}/outputs/hello_output/paths/helloworld.txt"), jobName);

            var modelVersion = await _modelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);
            Assert.Equal(request.Properties.Flavors.Count, modelVersion.Properties.Flavors.Count);
            Assert.True(request.Properties.Flavors.Keys.SequenceEqual(modelVersion.Properties.Flavors.Keys));
            Assert.Equal(request.Properties.ModelType, modelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.JobName, modelVersion.Properties.JobName);
            Assert.Equal(request.Properties.IsArchived, modelVersion.Properties.IsArchived);
            Assert.Equal(request.Properties.IsAnonymous, modelVersion.Properties.IsAnonymous);
            Assert.Equal(request.Properties.Properties.Count, modelVersion.Properties.Properties.Count);
            Assert.True(request.Properties.Properties.Keys.SequenceEqual(modelVersion.Properties.Properties.Keys));
            Assert.True(request.Properties.Properties.Values.SequenceEqual(modelVersion.Properties.Properties.Values));
            Assert.Equal(request.Properties.Tags.Count, modelVersion.Properties.Tags.Count);
            Assert.True(request.Properties.Tags.Keys.SequenceEqual(modelVersion.Properties.Tags.Keys));
            Assert.True(request.Properties.Tags.Values.SequenceEqual(modelVersion.Properties.Tags.Values));

            await _modelVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
        }

        [Theory(DisplayName = "CreateModelVersionWithModelTypeTest")]
        [InlineData(ModelType.custom_model)]
        [InlineData(ModelType.mlflow_model)]
        [InlineData(ModelType.triton_model)]
        public async Task CreateModelVersionWithModelTypeTest(ModelType modelType)
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var version = "123";
            var request = await GenerateVersion(_datastoreController, Fixture, modelType).ConfigureAwait(false);

            var modelVersion = await _modelVersionController.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version, request).ConfigureAwait(false);
            Assert.Equal(request.Properties.Flavors.Count, modelVersion.Properties.Flavors.Count);
            Assert.True(request.Properties.Flavors.Keys.SequenceEqual(modelVersion.Properties.Flavors.Keys));
            Assert.Equal(request.Properties.ModelType, modelVersion.Properties.ModelType);
            Assert.Equal(request.Properties.JobName, modelVersion.Properties.JobName);
            Assert.Equal(request.Properties.IsArchived, modelVersion.Properties.IsArchived);
            Assert.Equal(request.Properties.IsAnonymous, modelVersion.Properties.IsAnonymous);
            Assert.Equal(request.Properties.Properties.Count, modelVersion.Properties.Properties.Count);
            Assert.True(request.Properties.Properties.Keys.SequenceEqual(modelVersion.Properties.Properties.Keys));
            Assert.True(request.Properties.Properties.Values.SequenceEqual(modelVersion.Properties.Properties.Values));
            Assert.Equal(request.Properties.Tags.Count, modelVersion.Properties.Tags.Count);
            Assert.True(request.Properties.Tags.Keys.SequenceEqual(modelVersion.Properties.Tags.Keys));
            Assert.True(request.Properties.Tags.Values.SequenceEqual(modelVersion.Properties.Tags.Values));

            // ModelUri is modified by design if the blob container is not AzureML.
            // Assert.Equal(request.Properties.ModelUri, modelVersion.Properties.ModelUri);

            await _modelVersionController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version).ConfigureAwait(false);
        }

        [Fact]
        public async Task StageTests()
        {
            var name = GenerateContainerName(DateTime.UtcNow);
            var model1Version = "123";
            var jobName = "modelregistryintegtestfileuploadnamedoutputdata02";

            var modelVersion1 = await _modelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                model1Version,
                CreateVersion(jobName, AssetStage.Production)).ConfigureAwait(false);
            modelVersion1.Properties.IsAnonymous.Should().BeFalse();
            modelVersion1.Properties.IsArchived.Should().BeFalse();
            modelVersion1.Properties.Stage.Should().Be(AssetStage.Production);

            modelVersion1.Properties.Stage = AssetStage.Archived;
            var updatedModelVersion1 = await _modelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                model1Version,
                modelVersion1).ConfigureAwait(false);
            updatedModelVersion1.Properties.IsAnonymous.Should().BeFalse();
            updatedModelVersion1.Properties.IsArchived.Should().BeTrue();
            updatedModelVersion1.Properties.Stage.Should().Be(AssetStage.Archived);

            var model2Version = model1Version + 1;
            var modelVersion2 = await _modelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                model2Version,
                CreateVersion(jobName, AssetStage.Production)).ConfigureAwait(false);
            modelVersion2.Properties.IsAnonymous.Should().BeFalse();
            modelVersion2.Properties.IsArchived.Should().BeFalse();
            modelVersion2.Properties.Stage.Should().Be(AssetStage.Production);

            var listProductionModelVersions = await _modelVersionController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                stage: AssetStage.Production).ConfigureAwait(false);
            listProductionModelVersions.Value.Count().Should().Be(1);
            var listedProductionModelVersion = listProductionModelVersions.Value.Single();
            listedProductionModelVersion.Name.Should().Be(model2Version);
            listedProductionModelVersion.Properties.Stage.Should().Be(AssetStage.Production);

            var listArchivedModelVersions = await _modelVersionController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                stage: AssetStage.Archived).ConfigureAwait(false);
            listArchivedModelVersions.Value.Count().Should().Be(1);
            var listedArchivedModelVersion = listArchivedModelVersions.Value.Single();
            listedArchivedModelVersion.Properties.Stage.Should().Be(AssetStage.Archived);
            listedArchivedModelVersion.Name.Should().Be(model1Version);
        }

        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            await CleanUpModels().ConfigureAwait(false);
        }

        protected Resource<ModelVersion> GenerateModelVersion(Uri modelUrl, string jobName)
        {
            return new Resource<ModelVersion>
            {
                Properties = new ModelVersion
                {
                    Description = "description",
                    Flavors = new Dictionary<string, FlavorData>() { { "python_function", new FlavorData { Data = new Dictionary<string, string>() { { "loader_module", "test" } } } } },
                    JobName = jobName,
                    ModelUri = modelUrl.ToString(),
                    ModelType = ModelType.custom_model.ToString(),
                    Properties = new Dictionary<string, string> { { "property-name-1", "property-value-1" } },
                    Tags = new Dictionary<string, string> { { "api-version", "2023-04-01" }, { "tag-name-1", "tag-value-1" } }
                }
            };
        }

        protected async Task CleanUpModels()
        {
            var containers = await _modelContainerController.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, count: 200).ConfigureAwait(false);
            var containersToDelete = containers.Value.ToList()
                .Where(c => c.Name.StartsWith(_modelTestContainerNamePrefix, StringComparison.InvariantCulture) &&
                          (c.Properties.Tags.IsNullOrEmpty() || !c.Properties.Tags.ContainsKey(MfeTestingConstants.ModelDoNotDeleteTagKey)) &&
                          ((TimeSpan)(DateTime.UtcNow - c.SystemData?.CreatedAt)).TotalMinutes > 20)
                .Select(c => c.Name);
            foreach (var containerName in containersToDelete)
            {
                try
                {
                    await _modelContainerController.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, containerName).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error while cleaning up models. Message: {ex.Message}. Exception: {ex.StackTrace}");
                }
            }
        }
    }
}
