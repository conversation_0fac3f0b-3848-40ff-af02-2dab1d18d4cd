﻿// <copyright file="ScheduleControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using FakeItEasy;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.Testing.Arm;
using Microsoft.MachineLearning.Common.WebApi.Security;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.Schema.Contracts;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xunit;
using Schedule = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Schedule;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221001Preview
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "VSTHRD002:Avoid problematic synchronous waits", Justification = "Need to synchronously get result for some cases including Dispose and PollStatus. ")]
    public class E2EScheduleControllerTests : ScheduleControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>, IDisposable
    {
        private Resource<Schedule> _originalRequest = new Resource<Schedule>();
        private string _name = "generateNameEachTime";

        public E2EScheduleControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
                : base(fixture)
        {
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        [MfeTest(TestCategory.Integration, Api.ScenarioScope.Trigger)]
        public async Task TestTriggerE2E()
        {
            _name = TestHelpers.GenerateName();
            var create = await TestCreateTrigger(_name).ConfigureAwait(false);
            var retrieve = await TestGetTrigger(_name).ConfigureAwait(false);
            var list = await TestListTrigger(_name).ConfigureAwait(false);
            var update = await TestUpdateTrigger(_name, retrieve).ConfigureAwait(false);
            await TestDeleteTrigger(_name).ConfigureAwait(false);
        }

        protected virtual void Dispose(bool v)
        {
            try
            {
                var exist = TestGetTrigger(_name).GetAwaiter().GetResult();
                TestDeleteTrigger(_name).GetAwaiter().GetResult();
            }
            catch (Exception)
            {
                // Only need to delete if get result exist;
            }
        }

        private async Task<ICheckableLongRunningOperationResponse<Resource<Schedule>>> TestCreateTrigger(string name)
        {
            var schedule = new RecurrenceSchedule()
            {
                Hours = new List<int> { 1, 2 },
                Minutes = new List<int> { 1, 2 },
                MonthDays = new List<int> { 5 }
            };

            InternalSharedServices.Contracts.PipelineJobSettings settings = new InternalSharedServices.Contracts.PipelineJobSettings
            {
                ContinueRunOnStepFailure = false,
                Datastore = new MfeResourceArmScope(Fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeDatastore, "workspaceblobstore").ToString(),
                DefaultDatastoreName = "workspaceblobstore"
            };

            var computeId = await ComputeHelpers.GetComputeId(Fixture).ConfigureAwait(false);
            var componentArmId = GetComponentId();
            var experimentName = "Trigger_" + Guid.NewGuid().ToString();

            var step1 = JObject.FromObject(new
            {
                computeId = computeId,
                componentId = componentArmId,
                inputs = new
                {
                    int_a = new { type = "Literal", value = "52" },
                    int_b = new { type = "Literal", value = "78" }
                },
                outputs = new
                {
                    out_sum = new { type = "Dataset", mode = "ReadWriteMount" },
                    out_prod = new { type = "Dataset", mode = "ReadWriteMount" }
                }
            });

            var pipelineJob = new PipelineJob()
            {
                JobType = JobType.Pipeline,
                ExperimentName = experimentName,
                ComputeId = computeId,
                Settings = JObject.FromObject(settings),
                Inputs = new Dictionary<string, JobInput>
                {
                    {
                        "sample_input_data",
                        new MLTableJobInput()
                        {
                            JobInputType = JobInputType.uri_file,
                            Mode = InputDeliveryMode.ReadOnlyMount,
                            Description = null,
                            Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv"
                        }
                    },
                    {
                        "sample_input_string",
                        new LiteralJobInput()
                        {
                            JobInputType = JobInputType.literal,
                            Value = "Fake_Hello_Python_World",
                            Description = null,
                        }
                    }
                },
                Jobs = new Dictionary<string, JObject>
                {
                    { "step1", step1 }
                },
                Outputs = new Dictionary<string, JobOutput>
                 {
                    {
                        "sample_output_data",
                        new MLTableJobOutput()
                        {
                            JobOutputType = JobOutputType.mltable,
                            Mode = OutputDeliveryMode.ReadWriteMount,
                            Description = null
                        }
                    }
                },
                IsArchived = true,
                Tags = null
            };

            _originalRequest = new Resource<Schedule>
            {
                Properties = new Schedule()
                {
                    Trigger = new RecurrenceTrigger()
                    {
                        TriggerType = TriggerType.Recurrence,
                        Frequency = RecurrenceFrequency.Month,
                        Interval = 5,
                        Schedule = schedule
                    },
                    Action = new JobScheduleAction()
                    {
                        ActionType = ScheduleActionType.CreateJob,
                        JobDefinition = pipelineJob
                    },
                    DisplayName = name
                }
            };
            var createdJob = await Controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "Trigger" + name, _originalRequest).ConfigureAwait(false);

            var retrieveJob = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "Trigger" + name);
            retrieveJob.Should().NotBeNull();

            retrieveJob.Properties.DisplayName.Should().Be(name);
            retrieveJob.Properties.Trigger.TriggerType.Should().Be(TriggerType.Recurrence);
            retrieveJob.Properties.Trigger.Should().BeOfType<RecurrenceTrigger>();

            var recurrence = (RecurrenceTrigger)retrieveJob.Properties.Trigger;
            recurrence.Frequency.Should().Be(RecurrenceFrequency.Month);
            recurrence.Interval.Should().Be(5);
            recurrence.TimeZone.Should().Be("UTC");

            var scheduleResult = recurrence.Schedule;
            scheduleResult.Hours[0].Should().Be(1);
            scheduleResult.Hours[1].Should().Be(2);
            scheduleResult.Hours.Count().Should().Be(2);
            scheduleResult.Minutes[0].Should().Be(1);
            scheduleResult.Minutes[1].Should().Be(2);
            scheduleResult.Minutes.Count().Should().Be(2);
            scheduleResult.MonthDays[0].Should().Be(5);
            scheduleResult.MonthDays.Count().Should().Be(1);

            return createdJob;
        }

        private async Task<ICheckableLongRunningOperationResponse<Resource<Schedule>>> TestUpdateTrigger(string name, Resource<Schedule> createdSchedule)
        {
            var schedule = new RecurrenceSchedule()
            {
                Hours = new List<int> { 1, 2 },
                Minutes = new List<int> { 1, 2 },
                MonthDays = new List<int> { 6 }
            };
            var newTrigger = new RecurrenceTrigger()
            {
                TriggerType = TriggerType.Recurrence,
                Frequency = RecurrenceFrequency.Month,
                Interval = 6,
                Schedule = schedule
            };
            createdSchedule.Properties.Trigger = newTrigger;
            createdSchedule.Properties.IsEnabled = false;
            var updateJob = await Controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "Trigger" + name, createdSchedule).ConfigureAwait(false);

            var retrieveJob = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "Trigger" + name);
            retrieveJob.Should().NotBeNull();
            var fetchRecurrence = (RecurrenceTrigger)retrieveJob.Properties.Trigger;
            fetchRecurrence.Interval.Should().Be(6);
            fetchRecurrence.Schedule.MonthDays[0].Should().Be(6);

            return updateJob;
        }

        private async Task<Resource<Schedule>> TestGetTrigger(string name)
        {
            var retrieveJob = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "Trigger" + name);
            return retrieveJob;
        }

        private async Task<List<Resource<Schedule>>> TestListTrigger(string name)
        {
            var result = await Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName);
            var listJob = result.Value;
            listJob.SingleOrDefault(x => x.Properties.DisplayName == name);
            listJob.Should().NotBeNull();
            return listJob;
        }

        private async Task<ICheckableLongRunningOperationResponse<Resource<Schedule>>> TestDeleteTrigger(string name)
        {
            var result = await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "Trigger" + name).ConfigureAwait(false);
            return result;
        }

        private string GetComponentId()
        {
            var str = "/subscriptions/" + Fixture.SubscriptionId + "/resourceGroups/" + Fixture.ResourceGroupName + "/providers/Microsoft.MachineLearningServices" + "/workspaces/" + Fixture.WorkspaceName + "/components/111111/versions/123";
            return str;
        }
    }

    [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "VSTHRD002:Avoid problematic synchronous waits", Justification = "Need to synchronously get result for some cases including Dispose and PollStatus. ")]
    public abstract class ScheduleControllerTests<TFixture>
    where TFixture : TestFixtureManagementFrontEnd
    {
        public ScheduleControllerTests(TFixture fixture)
        {
            Fixture = fixture;
            Controller = Fixture.ManagementFrontEndClient.GetController<IScheduleController>(afterResponse: EnsureStatusSucceeded);
        }

        public IScheduleController Controller { get; }

        public TFixture Fixture { get; }

        public void EnsureStatusSucceeded(HttpResponseMessage response)
        {
            // Only schedule create use PUT Method. Here we use operationId to fetch status result to confirm we've created the schedule successfully.
            if ((response.RequestMessage.Method == HttpMethod.Put || response.RequestMessage.Method == HttpMethod.Delete) && response.Headers.Contains(MfeConstants.AzureAsyncOperation))
            {
                var url = response.Headers.GetValues(MfeConstants.AzureAsyncOperation).First();
                ScheduleProvisioningStatus status;
                int maxRetries = 30; // Maximum number of attempts
                int retryCount = 0;
                int delayMs = 2000; // 2 seconds between retries

                do
                {
                    if (retryCount > 0)
                    {
                        // Wait before retrying (except for first attempt)
                        Task.Delay(delayMs).GetAwaiter().GetResult();
                    }

                    try
                    {
                        var operationResult = PollOperationStatus(url).GetAwaiter().GetResult();
                        var body = operationResult.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                        var objStr = JsonConvert.DeserializeObject<Contracts.V20221001Preview.ArmCommon.AzureAsyncOperationResource<ScheduleProvisioningStatus>>(body);
                        status = objStr.Status;
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"Failed to poll operation status after {retryCount} attempts. Last error: {ex.Message}", ex);
                    }

                    retryCount++;

                    if (retryCount >= maxRetries)
                    {
                        throw new TimeoutException($"Schedule operation did not complete after {maxRetries} attempts (approximately {(maxRetries * delayMs) / 1000} seconds). Last status: {status}");
                    }
                }
                while (status == ScheduleProvisioningStatus.Creating ||
                       status == ScheduleProvisioningStatus.Updating ||
                       status == ScheduleProvisioningStatus.Deleting);

                Assert.True(status == ScheduleProvisioningStatus.Succeeded, $"Expected status to be Succeeded, but was {status}");
            }
        }

        public async Task<HttpResponseMessage> PollOperationStatus(string address)
        {
            var httpClient = new HttpClient();
            var s2sConfigMonitor = A.Fake<IOptionsMonitor<S2SConfiguration>>();
            ArmTestingConfiguration armTestingConfiguration = new ArmTestingConfiguration();
            var s2sConfiguration = new S2SConfiguration()
            {
                Authority = armTestingConfiguration.MicrosoftTenantLoginUrl,
                ClientId = Fixture.FetchTestSecretsFromKeyVault.Secrets["kubernetes--sp-client-id"],
                InjectedClientCertificate = Fixture.FetchTestSecretsFromKeyVault.BuildKVSecrets["certificate--cluster-sp-pfx"],
            };
            A.CallTo(() => s2sConfigMonitor.CurrentValue).Returns(s2sConfiguration);
            var s2sTokenProvider = new ServicePrincipalAADTokenProvider(s2sConfigMonitor, null);
            var s2sToken = await s2sTokenProvider.AcquireTokenForServicePrincipal("https://login.microsoftonline.com/72f988bf-86f1-41af-91ab-2d7cd011db47", "https://management.core.windows.net/", Fixture.S2SRole.AppId).ConfigureAwait(false);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", s2sToken.AccessToken);
            CreatedBy createdBy = new CreatedBy
            {
                UserObjectId = Fixture.ContributorRole.AppId,
                UserName = Fixture.ContributorRole.Tenant,
                UserTenantId = Fixture.ContributorRole.DisplayName,
            };
            string createdByHeaderValue = SerializeCreatedBy(createdBy);
            httpClient.DefaultRequestHeaders.Add("x-ms-baggage", createdByHeaderValue);
            var ans = await httpClient.GetAsync(new Uri(address)).ConfigureAwait(false);
            return ans;
        }

        private string SerializeCreatedBy(CreatedBy createdBy)
        {
            if (createdBy.UserTenantId != null && createdBy.UserObjectId != null && createdBy.UserName != null)
            {
                Dictionary<string, string> serializable = new Dictionary<string, string>();
                serializable["tid"] = createdBy.UserTenantId;
                serializable["oid"] = createdBy.UserObjectId;
                serializable["uname"] = createdBy.UserName;

                return ToUrlSafeBase64String(JsonConvert.SerializeObject(serializable));
            }

            return null;
        }

        private string ToUrlSafeBase64String(string input)
        {
            var normalBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(input));

            var urlSafe = normalBase64
                .Replace('+', '-')
                .Replace('/', '_')
                .TrimEnd('=');

            return urlSafe;
        }
    }
}
