﻿// <copyright file="SparkJobControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using FluentAssertions.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Artifact.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.Feed.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.AutoML;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Jobs;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Jobs.Spark;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221001Preview
{
    public class SparkE2EJobControllerTests : SparkJobControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public SparkE2EJobControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture, ServiceInvoker.Create<IArtifactControllerWorkspaceV2Routes>(fixture.Instance.HttpClient, new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri)))
        {
        }

        [Fact]
        [MfeTest(TestCategory.Integration, ScenarioScope.Spark)]
        public async Task TestAttachedSparkJobE2EWithDefaultIdentity()
        {
            var tags = "byo,default-identity";

            var jobName = TestHelpers.GenerateName();
            var experimentName = "mfe-test-spark";
            var computeId = await ComputeHelpers.GetAttachedSynapseComputeId(Fixture).ConfigureAwait(false);
            var inputData = GetJobInputs();
            var outputData = GetJobOutputs();
            var arguments = GetJobArguments(inputData, outputData);

            var createdJob = await TestCreateSparkJob(jobName, null, experimentName, computeId, sparkResourceConfiguration: null, identityToUse: null, useRegistryForEnvironment: false, inputDataBindings: inputData, outputDataBindings: outputData, arguments: arguments, tags: tags).ConfigureAwait(false);
            var retrievedJob = await TestGetJob(jobName, createdJob).ConfigureAwait(false);

            await WaitForCompletion(jobName);
        }

        private string GetJobArguments(Dictionary<string, JobInput> inputData, Dictionary<string, JobOutput> outputData)
        {
            var arguments = new List<string>();

            foreach (var key in inputData.Keys.EmptyIfNull())
            {
                var inputName = key;
                var expectedValue = GetJobInputValue(inputData[key]).ToLower();
                var argument = $"--check_argument {inputName},{expectedValue},${{{{inputs.{inputName}}}}}";
                arguments.Add(argument);
            }

            foreach (var key in outputData.Keys.EmptyIfNull())
            {
                var outputName = key;
                var expectedValue = GetJobOutputValue(outputData[key]).ToLower();
                var argument = $"--check_argument {outputName},{expectedValue},${{{{outputs.{outputName}}}}}";
                arguments.Add(argument);
            }
            arguments.Add("--fail_on_error");
            return arguments.IsEmpty() ? null : string.Join(" ", arguments);
        }

        private string GetJobInputValue(JobInput input)
        {
            switch (input.JobInputType)
            {
                case JobInputType.literal:
                    return ((LiteralJobInput)input).Value;
                case JobInputType.uri_file:
                    return ((UriFileJobInput)input).Uri;
                case JobInputType.uri_folder:
                    return ((UriFolderJobInput)input).Uri;
                case JobInputType.mltable:
                    return ((MLTableJobInput)input).Uri;
                default:
                    throw new Exception($"Unsupported job input type: {input.JobInputType}");
            }
        }

        private string GetJobOutputValue(JobOutput output)
        {
            switch (output.JobOutputType)
            {
                case JobOutputType.uri_file:
                    return ((UriFileJobOutput)output).Uri;
                case JobOutputType.uri_folder:
                    return ((UriFolderJobOutput)output).Uri;
                case JobOutputType.mltable:
                    return ((MLTableJobOutput)output).Uri;
                default:
                    throw new Exception($"Unsupported job output type: {output.JobOutputType}");
            }
        }

        private Dictionary<string, JobInput> GetJobInputs(InputDeliveryMode mode = InputDeliveryMode.Direct)
        {
            var inputFileUriWasbCredlessDS = new UriFileJobInput() { Mode = mode, Uri = "wasbs://<EMAIL>/data.csv", JobInputType = JobInputType.uri_file };
            var inputFileUriWasbCredDS = new UriFileJobInput() { Mode = mode, Uri = "wasbs://<EMAIL>/data.csv", JobInputType = JobInputType.uri_file };
            var inputFileUriWasbDefaultDS = new UriFileJobInput() { Mode = mode, Uri = "wasbs://<EMAIL>/data.csv", JobInputType = JobInputType.uri_file };
            var inputFileUriAdls = new UriFileJobInput() { Mode = mode, Uri = "adl://rezasadlsgen1.azuredatalakestore.net/sparkdata/input2/data1.csv", JobInputType = JobInputType.uri_file };
            var inputFileUriAbfss = new UriFileJobInput() { Mode = mode, Uri = "abfss://<EMAIL>/data4.csv", JobInputType = JobInputType.uri_file };

            var inputs = new Dictionary<string, JobInput>()
            {
                { "inputFileUriWasbCredlessDS", inputFileUriWasbCredlessDS },
                { "inputFileUriWasbCredDS", inputFileUriWasbCredDS },
                { "inputFileUriWasbDefaultDS", inputFileUriWasbDefaultDS },
                { "inputFileUriAdls", inputFileUriAdls },
                { "inputFileUriAbfss", inputFileUriAbfss }
            };
            return inputs;
        }

        private Dictionary<string, JobOutput> GetJobOutputs(OutputDeliveryMode mode = OutputDeliveryMode.Direct)
        {
            var outputFolderUriAdls = new UriFolderJobOutput() { Mode = mode, Uri = "adl://rezasadlsgen1.azuredatalakestore.net/sparkdata/", JobOutputType = JobOutputType.uri_folder };
            var outputFolderUriAbfss = new UriFolderJobOutput() { Mode = mode, Uri = "abfss://<EMAIL>/", JobOutputType = JobOutputType.uri_folder };
            var outputFolderUriShortDatastore = new UriFolderJobOutput() { Mode = mode, Uri = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourcegroups/int-shared-static-test-resources/workspaces/servicestestworkspace/datastores/workspaceblobstore/paths/", JobOutputType = JobOutputType.uri_folder };
            var outputFolderUriLongDatastore = new UriFolderJobOutput() { Mode = mode, Uri = "azureml://subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourcegroups/int-shared-static-test-resources/workspaces/servicestestworkspace/datastores/workspaceblobstore/paths/", JobOutputType = JobOutputType.uri_folder };

            var inputs = new Dictionary<string, JobOutput>()
            {
                { "outputFolderUriAdls", outputFolderUriAdls },
                { "outputFolderUriAbfss", outputFolderUriAbfss },
                { "outputFolderUriShortDatastore", outputFolderUriShortDatastore },
                { "outputFolderUriLongDatastore", outputFolderUriLongDatastore },
            };
            return inputs;
        }

        #region Sub-test methods under TestSparkJobE2E
        private async Task<Resource<JobBase>> TestCreateSparkJob(string jobName, string environmentNameVersion, string experimentName, string computeId, IdentityConfiguration identityToUse = null, SparkResourceConfiguration sparkResourceConfiguration = null, Dictionary<string, JobInput> inputDataBindings = null, Dictionary<string, JobOutput> outputDataBindings = null, string arguments = null, string tags = null, bool useRegistryForEnvironment = false)
        {
            var request = GetTestSparkJob(Fixture, environmentNameVersion, experimentName, computeId, sparkResourceConfiguration, identityToUse, inputDataBindings: inputDataBindings, outputDataBindings: outputDataBindings, tags: tags, useRegistryForEnvironment: useRegistryForEnvironment, arguments: arguments);

            var createdJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, request).ConfigureAwait(false);

            createdJob.Id.Should().BeEquivalentTo($"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/jobs/{jobName}");

            createdJob.Name.Should().BeEquivalentTo(jobName);
            createdJob.Type.Should().Be("Microsoft.MachineLearningServices/workspaces/jobs");
            createdJob.Properties.Should().BeOfType<SparkJob>();
            var properties = (SparkJob)createdJob.Properties;

            createdJob.SystemData.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, 2.Minutes());
            createdJob.SystemData.CreatedBy.Should().NotBeNullOrWhiteSpace();
            createdJob.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);

            properties.CodeId.Should().BeEquivalentTo(((SparkJob)request.Properties)?.CodeId);
            properties.Resources?.InstanceType.Should().Be(sparkResourceConfiguration?.InstanceType);
            properties.Resources?.RuntimeVersion.Should().Be(sparkResourceConfiguration?.RuntimeVersion);
            properties.Entry.Should().BeOfType(typeof(SparkJobPythonEntry));
            var entry = (SparkJobPythonEntry)properties.Entry;
            entry.File.Should().Be("testpyfile.py");
            var expectedArgsList = new List<string>();
            expectedArgsList.AddRange(inputDataBindings?.Select(pair => $"--check_argument {pair.Key},{((AssetJobInput)pair.Value).Uri.ToLower()},${{{{inputs.{pair.Key}}}}}"));
            expectedArgsList.AddRange(outputDataBindings?.Select(pair => $"--check_argument {pair.Key},{((AssetJobOutput)pair.Value).Uri.ToLower()},${{{{outputs.{pair.Key}}}}}"));
            expectedArgsList.Add("--fail_on_error");
            properties.Args.Should().Be(string.Join(" ", expectedArgsList));

            properties.EnvironmentId.Should().BeEquivalentTo(((SparkJob)request.Properties).EnvironmentId);
            properties.ExperimentName.Should().Be(experimentName);
            properties.Inputs.Should().BeEquivalentTo(((SparkJob)request.Properties).Inputs, options => options.UsingCaseInsensitiveProperties());
            properties.JobType.Should().Be(JobType.Spark);
            properties.Properties.Should().Contain("property-name", "property-value");
            properties.Tags.Should().Contain("tag-name", "tag-value");
            properties.Identity.Should().BeEquivalentTo(((SparkJob)request.Properties).Identity);
            properties.Description.Should().BeEquivalentTo(request.Properties.Description);
            properties.Outputs.Should().ContainKey("default");
            if (!string.IsNullOrWhiteSpace(tags))
            {
                tags.Split(",").ForEach(t => properties.Tags.Should().Contain(t, "yes"));
            }
            return createdJob;
        }
        #endregion

        #region Shared sub-test methods

        // TODO: remove after Richard's PR is merged https://msdata.visualstudio.com/Vienna/_git/vienna/pullrequest/848309
        /*
        private bool IsHoboSpark(Resource<JobBase> job)
        {
            return string.IsNullOrWhiteSpace(job.Properties.ComputeId);
        }
        */

        private async Task WaitForCompletion(string jobName, int timeout = 600)
        {
            RunDetailsDto details = null;
            for (int i = 0; i < timeout / 10; i++)
            {
                details = await RunHistoryClient.GetRunDetails(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                await Task.Delay(TimeSpan.FromSeconds(10)).ConfigureAwait(false);
                if (details.Status == RunStatus.CancelRequested.ToString() || details.Status == RunStatus.Completed.ToString() || details.Status == RunStatus.Failed.ToString() || details.Status == RunStatus.Canceled.ToString())
                {
                    break;
                }
            }

            details.Status.Should().Be(RunStatus.Completed.ToString());
        }

        private Resource<JobBase> GetTestSparkJob(TestFixtureManagementFrontEnd fixture, string environmentNameVersion, string experimentName, string computeId, SparkResourceConfiguration sparkResourceConfiguration = null, IdentityConfiguration identityToUse = null, Dictionary<string, JobInput> inputDataBindings = null, Dictionary<string, JobOutput> outputDataBindings = null, bool archived = false, string tags = null, bool useRegistryForEnvironment = false, string arguments = null)
        {
            string environmentId = null;
            var workspaceContext = fixture.GetTestWorkspaceContext2();

            if (!string.IsNullOrEmpty(environmentNameVersion))
            {
                if (useRegistryForEnvironment)
                {
                    environmentId = new AssetId("centraluseuap", workspaceContext.WorkspaceId.ToString(), AssetType.Environments, environmentNameVersion, "27", AssetContainerTypeOld.Workspace, AssetResourceType.Workspace).ToString();
                }
                else
                {
                    environmentId = MfeResourceArmScope.ToString(workspaceContext, MfeConstants.ArmTypeEnvironmentContainer, environmentNameVersion);
                }
            }

            var (codeId, entry) = GetCodeIdAndEntry();

            var request = new Resource<JobBase>
            {
                Properties = new SparkJob
                {
                    Identity = identityToUse,
                    Entry = entry,
                    Inputs = inputDataBindings ?? new Dictionary<string, JobInput>(),
                    Outputs = outputDataBindings ?? new Dictionary<string, JobOutput>(),
                    EnvironmentId = environmentId,
                    ExperimentName = experimentName,
                    Properties = new Dictionary<string, string>
                    {
                        { "property-name", "property-value" },
                    },
                    Tags = new Dictionary<string, string>
                    {
                        { "tag-name", "tag-value" },
                    },
                    Description = Guid.NewGuid().ToString(),
                    ComputeId = computeId,
                    Resources = sparkResourceConfiguration,
                    IsArchived = archived,
                    CodeId = codeId,
                    Conf = new Dictionary<string, string>()
                    {
                        { "spark.driver.memory", "1g" },
                        { "spark.driver.cores", "2" },
                        { "spark.executor.memory", "1g" },
                        { "spark.executor.cores", "1" },
                        { "spark.executor.instances", "1" },
                        { "spark.yarn.dist.jars", "https://foobaradrama2.blob.core.windows.net/versioned/v0.0.22/hadoop-azureml-fs.jar" }
                    },
                    Args = arguments,
                },
            };
            if (!string.IsNullOrEmpty(tags))
            {
                tags.Split(",").ForEach(t => request.Properties.Tags.Add(t, "yes"));
            }

            return request;
        }

        private (string, SparkJobEntry) GetCodeIdAndEntry()
        {
            /*
             * # Content of check_arguments.py
             *
             * import argparse
             * parser = argparse.ArgumentParser()
             * parser.add_argument("--check_argument", action='append', default=[])
             * parser.add_argument("--fail_on_error", action='store_true', default=False)
             * args = parser.parse_args()
             * errors = []
             * for argument_tuple in args.check_argument:
             *    name, expected_value, actual_value = argument_tuple.split(',')
             *      if expected_value != actual_value:
             *          errors.append((name, expected_value, actual_value))
             *  if len(errors) > 0:
             *      for (name, expected_value, actual_value) in errors:
             *          print(f"{name}: expected '{expected_value}' but received '{actual_value}'")
             *      if args.fail_on_error:
             *          exit(1)
             *  else:
             *      print("Found no errors.")
             */
            var entry = new SparkJobPythonEntry() { File = "testpyfile.py" };
            // var codeId = "/subscriptions/c0afea91-faba-4d71-bcb6-b08134f69982/resourceGroups/shared_static_test_resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/codes/abf9256c-6ad2-445d-b870-45c1efc0c296/versions/1";
            var codeId = "/subscriptions/4bf6b28a-452b-4af4-8080-8a196ee0ca4b/resourceGroups/int-shared-static-test-resources/providers/Microsoft.MachineLearningServices/workspaces/servicestestworkspace/codes/sparktest/versions/4";
            return (codeId, entry);
        }

        private void CheckJobEquivalency(Resource<JobBase> expected, Resource<JobBase> actual)
        {
            actual.Should().BeEquivalentTo(expected, options => options.Excluding(x => x.Properties).UsingCaseInsensitiveProperties());
            actual.Properties.Should().BeOfType(expected.Properties.GetType());

            switch (actual.Properties)
            {
                case CommandJob properties:
                    properties.ComputeId.Should().BeEquivalentTo(((CommandJob)expected.Properties).ComputeId);
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).Excluding(x => x.ComputeId).UsingCaseInsensitiveProperties());
                    break;

                case SweepJob properties:
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).UsingCaseInsensitiveProperties());
                    break;

                case PipelineJob properties:
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).UsingCaseInsensitiveProperties());
                    break;
                case AutoMLJob properties:
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Tags).Excluding(x => x.Services).Excluding(x => x.Status).Excluding(x => x.DisplayName).UsingCaseInsensitiveProperties());
                    break;

                case SparkJob properties:
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Tags).Excluding(x => x.Services).Excluding(x => x.Status).Excluding(x => x.DisplayName).UsingCaseInsensitiveProperties());
                    break;

                default:
                    throw new NotImplementedException();
            }
        }

        private async Task<Resource<JobBase>> TestGetJob(string jobName, Resource<JobBase> expectedJob)
        {
            var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            CheckJobEquivalency(expectedJob, retrievedJob);

            return retrievedJob;
        }
        #endregion
    }

    public abstract class SparkJobControllerTests<TFixture>
    where TFixture : TestFixtureManagementFrontEnd
    {
        public SparkJobControllerTests(TFixture fixture, IArtifactControllerWorkspaceV2Routes artifactController)
        {
            Fixture = fixture;

            Controller = Fixture.ManagementFrontEndClient.GetController<IJobController>();

            CodeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>();

            ArtifactController = artifactController;

            RunHistoryClient = ServiceInvoker.Create<IExperimentAgnosticRunController>(fixture.HttpClient, new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));

            ModelVersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();

            DataVersionController = Fixture.ManagementFrontEndClient.GetController<IDataVersionController>();

            DataContainerController = Fixture.ManagementFrontEndClient.GetController<IDataContainerController>();

            DataStoreController = ServiceInvoker.Create<IDataStoreController>(
                fixture.HttpClient,
                new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));
        }

        public IJobController Controller { get; }

        public TFixture Fixture { get; }

        public IArtifactControllerWorkspaceV2Routes ArtifactController { get; }

        public ICodeVersionController CodeVersionController { get; }

        public IExperimentAgnosticRunController RunHistoryClient { get; }

        protected IModelVersionController ModelVersionController { get; }

        protected IDataVersionController DataVersionController { get; }

        protected IDataContainerController DataContainerController { get; }

        protected IDataStoreController DataStoreController { get; }
    }
}
