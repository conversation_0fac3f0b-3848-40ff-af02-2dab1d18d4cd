﻿// <copyright file="EndpointHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20221001Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20221001Preview.Helpers
{
    public static class EndpointHelpers
    {
        private const string ScoringFileContents = @"
def init():
    pass

def run(raw_data):
    return raw_data
";

        private static readonly KeyValuePair<string, string> E2eTestIdentifier = new KeyValuePair<string, string>("usedforintegrationtest", "true");
        private static readonly KeyValuePair<string, string> DpCleanupTag = new KeyValuePair<string, string>("controlplane_delete_after_seconds", "7200");

        public static TrackedResource<T1> GenerateTestEndpoint<T1>(
            string name,
            T1 endpointProperties,
            string type,
            Dictionary<string, string> additionalProperties = null,
            CaseInsensitiveDictionary tags = null)
        {
            additionalProperties ??= new Dictionary<string, string>();
            tags ??= new CaseInsensitiveDictionary();
            additionalProperties.TryAdd(E2eTestIdentifier.Key, E2eTestIdentifier.Value);
            tags.TryAdd(DpCleanupTag.Key, DpCleanupTag.Value);

            var testEndpoint = new TrackedResource<T1>
            {
                Location = "centraluseuap",
                Identity = new ManagedServiceIdentity(),
                Name = name,
                Type = type,
                Properties = endpointProperties,
                Tags = tags
            };

            return testEndpoint;
        }

        public static string GenerateRandomEndpointName()
        {
            var chars = "abcdefghijklmnopqrstuvwxyz";
            var stringChars = new char[24];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            return new string(stringChars);
        }

        public static PartialTrackedResource<T1> GenerateTestUpdatableEndpoint<T1>(
            T1 endpointProperties,
            Dictionary<string, string> additionalProperties = null,
            CaseInsensitiveDictionary tags = null)
        {
            additionalProperties ??= new Dictionary<string, string>();
            tags ??= new CaseInsensitiveDictionary();
            additionalProperties.TryAdd(E2eTestIdentifier.Key, E2eTestIdentifier.Value);
            tags.TryAdd(DpCleanupTag.Key, DpCleanupTag.Value);

            var testEndpoint = new PartialTrackedResource<T1>
            {
                Location = "centraluseuap",
                Identity = new PartialManagedServiceIdentity(),
                Properties = endpointProperties,
                Tags = tags
            };

            return testEndpoint;
        }

        public static string GetModelId(TestFixtureManagementFrontEnd fixture, bool isAnonymous = false, bool ncd = false, string modelName = "sklearn", string modelVersion = "1")
        {
            modelName = ncd ? (isAnonymous ? "batchNCD-mlflow-anonymous" : "batchNCD-mlflow") : (isAnonymous ? "sklearn-anonymous" : modelName);
            modelVersion = ncd ? (isAnonymous ? "2" : "1") : (isAnonymous ? "2" : modelVersion);

            var workspace = fixture.GetTestWorkspaceContext2();
            var modelId = MfeResourceVersionArmScope.ToString(workspace, MfeConstants.ArmTypeModelContainer, modelName, modelVersion);
            return modelId;
        }

        public static string GetEnvironmentId(TestFixtureManagementFrontEnd fixture, string environmentName, string environmentVersion = null)
        {
            var workspace = fixture.GetTestWorkspaceContext2();

            if (string.IsNullOrEmpty(environmentVersion))
            {
                return MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeEnvironmentContainer, environmentName);
            }

            return MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeEnvironmentContainer, environmentName, MfeConstants.ArmTypeEnvironmentVersion, environmentVersion);
        }

        public static async Task<Resource<CodeVersion>> UploadAndRegisterScoringFile(
            TestFixtureManagementFrontEnd fixture,
            ICodeVersionController controller,
            IList<string> codeVersionTracker)
        {
            FakeDatastoreV1Setup.AddToDatastores(MfeTestingConstants.DatastoreName, MfeTestingConstants.StorageContainer);

            var storageConnectionDto = await fixture.GetStorageConnection().ConfigureAwait(false);
            var (filePath, containerUri) = await BlobHelpers.UploadBlob(storageConnectionDto.ConnectionString, MfeTestingConstants.StorageContainer, "scoring/score.py", ScoringFileContents).ConfigureAwait(false);

            var subscriptionId = fixture.SubscriptionId;
            var resourceGroupName = fixture.ResourceGroupName;
            var workspaceName = fixture.WorkspaceName;

            var request = new Resource<CodeVersion>()
            {
                Properties = new CodeVersion()
                {
                    CodeUri = containerUri + "/scoring"
                }
            };

            var resource = await controller.CreateOrUpdate(
                subscriptionId,
                resourceGroupName,
                workspaceName,
                Guid.NewGuid().ToString(),
                "1",
                request).ConfigureAwait(false);

            codeVersionTracker.Add(resource.Id);
            return resource;
        }

        public static async Task CleanupCodeAssets(
            TestFixtureManagementFrontEnd fixture,
            ICodeVersionController controller,
            IList<string> codeVersionTracker)
        {
            foreach (var id in codeVersionTracker)
            {
                var (name, version) = MfeResourceArmScopeHelper.ExtractInternalIdFromArmId(id, fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeCodeContainer);

                await controller.Delete(
                    fixture.SubscriptionId,
                    fixture.ResourceGroupName,
                    fixture.WorkspaceName,
                    name,
                    version).ConfigureAwait(false);
            }
        }
    }
}
