﻿// <copyright file="PipelineJobControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Newtonsoft.Json.Linq;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20240101Preview
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "VSTHRD002:Avoid problematic synchronous waits", Justification = "Need to synchronously get result for some cases including Dispose and PollStatus. ")]
    public class E2EPipelineJobControllerTests : PipelineJobControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>, IDisposable
    {
        private string _normalJobName = "generateNameEachTime";
        private string _failedJobName = "generateNameEachTime";

        public E2EPipelineJobControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
                : base(fixture)
        {
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        [MfeTest(TestCategory.Integration, Api.ScenarioScope.Pipeline)]
        public async Task TestAsyncCreatePipelineJobE2E()
        {
            _normalJobName = TestHelpers.GenerateName();
            var computeId = await ComputeHelpers.GetComputeId(Fixture).ConfigureAwait(false);

            // normal case
            var create = await TestAsyncCreatePipelineJob(_normalJobName, computeId).ConfigureAwait(false);

            var resource = await TestGetPipelineJob(_normalJobName).ConfigureAwait(false);
            var pipelineJob = resource.Properties as PipelineJob;
            Assert.Equal(JobType.Pipeline, pipelineJob.JobType);

            resource = await GetRunUntilTerminal(_normalJobName).ConfigureAwait(false);
            Assert.Equal(JobStatus.Completed, resource.Properties.Status);

            await TestUpdateJobPropTagsDescription(_normalJobName, resource).ConfigureAwait(false);

            // Failed case which is submission error
            _failedJobName = TestHelpers.GenerateName();

            var unvalidComputeId = "/subscriptions/fake_sub/resourceGroups/fake_rg/providers/Microsoft.MachineLearningServices/workspaces/fake_ws/computes/computeinstance";
            create = await TestAsyncCreatePipelineJob(_failedJobName, unvalidComputeId).ConfigureAwait(false);
            resource = await TestGetPipelineJob(_failedJobName).ConfigureAwait(false);

            pipelineJob = resource.Properties as PipelineJob;
            Assert.Equal(JobType.Pipeline, pipelineJob.JobType);

            resource = await GetRunUntilTerminal(_failedJobName).ConfigureAwait(false);
            Assert.Equal(JobStatus.Failed, resource.Properties.Status);
        }

        [Fact]
        [MfeTest(TestCategory.Integration, Api.ScenarioScope.Pipeline)]
        public async Task TestAsyncCreateAndUpdatePipelineJobE2E()
        {
            _normalJobName = TestHelpers.GenerateName();
            var computeId = await ComputeHelpers.GetComputeId(Fixture).ConfigureAwait(false);

            // normal case
            var create = await TestAsyncCreatePipelineJob(_normalJobName, computeId).ConfigureAwait(false);

            var resource = await TestGetPipelineJob(_normalJobName).ConfigureAwait(false);
            var pipelineJob = resource.Properties as PipelineJob;
            Assert.Equal(JobType.Pipeline, pipelineJob.JobType);

            resource = await GetRunUntilTerminal(_normalJobName).ConfigureAwait(false);
            Assert.Equal(JobStatus.Completed, resource.Properties.Status);

            await TestReplaceJobPropTags(_normalJobName, resource).ConfigureAwait(false);

            // Failed case which is submission error
            _failedJobName = TestHelpers.GenerateName();

            var unvalidComputeId = "/subscriptions/fake_sub/resourceGroups/fake_rg/providers/Microsoft.MachineLearningServices/workspaces/fake_ws/computes/computeinstance";
            create = await TestAsyncCreatePipelineJob(_failedJobName, unvalidComputeId).ConfigureAwait(false);
            resource = await TestGetPipelineJob(_failedJobName).ConfigureAwait(false);

            pipelineJob = resource.Properties as PipelineJob;
            Assert.Equal(JobType.Pipeline, pipelineJob.JobType);

            resource = await GetRunUntilTerminal(_failedJobName).ConfigureAwait(false);
            Assert.Equal(JobStatus.Failed, resource.Properties.Status);
        }

        private async Task<Resource<JobBase>> TestUpdateJobPropTagsDescription(string jobName, Resource<JobBase> initialJob)
        {
            initialJob.Properties.Description = "UpdateJob";
            initialJob.Properties.Tags = initialJob.Properties.Tags ?? new Dictionary<string, string>();
            initialJob.Properties.Tags.Add("tagsUpdateKey", "tagsUpdateValue");
            initialJob.Properties.Properties = initialJob.Properties.Properties ?? new Dictionary<string, string>();
            initialJob.Properties.Properties.Add("propertiesUpdateKey", "propertiesUpdateValue");

            Resource<JobBase> updatedJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, initialJob).ConfigureAwait(false);
            updatedJob.Properties.Tags.Should().Contain(initialJob.Properties.Tags);
            updatedJob.Properties.Description.Should().Be(initialJob.Properties.Description);
            updatedJob.Properties.Properties.Should().Contain(initialJob.Properties.Properties);
            return updatedJob;
        }

        private async Task<Resource<JobBase>> TestReplaceJobPropTags(string jobName, Resource<JobBase> initialJob)
        {
            initialJob.Properties.Description = "UpdateJob";
            initialJob.Properties.Tags = new Dictionary<string, string>();
            initialJob.Properties.Tags.Add("tagsUpdateKey", "tagsUpdateValue");

            Resource<JobBase> updatedJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, initialJob).ConfigureAwait(false);
            updatedJob.Properties.Tags.Should().Contain(initialJob.Properties.Tags);
            updatedJob.Properties.Tags.Count.Should().Be(1);
            updatedJob.Properties.Description.Should().Be(initialJob.Properties.Description);
            return updatedJob;
        }

        protected virtual void Dispose(bool v)
        {
            try
            {
                TestDeletePipelineJob(_normalJobName).GetAwaiter().GetResult();
                TestDeletePipelineJob(_failedJobName).GetAwaiter().GetResult();
            }
            catch (Exception)
            {
                // Only need to delete if get result exist;
            }
        }

        private async Task<Resource<JobBase>> TestGetPipelineJob(string jobId)
        {
            var retrieveJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobId);
            return retrieveJob;
        }

        private async Task<Resource<JobBase>> TestAsyncCreatePipelineJob(string jobId, string computeId)
        {
            var pipelineJob = BuildPipelineJob(computeId);

            var request = new Resource<JobBase>
            {
                Properties = pipelineJob
            };
            var createdJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobId, request).ConfigureAwait(false);
            return createdJob;
        }

        private string GetComponentId()
        {
            var str = "/subscriptions/" + Fixture.SubscriptionId + "/resourceGroups/" + Fixture.ResourceGroupName + "/providers/Microsoft.MachineLearningServices" + "/workspaces/" + Fixture.WorkspaceName + "/components/Hello_Python_World/versions/2";
            return str;
        }

        private PipelineJob BuildPipelineJob(string computeId)
        {
            var settings = new InternalSharedServices.Contracts.PipelineJobSettings
            {
                ContinueRunOnStepFailure = false,
                Datastore = new MfeResourceArmScope(Fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeDatastore, "workspaceblobstore").ToString(),
                DefaultDatastoreName = "workspaceblobstore"
            };
            var componentArmId = GetComponentId();
            var experimentName = "MFEPipelineJobAsyncCreate";
            var step1 = JObject.FromObject(new
            {
                computeId,
                componentId = componentArmId,
                inputs = new
                {
                    int_a = new { type = "Literal", value = "52" },
                    int_b = new { type = "Literal", value = "78" }
                },
                outputs = new
                {
                    out_sum = new { type = "Dataset", mode = "ReadWriteMount" },
                    out_prod = new { type = "Dataset", mode = "ReadWriteMount" }
                }
            });

            var pipelineJob = new PipelineJob()
            {
                JobType = JobType.Pipeline,
                ExperimentName = experimentName,
                ComputeId = computeId,
                Settings = JObject.FromObject(settings),
                Inputs = new Dictionary<string, JobInput>
                {
                    {
                        "sample_input_data",
                        new MLTableJobInput()
                        {
                            JobInputType = JobInputType.uri_file,
                            Mode = InputDeliveryMode.ReadOnlyMount,
                            Description = null,
                            Uri = "https://dprepdata.blob.core.windows.net/demo/Titanic.csv"
                        }
                    },
                    {
                        "sample_input_string",
                        new LiteralJobInput()
                        {
                            JobInputType = JobInputType.literal,
                            Value = "Fake_Hello_Python_World",
                            Description = null,
                        }
                    }
                },
                Jobs = new Dictionary<string, JObject>
                {
                    { "step1", step1 }
                },
                Outputs = new Dictionary<string, JobOutput>
                 {
                    {
                        "sample_output_data",
                        new MLTableJobOutput()
                        {
                            JobOutputType = JobOutputType.mltable,
                            Mode = OutputDeliveryMode.ReadWriteMount,
                            Description = null
                        }
                    }
                },
                IsArchived = true,
                Tags = new Dictionary<string, string>
                {
                    { "initialJobTagKey", "initialJobTag" }
                }
            };
            return pipelineJob;
        }

        private async Task<Resource<JobBase>> GetRunUntilTerminal(string jobId)
        {
            var resource = await TestGetPipelineJob(jobId).ConfigureAwait(false);
            while (!IsTerminal(resource.Properties.Status))
            {
                Thread.Sleep(1000);
                resource = await TestGetPipelineJob(jobId).ConfigureAwait(false);
            }
            return resource;
        }

        private bool IsTerminal(JobStatus status)
        {
            if (status == JobStatus.Completed ||
                status == JobStatus.Canceled ||
                status == JobStatus.Failed)
            {
                return true;
            }
            return false;
        }

        private async Task<HttpResponseMessage> TestDeletePipelineJob(string jobId)
        {
            var exist = TestGetPipelineJob(jobId).GetAwaiter().GetResult();
            var result = await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobId).ConfigureAwait(false);
            return result;
        }
    }

    public class PipelineJobControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public PipelineJobControllerTests(TFixture fixture)
        {
            Fixture = fixture;
            Controller = Fixture.ManagementFrontEndClient.GetController<IJobController>();
        }

        public IJobController Controller { get; }

        public TFixture Fixture { get; }
    }
}
