﻿// <copyright file="JobControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using FluentAssertions.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Artifact.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.ArmCommon;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Newtonsoft.Json.Linq;
using Xunit;
using FineTuningJob = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.FineTuningJob;
using FineTuningTaskType = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.FineTuningTaskType;
using JobStatus = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.JobStatus;
using ModelProvider = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20240101Preview.ModelProvider;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20240101Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.FineTuning)]
    public class FineTuningE2EJobControllerTests : JobControllerTests<TestFixtureFineTuningService>, IStaticFixture<TestFixtureFineTuningService>
    {
        private const int _maxListCount = 20;
        public FineTuningE2EJobControllerTests(StaticFixture<TestFixtureFineTuningService> fixture)
            : base(fixture, ServiceInvoker.Create<IArtifactControllerWorkspaceV2Routes>(fixture.Instance.HttpClient, new Uri(fixture.Instance.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri)))
        {
        }

        #region FineTuningJob E2E
        [Theory]
        [ClassData(typeof(FineTuningJobData))]
        [MfeTest(TestCategory.Integration, ScenarioScope.FineTuning)]
        public async Task TestFineTuningJobE2E(Resource<JobBase> request, string finetuningJobName)
        {
            var createdFineTuningJob = await TestCreateFineTuningJob(finetuningJobName, request).ConfigureAwait(false);

            var retrievedJob = await TestGetJob(finetuningJobName, createdFineTuningJob).ConfigureAwait(false);

            // Sometimes fetching immediately after as part of list doesnt show up due to eventual consistency by index.
            await Task.Delay(TimeSpan.FromSeconds(30)).ConfigureAwait(false);
            // Test filter by job type
            await TestListJobsFilterType(finetuningJobName, retrievedJob, JobType.FineTuning, true).ConfigureAwait(false);

            // Test filter by tag key
            await TestListJobsFilter(finetuningJobName, retrievedJob, null, tag: finetuningJobName, false).ConfigureAwait(false);
            await TestListJobsFilter(finetuningJobName, retrievedJob, null, "fake-tag", true).ConfigureAwait(false);

            // TODO: Commenting cancel as due t
            // Job already fails at data import leve
            // Microsoft.MachineLearning.EnvironmentManagement.Contracts.Errors.Brownouts.EolImageBrownoutsException: Specified image azureml/curated/acft-hf-nlp-gpu
            // So commenting cancel as it is not able to cancel the job
            // once the component is fixed, we can uncomment this.
            // await TestCancelJob(finetuningJobName).ConfigureAwait(false);

            await TestDeleteJob(finetuningJobName).ConfigureAwait(false);
        }

        #endregion     

        #region Static utility methods

        private static void CheckJobEquivalency(Resource<JobBase> expected, Resource<JobBase> actual)
        {
            actual.Should().BeEquivalentTo(expected, options => options.Excluding(x => x.Properties).UsingCaseInsensitiveProperties());
            actual.Properties.Should().BeOfType(expected.Properties.GetType());

            switch (actual.Properties)
            {
                case FineTuningJob properties:
                    properties.ComputeId.Should().BeEquivalentTo(((FineTuningJob)expected.Properties).ComputeId);
                    properties.Should().BeEquivalentTo(expected.Properties, options => options.Excluding(x => x.Properties).Excluding(x => x.Status).Excluding(x => x.Tags).Excluding(x => x.Services).Excluding(x => x.ComputeId).UsingCaseInsensitiveProperties());
                    break;

                default:
                    throw new NotImplementedException();
            }
        }
        #endregion

        #region Sub-test methods under FineTuningJobE2E

        internal class FineTuningJobData : TheoryData<Resource<JobBase>, string>
        {
            public FineTuningJobData()
            {
                var finetuningJobName = $"llama-finetuning-job-{Guid.NewGuid().ToString()[..4]}-TextCompletion";
                var llamaFineTuningJob = GetFineTuningJob(ModelProvider.Custom, FineTuningTaskType.TextCompletion, "azureml://registries/azureml-meta/models/Llama-2-7b/versions/22", finetuningJobName);
                // This is to indicate we need to hit Execution service finetuning API.
                // Routing in the ExecutionFineTuningClient is based on region, so we need to set this to hit execution FT API.
                llamaFineTuningJob.Properties.Properties.Add("__serviceTarget__", "execution");
                Add(llamaFineTuningJob, finetuningJobName);

                // Task type should change to ChatCompletion once finetuning service supports it.
                finetuningJobName = $"mistral-finetuning-job-{Guid.NewGuid().ToString()[..4]}-TextCompletion";
                var mistralFineTuningJob = GetFineTuningJob(ModelProvider.Custom, FineTuningTaskType.TextCompletion, "azureml://registries/azureml-mistral/models/mistralai-Mistral-7B-v01/versions/1", finetuningJobName);
                // This is to indicate we need to hit finetuning service and not Execution service finetuning API.
                // Routing in the ExecutionFineTuningClient is based on region, so we need to set this to hit the FTS service.
                mistralFineTuningJob.Properties.Properties.Add("__serviceTarget__", "finetuning");
                Add(mistralFineTuningJob, finetuningJobName);
            }
        }

        private static Resource<JobBase> GetFineTuningJob(ModelProvider modelProvider, FineTuningTaskType taskType, string modelAssetId, string jobId)
        {
            var ftJob = new FineTuningJob();
            switch (modelProvider)
            {
                case ModelProvider.AzureOpenAI:
                    var aoaiFineTuning = new AzureOpenAiFineTuning();
                    aoaiFineTuning.HyperParameters.LearningRateMultiplier = 1.0;
                    aoaiFineTuning.HyperParameters.BatchSize = 32;
                    aoaiFineTuning.HyperParameters.NEpochs = 10;
                    ftJob.FineTuningDetails = aoaiFineTuning;

                    break;
                case ModelProvider.Custom:
                    var fineTuningDetails = new CustomModelFineTuning();
                    fineTuningDetails.HyperParameters = new Dictionary<string, string>
                    {
                        { "learning_rate", "0.01" },
                        { "batch_size", "32" },
                        { "n_epochs", "10" },
                    };
                    ftJob.FineTuningDetails = fineTuningDetails;
                    break;
            }

            ftJob.ExperimentName = Guid.NewGuid().ToString();
            ftJob.Description = Guid.NewGuid().ToString();
            ftJob.DisplayName = Guid.NewGuid().ToString();
            ftJob.FineTuningDetails.TaskType = taskType;
            ftJob.FineTuningDetails.Model = new MLFlowModelJobInput()
            {
                Uri = modelAssetId,
                Description = "model description",
                Mode = InputDeliveryMode.ReadOnlyMount,
                JobInputType = JobInputType.mlflow_model,
            };
            ftJob.FineTuningDetails.TrainingData = new UriFileJobInput()
            {
                Uri = "azureml://datastores/workspaceblobstore/paths/UI/2023-06-06_175927_UTC/small_train.jsonl",
                Description = "training data description",
                Mode = InputDeliveryMode.ReadOnlyMount,
                JobInputType = JobInputType.uri_file,
            };
            ftJob.Outputs = new Dictionary<string, JobOutput>
            {
                { "registered_model",   new MLFlowModelJobOutput
                                        {
                                            Description = "output description",
                                            JobOutputType = JobOutputType.mlflow_model,
                                            AssetName = Guid.NewGuid().ToString(),
                                        }
                }
            };

            var request = new Resource<JobBase>
            {
                Properties = ftJob
            };

            SetJobProperties(request, jobId);
            return request;
        }

        private async Task<Resource<JobBase>> TestCreateFineTuningJob(string jobName, Resource<JobBase> request)
        {
            var createdJob = await Controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName, request).ConfigureAwait(false);

            createdJob.Id.Should().BeEquivalentTo($"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/jobs/{jobName}");
            createdJob.Name.Should().BeEquivalentTo(jobName);
            createdJob.Type.Should().Be("Microsoft.MachineLearningServices/workspaces/jobs");
            createdJob.Properties.Should().BeOfType<FineTuningJob>();
            createdJob.SystemData.CreatedAt.Should().BeCloseTo(DateTimeOffset.UtcNow, 2.Minutes());
            // TODO: CreatedBy/CreatedByType - This is coming null, need to figure out why.
            // createdJob.SystemData.CreatedBy.Should().NotBeNullOrWhiteSpace();
            // createdJob.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);

            return createdJob;
        }
        #endregion

        #region Shared sub-test methods
        /* Uncomment this method once the cancel job is fixed (finetuning).
        private async Task TestCancelJob(string jobName)
        {
            var allowedStatuses = new HashSet<JobStatus>() { JobStatus.Canceled, JobStatus.CancelRequested };
            var failedStatuses = new HashSet<JobStatus>() { JobStatus.Failed, JobStatus.NotResponding };
            await Controller.Cancel(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            var result = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            var startPollingTime = DateTime.Now;
            // Polling for 1min at most
            while (result.Properties.Status == JobStatus.Running
                && (DateTime.Now - startPollingTime) < TimeSpan.FromMinutes(1))
            {
                Thread.Sleep(10000); // Sleep 10s every time
                result = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            }

            switch (result.Properties)
            {
                case FineTuningJob properties:
                    properties.Status.Should().Match(m => m.HasValue && allowedStatuses.Contains(m.Value));
                    break;
                default:
                    throw new NotImplementedException();
            }
        }
        */

        private async Task TestDeleteJob(string jobName, bool isTrigger = false)
        {
            var status = await GetJobStatus(jobName).ConfigureAwait(false);
            var isTerminal = Enum.Parse<RunStatus>(status.ToString()).IsTerminal();

            var response = await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
            response.StatusCode.Should().Be(HttpStatusCode.Accepted);
            response.Headers.Location.Should().NotBeNull();

            // Do not poll - just check if the call goes through
            var operationLocation = response.Headers.Location;
            var operationResponse = await Fixture.HttpClient.GetAsync(operationLocation).ConfigureAwait(false);

            // If the run has not terminated yet, then the operation may result in a 409 Conflict.
            if (isTerminal)
            {
                operationResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Accepted, HttpStatusCode.OK);
            }
            else
            {
                operationResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Conflict, HttpStatusCode.Accepted, HttpStatusCode.OK);
            }

            try
            {
                await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                // Trigger job is not allowed to cancel.
                if (!isTrigger)
                {
                    await CancelJobWithRetry(jobName).ConfigureAwait(false);
                }
            }
            catch (ServiceInvocationException e)
            {
                e.Response.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Conflict);
            }
        }

        private async Task CancelJobWithRetry(string jobName, int retryCount = 3)
        {
            for (int i = 0; i <= retryCount; i++)
            {
                try
                {
                    await Controller.Cancel(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                }
                catch (ServiceInvocationException e)
                {
                    // in terminal status, it can't be canceled. Waiting the job is deleted.
                    var shouldRetry = e.Response.StatusCode == HttpStatusCode.BadRequest;
                    if (i < retryCount && shouldRetry)
                    {
                        // try to delete again
                        await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);
                        Thread.Sleep(60000);
                    }
                    else
                    {
                        throw;
                    }
                }
            }
        }

        private async Task<Resource<JobBase>> TestGetJob(string jobName, Resource<JobBase> expectedJob)
        {
            var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            CheckJobEquivalency(expectedJob, retrievedJob);

            return retrievedJob;
        }

        private async Task TestListJobsFilter(string jobName, Resource<JobBase> expectedJob, string jobtype, string tag, bool noJobsExpected)
        {
            var allJobs = await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, jobtype, tag), _maxListCount).ToListAsync();

            if (noJobsExpected)
            {
                allJobs.Should().HaveCount(0);
            }
            else
            {
                allJobs.Should().NotBeEmpty();
            }

            var listedJob = allJobs.SingleOrDefault(x => x.Name == jobName);
            if (listedJob != null)
            {
                CheckJobEquivalency(expectedJob, listedJob);
            }
        }

        private async Task TestListJobsFilterType(string jobName, Resource<JobBase> expectedJob, JobType jobtype, bool jobExpected)
        {
            var allJobs = await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, jobtype.ToString(), null), _maxListCount).ToListAsync();

            if (!jobExpected)
            {
                allJobs.Should().NotBeEmpty();
            }

            foreach (Resource<JobBase> job in allJobs?.EmptyIfNull())
            {
                job.Properties.JobType.Should().Be(jobtype);
            }

            var listedJob = allJobs.SingleOrDefault(x => x.Name == jobName);
            if (listedJob != null)
            {
                CheckJobEquivalency(expectedJob, listedJob);
            }
        }

        private static Resource<JobBase> SetJobProperties(Resource<JobBase> job, string runId)
        {
            job.Properties.Properties = new Dictionary<string, string>();
            job.Properties.Tags = new Dictionary<string, string>
            {
                { runId, "new-tag-value" },
            };
            job.Properties.Description = Guid.NewGuid().ToString();
            return job;
        }

        private async Task<JobStatus> GetJobStatus(string jobName)
        {
            var retrievedJob = await Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, jobName).ConfigureAwait(false);

            switch (retrievedJob.Properties)
            {
                case FineTuningJob properties:
                    return properties.Status;
                default:
                    throw new NotImplementedException();
            }
        }

        #endregion
    }

    public abstract class JobControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public JobControllerTests(TFixture fixture, IArtifactControllerWorkspaceV2Routes artifactController)
        {
            Fixture = fixture;

            Controller = Fixture.ManagementFrontEndClient.GetController<IJobController>(
                beforeRequest: (request) =>
                {
                    request.Headers.Add("x-azureml-token", "fake_aml_token");
                    return Task.CompletedTask;
                });

            CodeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>();

            ArtifactController = artifactController;

            RunHistoryClient = ServiceInvoker.Create<IExperimentAgnosticRunController>(fixture.HttpClient, new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));

            ModelVersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>();

            DataVersionController = Fixture.ManagementFrontEndClient.GetController<IDataVersionController>();

            DataContainerController = Fixture.ManagementFrontEndClient.GetController<IDataContainerController>();

            DataStoreController = ServiceInvoker.Create<IDataStoreController>(
                fixture.HttpClient,
                new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));
        }

        public IJobController Controller { get; }

        public TFixture Fixture { get; }

        public IArtifactControllerWorkspaceV2Routes ArtifactController { get; }

        public ICodeVersionController CodeVersionController { get; }

        public IExperimentAgnosticRunController RunHistoryClient { get; }

        protected IModelVersionController ModelVersionController { get; }

        protected IDataVersionController DataVersionController { get; }

        protected IDataContainerController DataContainerController { get; }

        protected IDataStoreController DataStoreController { get; }
    }
}
