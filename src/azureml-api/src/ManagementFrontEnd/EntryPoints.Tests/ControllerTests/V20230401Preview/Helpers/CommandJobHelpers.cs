﻿// <copyright file="CommandJobHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using FakeItEasy;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.EnvironmentManagement.Contracts;
using Microsoft.MachineLearning.Execution.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview.Jobs;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.RunHistory.Contracts;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401Preview.Helpers
{
    public static class CommandJobHelpers
    {
        public static void ConfigureFakesForCreateThenUpdate(string jobId, CommandJob createJob, TestFixtureIsolatedManagementFrontEnd fixture)
        {
            var experimentId = Guid.NewGuid().ToString();

            fixture.SetupFakeS2SClient<IExperimentAgnosticRunController>(client => A.CallTo(() => client.GetRunData(A<Guid>._, A<string>._, A<string>._, A<GetRunDataRequestDto>._))
                .ThrowsAsync(new ServiceInvocationException(null, new HttpResponseMessage(HttpStatusCode.NotFound), string.Empty))
                .Once()
                .Then
                .Returns(GetCommandJobRunDataResult(createJob, experimentId, jobId, fixture))
                .NumberOfTimes(1));

            fixture.SetupFakeS2SClient<IRunController>(client => A.CallTo(() => client.AddOrModifyRun(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<CreateRunDto>._, A<TagOperations>._)).Returns(GetTestRunDto(createJob, experimentId, jobId, fixture)));
        }

        public static (string Id, CommandJob Job) GetTestMockCommandJob(TestFixtureManagementFrontEnd fixture, string jobId = null, CommandJob copyJob = null, string workspaceName = null)
        {
            var workspace = new WorkspaceContext2(Guid.NewGuid(), workspaceName ?? fixture.WorkspaceName, fixture.ResourceGroupName, fixture.SubscriptionId, null, null, null);

            jobId ??= Guid.NewGuid().ToString();

            var environmentNameVersion = "AzureML-Minimal";
            string testEnvironmentId;
            if (copyJob == null || copyJob.EnvironmentId == null)
            {
                testEnvironmentId = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeEnvironmentContainer, environmentNameVersion);
            }
            else
            {
                testEnvironmentId = copyJob.EnvironmentId;
            }

            var testDataBinding = new UriFolderJobInput()
            {
                Uri = "temp",
                Mode = InputDeliveryMode.Download
            };
            var inputDataBindings = (copyJob?.Inputs != null) ?
                new Dictionary<string, JobInput>(copyJob.Inputs) :
                new Dictionary<string, JobInput>()
                {
                    { Guid.NewGuid().ToString(), testDataBinding }
                };

            var job = new CommandJob()
            {
                ExperimentName = copyJob?.ExperimentName ?? Guid.NewGuid().ToString(),
                Resources = copyJob?.Resources ?? new JobResourceConfiguration()
                {
                    InstanceCount = 1
                },
                ComputeId = copyJob?.ComputeId ?? MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeCompute, Guid.NewGuid().ToString()),
                CodeId = copyJob?.CodeId ?? MfeResourceVersionArmScope.ToString(workspace, MfeConstants.ArmTypeCodeContainer, Guid.NewGuid().ToString(), "1"),
                Command = "python helloWorld.py",
                Description = copyJob?.Description ?? "job description",
                Inputs = inputDataBindings,
                EnvironmentId = testEnvironmentId,
                EnvironmentVariables = copyJob?.EnvironmentVariables != null ?
                    new Dictionary<string, string>(copyJob.EnvironmentVariables) :
                    new Dictionary<string, string>()
                    {
                        { "test", "test" }
                    },
                Tags = (copyJob?.Tags != null) ? new Dictionary<string, string>(copyJob.Tags) : new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Properties = (copyJob?.Properties != null) ? new Dictionary<string, string>(copyJob.Properties) : new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Status = copyJob?.Status ?? JobStatus.NotStarted,
                SecretsConfiguration = copyJob?.SecretsConfiguration ?? new Dictionary<string, Contracts.V20230401Preview.SecretConfiguration>() { { "secret1", new Contracts.V20230401Preview.SecretConfiguration { WorkspaceSecretName = "testName", Uri = "test/uri" } } },
                Identity = copyJob?.Identity ?? new AmlToken(),
                Limits = copyJob?.Limits ?? new CommandJobLimits()
            };

            return (jobId, job);
        }

        private static RunDetailsDto GetTestRunDetailsDto(CommandJob commandjob, string jobId, TestFixtureManagementFrontEnd fixture, bool isARoot = true, bool withInvalidData = false)
        {
            var testTarget = MfeResourceArmScope.Parse(commandjob.ComputeId, fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeCompute).MfeResourceName;
            var testTags = commandjob.Tags;
            var testProperties = new Dictionary<string, string>(commandjob.Properties);
            testProperties.Add(MfeConstants.SnapshotPropertyName, MfeResourceVersionArmScope.Parse(commandjob.CodeId, fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeCodeContainer).MfeResourceName);

            var (resourceIDName, resourceIDVersion) = MfeResourceArmScopeHelper.ExtractInternalIdFromArmId(commandjob.EnvironmentId, fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeEnvironmentContainer);

            var runConfiguration = new RunConfiguration()
            {
                Target = testTarget,
                NodeCount = commandjob.Resources.InstanceCount,
                Command = commandjob.Command,
                Environment = new EnvironmentDefinition()
                {
                    Name = resourceIDName,
                    Version = resourceIDVersion
                },
                EnvironmentVariables = commandjob.EnvironmentVariables,
                Identity = new Execution.Contracts.IdentityConfiguration()
                {
                    Type = Execution.Contracts.IdentityType.AMLToken
                },
                CredentialPassthrough = commandjob.Identity?.IdentityType == IdentityConfigurationType.UserIdentity,
            };

            if (withInvalidData)
            {
                var data = new Dictionary<string, Execution.Contracts.Data>();
                var uri = new Execution.Contracts.UriReference() { Path = "/subscriptions/00000000-0000-0000-0000-000000000000/resourcegroups/myRg/providers/Microsoft.MachineLearningServices/workspaces/myWorkspace/data/myData/versions/1" };
                var innerData = new Execution.Contracts.Data() { DataLocation = new DataLocation() { Type = "Dataset", Uri = uri } };

                data.Add("datasetArmid", innerData);
                runConfiguration.Data = data;
                var inputAsset = new InputAsset() { Asset = new Asset() { Type = "Dataset", AssetId = new AssetId("reg", "cid", "dataset", "blue") } };
                runConfiguration.InputAssets = new Dictionary<string, InputAsset>();
                runConfiguration.InputAssets.Add("datasetArmid", inputAsset);
            }

            var runDetails = new RunDetailsDto()
            {
                RunId = jobId,
                Status = commandjob.Status.ToString(),
                RunDefinition = JObject.FromObject(runConfiguration),
                Tags = testTags,
                Properties = testProperties
            };

            if (!isARoot)
            {
                runDetails.ParentRunId = Guid.NewGuid().ToString();
            }

            return runDetails;
        }

        private static RunDto GetTestRunDto(CommandJob commandjob, string experimentId, string id, TestFixtureManagementFrontEnd fixture, bool isARoot = true, string runType = null)
        {
            return new RunDto()
            {
                DataContainerId = $"dcid.{id}",
                ExperimentId = experimentId,
                RunId = id,
                RunType = runType ?? MfeConstants.CommandJobRunType,
                Status = commandjob.Status.ToString(),
                Tags = commandjob.Tags,
                ParentRunId = isARoot ? null : Guid.NewGuid().ToString(),
                Description = commandjob.Description,
                Properties = new Dictionary<string, string>(commandjob.Properties)
                {
                    { MfeConstants.SnapshotPropertyName, MfeResourceVersionArmScope.Parse(commandjob.CodeId, fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeCodeContainer).MfeResourceName }
                }
            };
        }

        private static GetRunDataResultDto GetCommandJobRunDataResult(CommandJob commandJob, string experimentId, string jobId, TestFixtureManagementFrontEnd fixture, bool isARoot = true, string runType = null, bool withInvalidData = false)
        {
            var runDto = GetTestRunDto(commandJob, experimentId, jobId, fixture, isARoot, runType);

            var runDetails = GetTestRunDetailsDto(commandJob, jobId, fixture, isARoot, withInvalidData);

            return new GetRunDataResultDto { RunMetadata = runDto, RunDefinition = runDetails.RunDefinition };
        }
    }
}
