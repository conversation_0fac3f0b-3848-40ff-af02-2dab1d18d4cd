﻿// <copyright file="DataAssetHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Azure.Storage.Blob;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview.Controllers;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401Preview.Helpers;

public class DataAssetHelpers
{
    private static readonly Random _random = new Random();
    private readonly RegistryContext _registryContext;
    private IDataVersionRegistryController _dataVersionRegistryController;

    public DataAssetHelpers(
        RegistryContext registryContext,
        IDataVersionRegistryController dataVersionRegistryController)
    {
        _registryContext = registryContext;

        InitialProperties = new Dictionary<string, string>() { { "key1", null }, { "timestamp_column1", "datetime" }, { "key3", "values2" } };
        InitialTags = new Dictionary<string, string>() { { "mytag1", "woah" }, { "mytag2", "cool" }, { "mytag3", "tags" } };
        _dataVersionRegistryController = dataVersionRegistryController;
    }

    public Dictionary<string, string> InitialProperties { get; }

    public Dictionary<string, string> InitialTags { get; }

    public async Task<string> GetDataUri(string dataContainerName, string dataVersion)
    {
        var pendingUploadRequestDto = new PendingUploadRequestDto { PendingUploadType = PendingUploadType.TemporaryBlobReference };
        var pendingUploadResponse = await _dataVersionRegistryController.CreateOrGetStartPendingUpload(
                                                                                        _registryContext.SubscriptionId,
                                                                                        _registryContext.ResourceGroup,
                                                                                        _registryContext.RegistryName,
                                                                                        dataContainerName,
                                                                                        dataVersion,
                                                                                        pendingUploadRequestDto).ConfigureAwait(false);

        var feedCredentials = pendingUploadResponse.BlobReferenceForConsumption.Credential;
        var containerSas = ((SASCredentialDto)feedCredentials).SASUri;
        var containerReference = new CloudBlobContainer(containerSas);
        var cloudBlockBlob = containerReference.GetBlockBlobReference($"File_{Guid.NewGuid()}.txt");
        await cloudBlockBlob.UploadTextAsync("File Content:{Guid.NewGuid()}").ConfigureAwait(false);

        var dataUri = pendingUploadResponse.BlobReferenceForConsumption.BlobUri.ToString();
        return dataUri;
    }

    /// <summary>
    /// Creates ARM Resource conversion for public contracts.
    /// </summary>
    public Resource<DataVersionBase> ConvertToArmResourceDto(DataVersionBase dataVersion, string version, SystemData systemData = null)
    {
        var dataVersionResource = new Resource<DataVersionBase>()
        {
            Name = version,
            Type = Feed.Contracts.AssetType.Data,
            Properties = dataVersion,
            SystemData = systemData ?? new SystemData()
        };

        return dataVersionResource;
    }

    public async Task<Resource<DataVersionBase>> RegisterDataContainerAndVersion(IDataVersionRegistryController dataVersionRegistryController, string dataContainerName, string dataVersion, bool isArchived = false, Dictionary<string, string> tags = null, IDataContainerRegistryController dataContainerRegistryController = null)
    {
        Resource<DataContainer> targetDatasetContainer = new Resource<DataContainer>() { Properties = new DataContainer() };
        await dataContainerRegistryController.CreateOrUpdate(_registryContext.SubscriptionId, _registryContext.ResourceGroup, _registryContext.RegistryName, dataContainerName, targetDatasetContainer).ConfigureAwait(false);

        return await RegisterDataVersion(dataVersionRegistryController, dataContainerName, dataVersion, isArchived, tags).ConfigureAwait(false);
    }

    public async Task<Resource<DataVersionBase>> RegisterDataVersion(IDataVersionRegistryController dataVersionRegistryController, string dataContainerName, string dataVersion, bool isArchived = false, Dictionary<string, string> tags = null)
    {
        var inputPublicBlobDataset = await CreateDataVersion(dataContainerName, dataVersion, isArchived).ConfigureAwait(false);

        Resource<DataVersionBase> dataVersionResource = ConvertToArmResourceDto(inputPublicBlobDataset, dataVersion);
        dataVersionResource.Properties.Tags = tags ?? dataVersionResource.Properties.Tags;
        dataVersionResource.Properties.IsArchived = isArchived;
        dataVersionResource.Type = "data";

        var response = await dataVersionRegistryController.CreateOrUpdate(_registryContext.SubscriptionId, _registryContext.ResourceGroup, _registryContext.RegistryName, dataContainerName, dataVersion, dataVersionResource).ConfigureAwait(false);
        await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
        return dataVersionResource;
    }

    public async Task<DataVersionBase> CreateDataVersion(string dataContainerName, string dataVersion, bool isArchived = false)
    {
        string datasetDescription = "Controller Testing Dataset";

        var datasetVersion = new UriFileDataVersion // Use UriFile as the sample version
        {
            Description = datasetDescription,
            Properties = InitialProperties,
            Tags = InitialTags,
            DataUri = await GetDataUri(dataContainerName, dataVersion).ConfigureAwait(false),
            IsArchived = isArchived
        };

        return datasetVersion;
    }

    public Resource<DataContainer> MakeDataContainerRequest(string dataContainerName, DataType? dataType = null, bool isArchived = false)
    {
        return new Resource<DataContainer>
        {
            Name = dataContainerName,
            Properties = new DataContainer
            {
                DataType = dataType ?? (DataType)_random.Next(0, 3),
                Description = "fake_description",
                IsArchived = isArchived,
                NextVersion = null,
                Properties = null,
                LatestVersion = null,
                Tags = new Dictionary<string, string>() { { "tag1", "val1" }, { "tag2", "val2" } }
            },
            Type = Feed.Contracts.AssetType.Data
        };
    }

    public async Task RegisterDataContainer(string dataContainerName, IDataContainerRegistryController dataContainerRegistryController, DataType dataType = DataType.uri_file, bool isArchived = false)
    {
        var request = MakeDataContainerRequest(dataContainerName, dataType, isArchived);
        var response = await dataContainerRegistryController.CreateOrUpdate(
            subscriptionId: _registryContext.SubscriptionId,
            resourceGroupName: _registryContext.ResourceGroup,
            registryName: _registryContext.RegistryName,
            name: dataContainerName,
            request: request);
        await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
    }

    public void CheckCreatedDataContainer(Resource<DataContainer> actual, Resource<DataContainer> expected)
    {
        actual.Id.Should().NotBeNull();
        actual.Name.Should().Be(expected.Name);
        actual.Properties.Should().BeEquivalentTo(
                                    expected.Properties,
                                    assertionOptions => assertionOptions.Excluding(expected => expected.NextVersion));
        actual.Type.Should().Be(expected.Type);
    }

    public void CheckCreatedDataVersion(Resource<DataVersionBase> actual, Resource<DataVersionBase> expected)
    {
        actual.Id.Should().NotBeNull();
        actual.Type.Should().Be(expected.Type);
        actual.Name.Should().Be(expected.Name);
        actual.Properties.Should().BeEquivalentTo(
                                    expected.Properties,
                                    options => options.Excluding(prop => prop.DataUri));

        Uri actualUri = new Uri(actual.Properties.DataUri);
        Uri expectedUri = new Uri(expected.Properties.DataUri);
        actualUri.AbsolutePath.Should().Be(expectedUri.AbsolutePath);
    }
}
