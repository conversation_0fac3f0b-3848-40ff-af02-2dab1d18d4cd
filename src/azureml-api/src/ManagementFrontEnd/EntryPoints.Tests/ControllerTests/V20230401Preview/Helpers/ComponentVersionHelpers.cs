﻿// <copyright file="ComponentVersionHelpers.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401Preview.Assets;
using Newtonsoft.Json.Linq;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401Preview.Helpers
{
    public static class ComponentVersionHelpers
    {
        public static Resource<ComponentVersion> GetNewComponentVersion(string name, string version, string environmentId, string codeId, bool isArchived = false)
        {
            var newComponentVersion = new ComponentVersion()
            {
                IsArchived = isArchived,
                ComponentSpec = JObject.Parse(@"{
    ""name"": """ + name + @""",
    ""version"": """ + version + @""",
    ""type"": ""command"",
    ""display_name"": """ + name + @""",
    ""description"": ""a component which reference a curated environment"",
    ""is_deterministic"": ""True"",
    ""inputs"": {
        ""input_path"": {
            ""name"": ""input_path"",
            ""optional"": ""False"",
            ""description"": ""The directory contains dataframe."",
            ""type"": ""DataFrameDirectory""
        },
        ""string_parameter"": {
            ""name"": ""string_parameter"",
            ""optional"": ""True"",
            ""description"": ""A parameter accepts a string value."",
            ""type"": ""String""
        },
        ""int_parameter"": {
            ""name"": ""int_parameter"",
            ""optional"": ""False"",
            ""description"": ""A parameter accepts an int value."",
            ""type"": ""Integer"",
            ""default"": ""3"",
            ""min"": ""1"",
            ""max"": ""5""
        },
        ""boolean_parameter"": {
            ""name"": ""boolean_parameter"",
            ""optional"": ""False"",
            ""description"": ""A parameter accepts a boolean value."",
            ""type"": ""Boolean""
        }
    },
    ""outputs"": {
        ""output_path"": {
            ""name"": ""output_path"",
            ""description"": ""The directory contains a dataframe."",
            ""type"": ""DataFrameDirectory""
        }
    },
    ""command"": ""python hello_world.py --input-path ${{inputs.input_path}} $[[--string-parameter ${{inputs.string_parameter}}]] --int-parameter ${{inputs.int_parameter}} --boolean-parameter ${{inputs.boolean_parameter}} --output-path ${{outputs.output_path}}"",
    ""environment"": """ + environmentId + @""",
    ""code"": """ + codeId + @"""
}"),
                Tags = new Dictionary<string, string> { { "tags1", "tags1" } },
                Properties = new Dictionary<string, string> { { "properties1", "properties1" } }
            };

            return new Resource<ComponentVersion>
            {
                Properties = newComponentVersion,
            };
        }

        public static bool Equal(JObject j1, JObject j2, string path, bool caseSensitive = true)
        {
            return string.Equals(j1[path].ToString(), j2[path].ToString(), caseSensitive ? System.StringComparison.InvariantCulture : System.StringComparison.InvariantCultureIgnoreCase);
        }
    }
}
