﻿// <copyright file="FeaturesetVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Common.Core.Contracts;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.FeatureStore.Contracts.Mfe;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230801Preview.Controllers.Workspaces;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Clients;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Configuration;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Services;
using Moq;
using Xunit;
using FeaturesetVersion = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.Featurestore.FeaturesetVersion;
using FeatureSetVersion = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturesetVersion;
using FeatureSetVersionDtoResource = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Resource<Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturesetVersion>;
using FeaturesetVersionResource = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.ArmCommon.Resource<Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.Featurestore.FeaturesetVersion>;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230801Preview
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    [MfeTest(TestCategory.Unit, ScenarioScope.Featuresets)]
    public class FeaturesetVersionControllerTests : IDisposable
    {
        private const string _featureSetName = "myFeatureSet";
        private const string _featureSetVersion = "1";

        private readonly FeaturesetVersionController _featureSetVersionController;
        private readonly WorkspaceContext2 _workspaceContext;

        public FeaturesetVersionControllerTests()
        {
            Mock<ILoggerFactory> loggerFactory = new Mock<ILoggerFactory>();
            Mock<ILogger> logger = new Mock<ILogger>();
            loggerFactory.Setup(m => m.CreateLogger(It.IsAny<string>())).Returns(logger.Object);

            var hostConfig = new Mock<IOptionsMonitor<ExperimentationHostConfiguration>>();
            hostConfig.Setup(m => m.CurrentValue).Returns(new ExperimentationHostConfiguration());
            hostConfig.Setup(m => m.CurrentValue).Returns(new ExperimentationHostConfiguration
            {
                FeatureStoreHostUri = "http://test",
            });

            var featureStoreConfig = new Mock<IOptionsMonitor<FeaturestoreConfiguration>>();
            featureStoreConfig.Setup(m => m.CurrentValue).Returns(new FeaturestoreConfiguration());

            var mockDto = new FeatureSetVersionDtoResource()
            {
                Name = "Name",
                Properties = new FeatureSetVersion()
                {
                    Description = "Description",
                }
            };

            var mockFeaturesetJob = new FeaturesetJob()
            {
                JobId = "SomeJobId"
            };

            var mockFeaturesetBackfillResponse = new FeaturesetVersionBackfillResponse
            {
                JobIds = new List<string>
                {
                    "somejob"
                }
            };

            var mockDtoLro = LongRunningOperationResponse.Create<FeatureSetVersionDtoResource>(mockDto);

            var mockDtoBackfillLro = LongRunningOperationResponse.Create(mockFeaturesetJob);

            var mockDtoBackfillResponseLro = LongRunningOperationResponse.Create(mockFeaturesetBackfillResponse);

            var mockDtoList = new PaginatedResult<FeatureSetVersionDtoResource>()
            {
                Value = new List<FeatureSetVersionDtoResource>()
                {
                    mockDto
                }
            };

            var mockFeaturesetJobList = new PaginatedResult<FeaturesetJob>()
            {
                Value = new List<FeaturesetJob>()
                {
                    mockFeaturesetJob
                }
            };

            var controller = new Mock<IFeatureSetVersionController>();
            controller.Setup(x => x.CreateOrUpdateAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<FeatureSetVersionDtoResource>())).ReturnsAsync(mockDtoLro);
            controller.Setup(x => x.ShowAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDto);
            controller.Setup(x => x.ListAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<ListViewType>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDtoList);
            controller.Setup(x => x.DeleteAsync(
               It.IsAny<Guid>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<bool>())).ReturnsAsync(LongRunningOperationResponse.Create<bool>(true));
            controller.Setup(x => x.ListMaterializationStatusAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockFeaturesetJobList);
            controller.Setup(x => x.BackfillOperationAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<FeaturesetVersionBackfillRequest>())).ReturnsAsync(mockDtoBackfillResponseLro);

            Mock<IHttpContextAccessor> httpContextAccessor = new Mock<IHttpContextAccessor>();
            Mock<IS2SClientWrapper> s2sClientFactory = new Mock<IS2SClientWrapper>();
            s2sClientFactory.Setup(x => x.CreateS2SClient<IFeatureSetVersionController>(
                It.IsAny<Uri>(),
                false,
                It.IsAny<Dictionary<string, string>>(),
                null,
                It.IsAny<TimeSpan>(),
                null,
                null,
                null)).Returns(controller.Object);

            IFeaturesetVersionService featureSetVersionService = new FeaturesetVersionService(
                                loggerFactory.Object,
                                s2sClientFactory.Object,
                                hostConfig.Object,
                                featureStoreConfig.Object,
                                httpContextAccessor.Object);

            Mock<IArmContextAccessor> armContextAccessor = new Mock<IArmContextAccessor>();
            armContextAccessor.Setup(x => x.GetResourceUri()).Returns(new Uri("http://localhost"));

            var mapperProfile = new Api.V20230801Preview.MapperProfile();
            var mapperConfig = new MapperConfiguration(config =>
            {
                config.AddProfile(mapperProfile);
            });
            mapperConfig.AssertConfigurationIsValid();
            mapperConfig.CompileMappings();

            var mapper = new Mapper(mapperConfig);

            _featureSetVersionController = new FeaturesetVersionController(
                armContextAccessor.Object,
                mapper,
                featureSetVersionService);

            _workspaceContext = new WorkspaceContext2(
                Guid.NewGuid(),
                "ws",
                "rg",
                Guid.NewGuid(),
                "eastus",
                "tenantid",
                null);
        }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        public async Task CreateOrUpdateAsyncTest()
        {
            var request = new FeaturesetVersionResource()
            {
                Name = "Name",
                Properties = new FeaturesetVersion()
                {
                    Description = "Description"
                }
            };

            var actionResult = await _featureSetVersionController.CreateOrUpdate(
                _workspaceContext,
                _featureSetName,
                _featureSetVersion,
                request,
                null).ConfigureAwait(false);

            var createdResult = actionResult as OkObjectResult;
            var featureSetDto = createdResult.Value as FeaturesetVersionResource;
            Assert.Equal("Name", featureSetDto.Name);
            Assert.Equal("Description", featureSetDto.Properties.Description);
        }

        [Fact]
        public async Task DeleteAsyncTestSuccess()
        {
            var actionResult = await _featureSetVersionController.Delete(
                _workspaceContext,
                _featureSetName,
                _featureSetVersion,
                null).ConfigureAwait(false);
            var okResult = actionResult as OkObjectResult;
        }

        [Fact]
        public async Task ListTest()
        {
            var actionResult = await _featureSetVersionController.List(
                _workspaceContext,
                _featureSetName,
                null,
                null,
                null,
                Contracts.V20230801Preview.Assets.ListViewType.ActiveOnly,
                20,
                null,
                null,
                null,
                null,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureSetDto = okResult.Value as ArmPaginatedResult<FeaturesetVersionResource>;
            Assert.Single(featureSetDto.Value);
            Assert.Equal("Name", featureSetDto.Value.First().Name);
            Assert.Equal("Description", featureSetDto.Value.First().Properties.Description);
        }

        [Fact]
        public async Task GetEntityTest()
        {
            var actionResult = await _featureSetVersionController.GetEntity(
                _workspaceContext,
                _featureSetName,
                _featureSetVersion,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureSetDto = okResult.Value as FeaturesetVersionResource;
            Assert.Equal("Name", featureSetDto.Name);
            Assert.Equal("Description", featureSetDto.Properties.Description);
        }

        [Fact]
        public async Task BackfillTest()
        {
            var request = new ManagementFrontEnd.Contracts.V20230801Preview.Featurestore.FeaturesetVersionBackfillRequest { DisplayName = "DisplayName" };
            var actionResult = await _featureSetVersionController.Backfill(
                _workspaceContext,
                _featureSetName,
                _featureSetVersion,
                request,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var features = okResult.Value as Contracts.V20230801Preview.Featurestore.FeaturesetVersionBackfillResponse;
            Assert.Equal("somejob", features.JobIds[0]);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _featureSetVersionController.Dispose();
            }
        }
    }
}