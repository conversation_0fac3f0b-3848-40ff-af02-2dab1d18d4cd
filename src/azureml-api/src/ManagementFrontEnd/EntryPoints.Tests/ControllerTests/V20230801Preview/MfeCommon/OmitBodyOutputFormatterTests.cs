﻿// <copyright file="OmitBodyOutputFormatterTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using FakeItEasy;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Services.ModelEndpointDiscoveryService;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230801Preview.MfeCommon
{
    /// <summary>
    /// Only do isolated testing
    /// </summary>
    [Collection(nameof(ControllerIsolatedTestsCollection))]
    public class IsolatedOmitBodyOutputFormatterTests
    {
        private readonly TestFixtureIsolatedManagementFrontEnd _fixture;
        private readonly IServerlessEndpointController _controller;

        private HttpResponseMessage _lastHttpResponse;

        public IsolatedOmitBodyOutputFormatterTests(TestFixtureIsolatedManagementFrontEnd fixture)
        {
            _fixture = fixture;
            _controller = _fixture.ManagementFrontEndClient.GetController<IServerlessEndpointController>(
                afterResponse: (response) => _lastHttpResponse = response);

            _fixture.GetHostService<IServerlessEndpointService>();

            var httpResponseMessage = new HttpResponseMessage()
            {
                StatusCode = HttpStatusCode.Accepted,
                ReasonPhrase = "Testing Accepted output",
            };
            httpResponseMessage.Headers.Add("Location", "test-location");

            var medsDeleteMethodDetails = new ServiceInvoker.MethodDetails(typeof(ModelEndpointDiscovery.Contracts.Controllers.IServerlessEndpointController).GetMethod("DeleteServerlessEndpoint"), "DELETE", string.Empty, typeof(IServerlessEndpointController));
            
            _fixture.SetupFakeS2SClient<ModelEndpointDiscovery.Contracts.Controllers.IServerlessEndpointController>((service, getAfterResponseHandler) =>
                A.CallTo(() => service.DeleteServerlessEndpoint(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .Invokes(() => getAfterResponseHandler()?.Invoke(httpResponseMessage, medsDeleteMethodDetails))    
                .Returns(Task.CompletedTask));
        }

        [Fact]
        [MfeTest(TestCategory.Component, ScenarioScope.Serverless)]
        public async Task Test_Accepted_HasBodyWithNull()
        {
            await _controller.Delete(_fixture.SubscriptionId, _fixture.ResourceGroupName, _fixture.WorkspaceName, "dne");

            var responseBody = await _lastHttpResponse.Content.ReadAsStringAsync();

            // This bug is addresed in Jan2024Preview and beyond, adding check here for back-compat reasons
            responseBody.Should().Be("null");
        }
    }

    public class ServerlessEndpointServiceTestHarness : ITestHarness
    {
        public ServerlessEndpointServiceTestHarness()
        {
            FakedServerlessEndpointService = A.Fake<IServerlessEndpointService>();
        }

        public IServerlessEndpointService FakedServerlessEndpointService;

        public void ConfigureServices(IServiceCollection services)
        {
            services.RemoveAll<IServerlessEndpointService>();
            services.AddSingleton(FakedServerlessEndpointService);
        }
    }
}
