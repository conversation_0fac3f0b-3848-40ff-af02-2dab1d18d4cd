﻿// <copyright file="FeaturestoreEntityContainerControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Common.Core.Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.FeatureStore.Contracts.Mfe;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230801Preview.Controllers.Workspaces;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Configuration;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Services;
using Moq;
using Xunit;
using FeatureEntityDtoResource = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Resource<Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturestoreEntityContainer>;
using FeaturestoreEntityContainer = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.Featurestore.FeaturestoreEntityContainer;
using FeatureStoreEntityContainer = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.FeatureStore.FeaturestoreEntityContainer;
using FeaturestoreEntityContainerResource = Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.ArmCommon.Resource<Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230801Preview.Featurestore.FeaturestoreEntityContainer>;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230801Preview
{
    [Trait(nameof(TestCategory), TestCategory.Unit)]
    [MfeTest(TestCategory.Unit, ScenarioScope.FeaturestoreEntities)]
    public class FeaturestoreEntityContainerControllerTests : IDisposable
    {
        private const string _featureEntityName = "myFeatureEntity";

        private readonly FeaturestoreEntityContainerController _featureEntityController;
        private readonly WorkspaceContext2 _workspaceContext;

        public FeaturestoreEntityContainerControllerTests()
        {
            Mock<ILoggerFactory> loggerFactory = new Mock<ILoggerFactory>();
            Mock<ILogger> logger = new Mock<ILogger>();
            loggerFactory.Setup(m => m.CreateLogger(It.IsAny<string>())).Returns(logger.Object);

            var hostConfig = new Mock<IOptionsMonitor<ExperimentationHostConfiguration>>();
            hostConfig.Setup(m => m.CurrentValue).Returns(new ExperimentationHostConfiguration());

            var featureStoreConfig = new Mock<IOptionsMonitor<FeaturestoreConfiguration>>();
            featureStoreConfig.Setup(m => m.CurrentValue).Returns(new FeaturestoreConfiguration());

            var mockDto = new FeatureEntityDtoResource()
            {
                Name = "Name",
                Properties = new FeatureStoreEntityContainer()
                {
                }
            };

            var mockDtoLro = LongRunningOperationResponse.Create<FeatureEntityDtoResource>(mockDto);

            var mockDtoList = new PaginatedResult<FeatureEntityDtoResource>()
            {
                Value = new List<FeatureEntityDtoResource>()
                {
                    mockDto
                }
            };

            var controller = new Mock<IFeatureEntityController>();
            controller.Setup(x => x.CreateOrUpdateAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<FeatureEntityDtoResource>())).ReturnsAsync(mockDtoLro);
            controller.Setup(x => x.ShowAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDto);
            controller.Setup(x => x.ListAsync(
                It.IsAny<Guid>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<ListViewType>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync(mockDtoList);
            controller.Setup(x => x.DeleteAsync(
               It.IsAny<Guid>(),
               It.IsAny<string>(),
               It.IsAny<string>(),
               It.IsAny<string>())).ReturnsAsync(LongRunningOperationResponse.Create<bool>(true));

            Mock<IS2SClientFactory> s2sClientFactory = new Mock<IS2SClientFactory>();
            s2sClientFactory.Setup(x => x.CreateS2SClient<IFeatureEntityController>(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                false)).Returns(controller.Object);

            IFeaturestoreEntityContainerService featureEntityService = new FeaturestoreEntityContainerService(
                                loggerFactory.Object,
                                s2sClientFactory.Object,
                                hostConfig.Object,
                                featureStoreConfig.Object);

            Mock<IArmContextAccessor> armContextAccessor = new Mock<IArmContextAccessor>();
            armContextAccessor.Setup(x => x.GetResourceUri()).Returns(new Uri("http://localhost"));

            var mapperProfile = new Api.V20230801Preview.MapperProfile();
            var mapperConfig = new MapperConfiguration(config =>
            {
                config.AddProfile(mapperProfile);
            });
            mapperConfig.AssertConfigurationIsValid();
            mapperConfig.CompileMappings();

            var mapper = new Mapper(mapperConfig);

            _featureEntityController = new FeaturestoreEntityContainerController(
                armContextAccessor.Object,
                mapper,
                featureEntityService);

            _workspaceContext = new WorkspaceContext2(
                Guid.NewGuid(),
                "ws",
                "rg",
                Guid.NewGuid(),
                "eastus",
                "tenantid",
                null);
        }

        public void Dispose()
        {
            this.Dispose(true);
            GC.SuppressFinalize(this);
        }

        [Fact]
        public async Task CreateOrUpdateAsyncTest()
        {
            var request = new FeaturestoreEntityContainerResource
            {
                Name = "Name",
                Properties = new FeaturestoreEntityContainer()
                {
                }
            };

            var actionResult = await _featureEntityController.CreateOrUpdate(
                _workspaceContext,
                _featureEntityName,
                request,
                null).ConfigureAwait(false);

            var createdResult = actionResult as CreatedResult;
            var featureEntityDto = createdResult.Value as FeaturestoreEntityContainerResource;
            Assert.Equal("Name", featureEntityDto.Name);
        }

        [Fact]
        public async Task DeleteAsyncTestSuccess()
        {
            var actionResult = await _featureEntityController.Delete(
                _workspaceContext,
                _featureEntityName,
                null).ConfigureAwait(false);
            var okResult = actionResult as OkObjectResult;
        }

        [Fact]
        public async Task ListTest()
        {
            var actionResult = await _featureEntityController.List(
                _workspaceContext,
                null,
                null,
                null,
                Contracts.V20230801Preview.Assets.ListViewType.ActiveOnly,
                20,
                null,
                null,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureEntityDto = okResult.Value as ArmPaginatedResult<FeaturestoreEntityContainerResource>;
            Assert.Single(featureEntityDto.Value);
            Assert.Equal("Name", featureEntityDto.Value.First().Name);
        }

        [Fact]
        public async Task GetEntityTest()
        {
            var actionResult = await _featureEntityController.GetEntity(
                _workspaceContext,
                _featureEntityName,
                null).ConfigureAwait(false);

            var okResult = actionResult as OkObjectResult;
            var featureEntityDto = okResult.Value as FeaturestoreEntityContainerResource;
            Assert.Equal("Name", featureEntityDto.Name);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _featureEntityController.Dispose();
            }
        }
    }
}