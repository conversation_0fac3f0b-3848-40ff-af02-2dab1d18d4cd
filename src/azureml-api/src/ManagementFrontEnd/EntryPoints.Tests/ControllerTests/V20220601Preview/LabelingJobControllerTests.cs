// <copyright file="LabelingJobControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using FluentAssertions.Equivalency;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Labeling.API.EntryPoints;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220601Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220601Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220601Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Fuzzing;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220601Preview
{
    // TODO: Re-enable these tests once the flakiness issue is fixed (Task 1552814: Reenable E2ELabelingJobControllerTests).
    /* [MfeTest(TestCategory.Integration)]
    public class E2ELabelingJobControllerTests : LabelingJobControllerTests<TestFixtureManagementFrontEnd>, IAsyncLifetime, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        private const string _imageDatasetBlobContainer = "testlabeling/images";
        private const string _textDatasetBlobContainer = "testlabeling/texts";

        public E2ELabelingJobControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
            DatastoreController = Fixture.ManagementFrontEndClient.GetController<IDatastoreController>();

            DataVersionController = Fixture.ManagementFrontEndClient.GetController<IDataVersionController>();
        }

        public IDatastoreController DatastoreController { get; }

        public IDataVersionController DataVersionController { get; }

        public async Task InitializeAsync()
        {
            string datastoreName = "workspaceblobstore";
            Resource<DatastoreProperties> datastore = await DatastoreController
                .Get(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    datastoreName)
                .ConfigureAwait(false);

            try
            {
                await DataVersionController
                    .Get(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.WorkspaceName,
                        TestImageDatasetName,
                        TestDatasetVersion)
                    .ConfigureAwait(false);
            }
            catch (ServiceInvocationException e) when (e.Response != null && e.Response.StatusCode == HttpStatusCode.NotFound)
            {
                string container = datastore.Properties.Contents.AzureStorage.ContainerName;
                await UploadAndCreateDataset(container, _imageDatasetBlobContainer, "labeling_image_1.jpg", datastore.Id, TestImageDatasetName).ConfigureAwait(false);
                await UploadAndCreateDataset(container, _textDatasetBlobContainer, "labeling_text_1.txt", datastore.Id, TestTextDatasetName).ConfigureAwait(false);
            }
        }

        public Task DisposeAsync()
            => Task.CompletedTask;

        private async Task UploadAndCreateDataset(string containerName, string path, string fileName, string datastoreId, string datasetName)
        {
            string filePath = await UploadBlob(containerName, path, fileName).ConfigureAwait(false);

            await CreateDataset(datastoreId, filePath, datasetName).ConfigureAwait(false);
        }

        private async Task<string> UploadBlob(string containerName, string path, string fileName)
        {
            StorageConnectionDto storageConnectionDto = await Fixture.GetStorageConnection().ConfigureAwait(false);
            CloudStorageAccount account = CloudStorageAccount.Parse(storageConnectionDto.ConnectionString);
            CloudBlobClient blobClient = account.CreateCloudBlobClient();

            CloudBlobContainer cloudBlobContainer = blobClient.GetContainerReference(containerName);
            await cloudBlobContainer.CreateIfNotExistsAsync().ConfigureAwait(false);

            string filePath = $"{path}/{fileName}";
            CloudBlockBlob cloudBlockBlob = cloudBlobContainer.GetBlockBlobReference(filePath);
            await cloudBlockBlob.UploadFromFileAsync(fileName).ConfigureAwait(false);

            return filePath;
        }

        private async Task CreateDataset(string datastoreId, string filePath, string datasetName)
        {
            DataVersion dataVersion = new DataVersion
            {
                DatastoreId = datastoreId,
                DatasetType = DatasetType.Simple,
                AssetPath = new AssetPath
                {
                    IsDirectory = false,
                    Path = filePath
                }
            };

            await DataVersionController
                .CreateOrUpdate(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    datasetName,
                    TestDatasetVersion,
                    new Resource<DataVersion>
                    {
                        Properties = dataVersion
                    })
                .ConfigureAwait(false);
        }
    } */

    [MfeTest(TestCategory.Component, ScenarioScope.Labeling)]
    [Collection(nameof(ControllerIsolatedTestsCollection))]
    public class IsolatedLabelingJobControllerTests : LabelingJobControllerTests<TestFixtureIsolatedManagementFrontEnd>
    {
        public IsolatedLabelingJobControllerTests(TestFixtureIsolatedManagementFrontEnd fixture)
            : base(fixture)
        {
            fixture.ResetFakeS2SClients();
            ConfigureFakes();
        }

        [Fact]
        public async Task FuzzCreateLabelingJob()
        {
            var job = new Resource<LabelingJob>
            {
                Name = Guid.NewGuid().ToString(),
                Properties = GetTestLabelingJob(MediaType.Image, ImageDatasetResourceId),
            };

            var fuzzer = new ControllerFuzzer(Fixture, ConfigureFakes);

            await fuzzer.FuzzControllerMethod<ILabelingJobController>(controller => controller.CreateOrUpdateJob(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, job.Name, job)).ConfigureAwait(false);
        }

        private void ConfigureFakes()
        {
            FakeLabelingJobSetup.ConfigureFakes(Fixture.GetFakeS2SClient<ILabelingS2SController>());
        }
    }

    [MfeTest(TestCategory.Integration, ScenarioScope.Labeling)]
    public abstract class LabelingJobControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public const string TestImageDatasetName = "testImageDataset";
        public const string TestTextDatasetName = "testTextDataset";
        public const string TestDatasetVersion = "1";
        public const int RetryIntervalInMilliSeconds = 30000;
        public const int MaxRetryCount = 20;

        public LabelingJobControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            Controller = Fixture.ManagementFrontEndClient.GetController<ILabelingJobController>();
        }

        public ILabelingJobController Controller { get; }

        public TFixture Fixture { get; }

        public string ImageDatasetResourceId => $"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/data/{TestImageDatasetName}{TestDatasetVersion}/versions/1";

        public string TextDatasetResourceId => $"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/data/{TestTextDatasetName}{TestDatasetVersion}/versions/1";

        [Fact(Timeout = 600000)]
        public async Task TestJobLifecycle()
        {
            string id = Guid.NewGuid().ToString();
            LabelingJob labelingJob = GetTestLabelingJob(MediaType.Image, ImageDatasetResourceId);
            LabelingJob modifiedlabelingJob = GetTestLabelingJob(MediaType.Image, ImageDatasetResourceId, labelingJob);
            modifiedlabelingJob.Tags = new Dictionary<string, string>(labelingJob.Tags);
            modifiedlabelingJob.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());

            Resource<LabelingJob> result = await Controller
                .CreateOrUpdateJob(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id,
                    new Resource<LabelingJob>() { Properties = labelingJob })
                .ConfigureAwait(false);

            ValidateLabelingJob(result, labelingJob, id);

            int retryCount = 0;
            do
            {
                if (retryCount > 0)
                {
                    await Task.Delay(RetryIntervalInMilliSeconds).ConfigureAwait(false);
                }

                result = await Controller
                    .GetById(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.WorkspaceName,
                        id)
                    .ConfigureAwait(false);
            }
            while (result.Properties.Status == JobStatus.Starting && ++retryCount < MaxRetryCount);

            result.Properties.Status.Should().Be(JobStatus.Running);

            await Controller
                .Pause(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id)
                .ConfigureAwait(false);

            result = await Controller
                .GetById(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id)
                .ConfigureAwait(false);

            result.Properties.Status.Should().Be(JobStatus.Paused);

            result = await Controller
                .CreateOrUpdateJob(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id,
                    new Resource<LabelingJob>() { Properties = modifiedlabelingJob })
                .ConfigureAwait(false);

            ValidateLabelingJob(result, modifiedlabelingJob, id);
            result.Properties.Status.Should().Be(JobStatus.Paused);

            var op = await Controller
                .Resume(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id)
                .ConfigureAwait(false);

            await op.PollUntilComplete(TimeSpan.FromSeconds(5), new CancellationTokenSource(TimeSpan.FromMinutes(5)).Token).ConfigureAwait(false);

            retryCount = 0;
            do
            {
                if (retryCount > 0)
                {
                    await Task.Delay(RetryIntervalInMilliSeconds).ConfigureAwait(true);
                }

                result = await Controller
                    .GetById(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.WorkspaceName,
                        id)
                    .ConfigureAwait(false);
            }
            while (result.Properties.Status == JobStatus.Preparing && ++retryCount < MaxRetryCount);

            result.Properties.Status.Should().Be(JobStatus.Running);

            ValidateLabelingJob(result, labelingJob, id);

            await DeleteProject(id).ConfigureAwait(false);
            Func<Task<Resource<LabelingJob>>> retrievalTask = () => Controller.GetById(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, id);
            (await retrievalTask.Should().ThrowAsync<ServiceInvocationException>()).Which.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact(Timeout = 600000)]
        public async Task TestList()
        {
            const int UNSUPPORTED_ANNOTATION_TYPE = -1;
            const int UNSUPPORTED_MEDIA_TYPE = -1;

            List<(string, LabelingJob)> labelingJobs = new List<(string, LabelingJob)>(); // (JobID, LabelingJob)
            string jobId = string.Empty;
            LabelingJob labelingJob = null;
            foreach (ImageAnnotationType annotationType in Enum.GetValues(typeof(ImageAnnotationType)))
            {
                jobId = Guid.NewGuid().ToString();
                labelingJob = GetTestLabelingJob(
                    MediaType.Image,
                    ImageDatasetResourceId,
                    null, // copyLabelingJob
                    new LabelingJobImageProperties { AnnotationType = annotationType });
                labelingJobs.Add((jobId, labelingJob));
            }

            foreach (TextAnnotationType annotationType in Enum.GetValues(typeof(TextAnnotationType)))
            {
                jobId = Guid.NewGuid().ToString();
                labelingJob = GetTestLabelingJob(
                    MediaType.Text,
                    ImageDatasetResourceId,
                    null, // copyLabelingJob
                    new LabelingJobTextProperties { AnnotationType = annotationType });
                labelingJobs.Add((jobId, labelingJob));
            }

            jobId = Guid.NewGuid().ToString();
            labelingJob = GetTestLabelingJob(MediaType.Image, TextDatasetResourceId);
            labelingJobs.Add((jobId, labelingJob));
            var unsupportedImageAnnotationTypeJobId = jobId;

            jobId = Guid.NewGuid().ToString();
            labelingJob = GetTestLabelingJob(MediaType.Text, TextDatasetResourceId);
            labelingJobs.Add((jobId, labelingJob));
            var unsupportedTextAnnotationTypeJobId = jobId;

            jobId = Guid.NewGuid().ToString();
            labelingJob = GetTestLabelingJob(MediaType.Image, TextDatasetResourceId);
            labelingJobs.Add((jobId, labelingJob));
            var unsupportedMediaTypeJobId = jobId;

            foreach (var job in labelingJobs)
            {
                await Controller
                .CreateOrUpdateJob(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    job.Item1,
                    new Resource<LabelingJob>() { Properties = job.Item2 })
                .ConfigureAwait(false);
            }

            // Bypass API validdation logic
            FakeLabelingJobSetup.SetAnnotationType(unsupportedImageAnnotationTypeJobId, UNSUPPORTED_ANNOTATION_TYPE);
            FakeLabelingJobSetup.SetAnnotationType(unsupportedTextAnnotationTypeJobId, UNSUPPORTED_ANNOTATION_TYPE);
            FakeLabelingJobSetup.SetMediaType(unsupportedMediaTypeJobId, UNSUPPORTED_MEDIA_TYPE);

            ArmPaginatedResult<Resource<LabelingJob>> results = await Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName).ConfigureAwait(false);

            // GreaterThan accounts for the extra job created in FuzzCreateLabelingJob
            results.Value.Count.Should().BeGreaterThanOrEqualTo(labelingJobs.Count - 3);
            int countRemaining = 0;

            for (int i = 0; i < labelingJobs.Count; i++)
            {
                Resource<LabelingJob> result = results.Value.Find(j => j.Id.EndsWith(labelingJobs[i].Item1, StringComparison.InvariantCultureIgnoreCase));
                if (result != null)
                {
                    ValidateLabelingJob(result, labelingJobs[i].Item2, labelingJobs[i].Item1);
                }
                else
                {
                    countRemaining++;
                }

                await DeleteProject(labelingJobs[i].Item1).ConfigureAwait(false);
            }
            countRemaining.Should().Be(3);
        }

        [Fact(Timeout = 600000)]
        public async Task TestExport()
        {
            string id = Guid.NewGuid().ToString();
            LabelingJob labelingJob = GetTestLabelingJob(MediaType.Image, ImageDatasetResourceId);

            await Controller
                .CreateOrUpdateJob(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id,
                    new Resource<LabelingJob>() { Properties = labelingJob })
                .ConfigureAwait(false);

            var op = await Controller
                .ExportLabels(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.WorkspaceName,
                    id,
                    new CocoExportSummary())
                .ConfigureAwait(false);

            FakeLabelingJobSetup.SetLabelExportState(Labeling.API.Contracts.LabelExportState.Running);
            ExportSummary summary = null;
            try
            {
                await op.PollUntilComplete(TimeSpan.FromSeconds(5), new CancellationTokenSource(TimeSpan.FromSeconds(10)).Token).ConfigureAwait(false);
            }
            catch (TaskCanceledException)
            {
                // The op.PollUntilComplete should timeout and get canceled
            }

            FakeLabelingJobSetup.SetLabelExportState(Labeling.API.Contracts.LabelExportState.Completed);
            summary = await op.PollUntilComplete(TimeSpan.FromSeconds(5), new CancellationTokenSource(TimeSpan.FromSeconds(15)).Token).ConfigureAwait(false);

            summary.Format.Should().Be(ExportFormatType.Coco);
            summary.LabelingJobId.Should().Be(id);
            summary.StartDateTime.Should().NotBeNull();
            summary.EndDateTime.Should().NotBeNull();

            await DeleteProject(id).ConfigureAwait(false);
        }

        protected LabelingJob GetTestLabelingJob(
            MediaType mediaType,
            string datasetResourceId,
            LabelingJob copyLabelingJob = null,
            LabelingJobMediaProperties mediaProperties = null)
        {
            return new LabelingJob()
            {
                JobType = copyLabelingJob?.JobType ?? JobType.Labeling,
                DataConfiguration = copyLabelingJob?.DataConfiguration ?? new LabelingDataConfiguration
                {
                    DataId = datasetResourceId,
                    IncrementalDataRefreshEnabled = true,
                },
                MLAssistConfiguration = copyLabelingJob?.MLAssistConfiguration ?? new MLAssistConfigurationDisabled(),
                LabelCategories = copyLabelingJob?.LabelCategories ?? new Dictionary<string, LabelCategory>
                {
                    {
                        FakeLabelingJobSetup.TestLabelCategory, new LabelCategory
                        {
                            DisplayName = FakeLabelingJobSetup.TestLabelCategory,
                            MultiSelectEnabled = false,
                            Classes = new Dictionary<string, LabelClass>
                            {
                                { FakeLabelingJobSetup.TestLabelClass1, new LabelClass { DisplayName = FakeLabelingJobSetup.TestLabelClass1 } },
                                { FakeLabelingJobSetup.TestLabelClass2, new LabelClass { DisplayName = FakeLabelingJobSetup.TestLabelClass2 } }
                            }
                        }
                    }
                },
                JobInstructions = copyLabelingJob?.JobInstructions ?? new LabelingJobInstructions
                {
                    Uri = Guid.NewGuid().ToString()
                },
                LabelingJobMediaProperties = copyLabelingJob?.LabelingJobMediaProperties ??
                    mediaProperties ?? GetMediaProperties(mediaType),
                Status = copyLabelingJob == null ? JobStatus.NotStarted : JobStatus.Paused,
                Tags = copyLabelingJob?.Tags ?? new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Properties = copyLabelingJob?.Properties ?? new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } }
            };

            LabelingJobMediaProperties GetMediaProperties(MediaType mediaType)
                => mediaType == MediaType.Image
                    ? (LabelingJobMediaProperties)new LabelingJobImageProperties { AnnotationType = ImageAnnotationType.BoundingBox }
                    : new LabelingJobTextProperties { AnnotationType = TextAnnotationType.Classification };
        }

        private void ValidateLabelingJob(Resource<LabelingJob> result, LabelingJob expected, string expectedJobName)
        {
            result.Id.Should().EndWith(expectedJobName);
            result.Properties.JobType.Should().Be(JobType.Labeling);
            result.Properties.Should().BeEquivalentTo(expected, ExcludeProperties);
        }

        private EquivalencyAssertionOptions<LabelingJob> ExcludeProperties(EquivalencyAssertionOptions<LabelingJob> options)
        {
            options.Excluding(t => t.ProjectId);
            options.Excluding(t => t.CreatedDateTime);
            options.Excluding(t => t.Status);
            options.Excluding(t => t.ProgressMetrics);
            options.Excluding(t => t.StatusMessages);
            options.Excluding(t => t.Properties);
            options.Excluding(t => t.Tags);
            options.Excluding(t => t.DataConfiguration);
            options.Excluding(t => t.MLAssistConfiguration);
            options.Excluding(t => t.LabelCategories);
            options.Excluding(t => t.JobInstructions);
            return options;
        }

        private async Task DeleteProject(string projectId)
        {
            int attempts = 0;
            do
            {
                try
                {
                    attempts++;
                    await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, projectId).ConfigureAwait(false);

                    break;
                }
                catch (ServiceInvocationException e) when (e.Response.StatusCode == HttpStatusCode.Conflict)
                {
                    if (attempts > MaxRetryCount)
                    {
                        throw;
                    }
                }

                await Task.Delay(RetryIntervalInMilliSeconds).ConfigureAwait(true);
            }
            while (true);
        }
    }
}
