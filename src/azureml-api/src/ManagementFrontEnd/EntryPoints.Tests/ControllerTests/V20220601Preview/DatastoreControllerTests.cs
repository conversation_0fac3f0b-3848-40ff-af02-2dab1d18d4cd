﻿// <copyright file="DatastoreControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using FakeItEasy;
using FluentAssertions;
using FluentAssertions.Equivalency;
using FluentAssertions.Extensions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220601Preview;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220601Preview.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20220601Preview.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.CustomSerializers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Fuzzing;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Newtonsoft.Json;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20220601Preview
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Datastores)]
    public class E2EDatastoreControllerTests : DatastoreControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2EDatastoreControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    [MfeTest(TestCategory.Component, ScenarioScope.Datastores)]
    [Collection(nameof(ControllerIsolatedTestsCollection))]
    public class IsolatedDatastoreControllerTests : DatastoreControllerTests<TestFixtureIsolatedManagementFrontEnd>
    {
        public IsolatedDatastoreControllerTests(TestFixtureIsolatedManagementFrontEnd fixture)
            : base(fixture)
        {
            Fixture.ResetFakeS2SClients();
            ConfigureFakes();
        }

        #region Test case generators

        public static IEnumerable<object[]> TestRequiredAttributeFailureErrorCodeTestCases()
        {
            yield return new object[]
            {
                // Tests validation of deeply nested polymorphic type missing many [Required] properties
                new Resource<Datastore>
                {
                    Properties = new AzureFileDatastore
                    {
                        Description = "description",
                        Tags = new Dictionary<string, string>
                        {
                            { "tag1", "value1" },
                            { "tag2", "value2" },
                        },
                    },
                },
                new List<PropertyInfo>(),
                nameof(AzureFileDatastore.Credentials)
            };

            // Tests validation of deeply nested polymorphic derived type
            // missing a [Required] polymorphic type.
            // Should fail due to missing Credentials
            yield return new object[]
            {
                new Resource<Datastore>
                {
                    Properties = new AzureFileDatastore
                    {
                        Description = "description",
                        AccountName = "accountName",
                        FileShareName = "fileShareName",
                        Tags = new Dictionary<string, string>
                        {
                            { "tag1", "value1" },
                            { "tag2", "value2" },
                        },
                    }
                },
                new List<PropertyInfo>()
                {
                    typeof(AzureFileDatastore).GetProperty(nameof(AzureFileDatastore.Credentials))
                },
                nameof(AzureFileDatastore.Credentials)
            };

            // Tests validation of deeply nested polymorphic derived type
            // missing a [Required] polymorphic type.
            // Should fail due to null Credentials
            yield return new object[]
            {
                new Resource<Datastore>
                {
                    Properties = new AzureFileDatastore
                    {
                        Description = "description",
                        AccountName = "accountName",
                        FileShareName = "fileShareName",
                        Tags = new Dictionary<string, string>
                        {
                            { "tag1", "value1" },
                            { "tag2", "value2" },
                        },
                    }
                },
                new List<PropertyInfo>(),
                nameof(AzureFileDatastore.Credentials)
            };

            // Tests validation of a deeply nested polymorphic derived type containing another polymorphic derived type
            // that's missing a [Required] non-nullable property.
            // Should fail due to missing Credentials.TenantId
            yield return new object[]
            {
                new Resource<Datastore>
                {
                    Properties = new AzureFileDatastore
                    {
                        Description = "description",
                        AccountName = "accountName",
                        FileShareName = "fileShareName",
                        Credentials = new CertificateDatastoreCredentials()
                        {
                            ClientId = Guid.NewGuid(),
                            Thumbprint = "thumbprint",
                            Secrets = new CertificateDatastoreSecrets()
                        },
                        Tags = new Dictionary<string, string>
                        {
                            { "tag1", "value1" },
                            { "tag2", "value2" },
                        },
                    }
                },
                new List<PropertyInfo>()
                {
                    typeof(CertificateDatastoreCredentials).GetProperty(nameof(CertificateDatastoreCredentials.TenantId))
                },
                nameof(CertificateDatastoreCredentials.TenantId)
            };

            // Tests validation of a deeply nested polymorphic derived type missing a [Required] non-nullable property
            // Should fail due to missing AccountName
            yield return new object[]
            {
                new Resource<Datastore>
                {
                    Properties = new AzureFileDatastore
                    {
                        Description = "description",
                        FileShareName = "fileShareName",
                        Credentials = new CertificateDatastoreCredentials()
                        {
                            TenantId = Guid.NewGuid(),
                            ClientId = Guid.NewGuid(),
                            Thumbprint = "thumbprint",
                            Secrets = new CertificateDatastoreSecrets()
                        },
                        Tags = new Dictionary<string, string>
                        {
                            { "tag1", "value1" },
                            { "tag2", "value2" },
                        },
                   }
                },
                new List<PropertyInfo>()
                {
                    typeof(AzureFileDatastore).GetProperty(nameof(AzureFileDatastore.AccountName))
                },
                nameof(AzureFileDatastore.AccountName)
            };

            // Tests validation of a deeply nested polymorphic derived type missing a [Required] non-nullable property
            // Should fail due to null AccountName
            yield return new object[]
            {
                new Resource<Datastore>
                {
                    Properties = new AzureFileDatastore
                    {
                        Description = "description",
                        FileShareName = "fileShareName",
                        Credentials = new CertificateDatastoreCredentials()
                        {
                            TenantId = Guid.NewGuid(),
                            ClientId = Guid.NewGuid(),
                            Thumbprint = "thumbprint",
                            Secrets = new CertificateDatastoreSecrets()
                        },
                        Tags = new Dictionary<string, string>
                        {
                            { "tag1", "value1" },
                            { "tag2", "value2" },
                        },
                   }
                },
                new List<PropertyInfo>(),
                nameof(AzureFileDatastore.AccountName)
            };
        }

        #endregion

        [Theory]
        [InlineData(HttpStatusCode.BadRequest, HttpStatusCode.BadRequest)]
        [InlineData(HttpStatusCode.NotFound, HttpStatusCode.NotFound)]
        [InlineData(HttpStatusCode.Forbidden, HttpStatusCode.Unauthorized)]
        public async Task TestServiceErrorCodesAsync(HttpStatusCode datastoreStatusCode, HttpStatusCode mfeStatusCode)
        {
            Fixture.SetupFakeS2SClient<IDataStoreController>(controller =>
                A.CallTo(() => controller.Get(A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<bool>._, A<int>._, false))
                    .ThrowsAsync(ServiceInvocationExceptionUtil.Create(datastoreStatusCode)));

            Func<Task> call = () => Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "name");
            (await call.Should().ThrowAsync<ServiceInvocationException>()).Which.Response.StatusCode.Should().Be(mfeStatusCode);
        }

        [Theory]
        [MemberData(nameof(TestRequiredAttributeFailureErrorCodeTestCases))]
        public async Task TestRequiredAttributeFailureErrorCodeAsync(
            Resource<Datastore> datastoreCreateDto,
            List<PropertyInfo> propertiesToExcludeFromSerialization,
            string expectedMissingFieldInResponseErrorMessage)
        {
            // Need to exclude non-nullable properties from the json, as otherwise
            // they will default a value in the dto and will be present in the json.
            // To do so, need to create a new MFE client (with its own json serializer settings)
            // to set the excluded json properties, as otherwise the exclusion list may interfere
            // with other tests being run concurrently using the same fixture's MFE client.
            var mfeClient = await Fixture.GetManagementFrontEndClient(Fixture.ContributorRole).ConfigureAwait(false);
            var mfeContractResolver = (MfeTestContractResolver)mfeClient.SerializerSettings.ContractResolver;
            mfeContractResolver.PropertiesToExcludeFromSerialization.AddRange(propertiesToExcludeFromSerialization);
            var controller = mfeClient.GetController<IDatastoreController>();

            Func<Task> call = () => controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, "name", datastoreCreateDto);
            var exception = (await call.Should().ThrowAsync<ServiceInvocationException>()).And;
            exception.Response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            exception.ResponseBody.Should().Match(responseBody => responseBody.Contains(expectedMissingFieldInResponseErrorMessage, StringComparison.OrdinalIgnoreCase));
        }

        [Fact]
        public async Task FuzzCreateDatastore()
        {
            var (storageAccountName, storageKey) = await GetStorageInfo().ConfigureAwait(false);

            var name = GenerateDatastoreName(DateTime.UtcNow);
            var request = GenerateDatastore(storageAccountName, storageKey);

            var fuzzer = new ControllerFuzzer(Fixture, ConfigureFakes);

            await fuzzer.FuzzControllerMethod<IDatastoreController>(controller => controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, request)).ConfigureAwait(false);
        }

        private void ConfigureFakes()
        {
            FakeDatastoreV1Setup.ConfigureFakes(Fixture.GetFakeS2SClient<IDataStoreController>(), Fixture.GetFakeS2SClient<IDataStoreDefaultController>(), Fixture.StorageContainer);
        }
    }

    public abstract class DatastoreControllerTests<TFixture> : IAsyncLifetime
        where TFixture : TestFixtureManagementFrontEnd
    {
        private readonly JsonSerializerSettings _serializerSettings = new JsonSerializerSettings().AddAllCustomSerializerSettings();

        public DatastoreControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            Controller = Fixture.ManagementFrontEndClient.GetController<IDatastoreController>();
        }

        public IDatastoreController Controller { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestDatastoreCreateUpdateDelete()
        {
            // Configuration setup.
            var (storageAccountName, storageKey) = await GetStorageInfo().ConfigureAwait(false);

            var datastoreName = GenerateDatastoreName(DateTime.UtcNow);

            // Test each operation (order is relevant; e.g., the Get test needs the datastore from Create to exist).
            var createdDatastore = await TestCreate(datastoreName, storageAccountName, storageKey).ConfigureAwait(false);
            var retrievedDatastore = await TestGet(datastoreName, createdDatastore).ConfigureAwait(false);
            var retrievedSecret = await TestGetSecrets(datastoreName, storageKey).ConfigureAwait(false);
            var retrievedDatastores = await TestList(datastoreName, retrievedDatastore).ConfigureAwait(false);
            var oldDefaultDatastore = await TestGetDefault(datastoreName).ConfigureAwait(false);
            var newDefaultDatastore = await TestSetDefault(datastoreName, storageKey, createdDatastore).ConfigureAwait(false);
            var updatedDatastore = await TestUpdate(datastoreName, storageKey, newDefaultDatastore).ConfigureAwait(false);
            await TestDelete(datastoreName).ConfigureAwait(false);
        }

        [Fact]
        public async Task TestDatastoreCreateWithoutCredentials()
        {
            var (storageAccountName, _) = await GetStorageInfo().ConfigureAwait(false);

            var datastoreName = GenerateDatastoreName(DateTime.UtcNow);

            await TestCreate(datastoreName, storageAccountName).ConfigureAwait(false);
            await TestDelete(datastoreName).ConfigureAwait(false);
        }

        public Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public async Task DisposeAsync()
        {
            // Cleanup old datastores.
            var allDatastores = await ListDatastores(searchText: "test_mfe_").ConfigureAwait(false);
            var oldDatastoreName = GenerateDatastoreName(DateTime.UtcNow.AddHours(-1));
            var oldDatastores = allDatastores.Where(x => x.Name.StartsWith("test_mfe_", StringComparison.InvariantCulture) && string.CompareOrdinal(x.Name, oldDatastoreName) < 0).ToList();
            await Task.WhenAll(oldDatastores.Select(x => Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, x.Name))).ConfigureAwait(false);
        }

        protected static string GenerateDatastoreName(DateTime timestamp)
        {
            return $"test_mfe_{timestamp:yyyyMMddHHmmss}_{Guid.NewGuid().ToString()[..8]}";
        }

        protected async Task<List<Resource<Datastore>>> ListDatastores(int count = 30, bool? isDefault = null, IList<string> names = null, string searchText = null, string orderBy = null, bool orderByAsc = false)
        {
            return await ControllerHelpers.List(skipToken => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, skipToken, count, isDefault, names, searchText, orderBy, orderByAsc)).ToListAsync();
        }

        protected Resource<Datastore> GenerateDatastore(string storageAccountName, string storageKey = null, string subscriptionId = null, string resourceGroup = null)
        {
            var credentials = storageKey != null
                ? (DatastoreCredentials)new AccountKeyDatastoreCredentials { Secrets = new AccountKeyDatastoreSecrets { Key = storageKey } }
                : (DatastoreCredentials)new NoneDatastoreCredentials();

            return new Resource<Datastore>
            {
                Properties = new AzureBlobDatastore
                {
                    AccountName = storageAccountName,
                    ContainerName = "azureml",
                    Credentials = credentials,
                    Endpoint = "core.windows.net",
                    Protocol = "https",
                    Description = "description",
                    Tags = new Dictionary<string, string>
                    {
                        { "tag1", "value1" },
                        { "tag2", "value2" },
                    },
                    SubscriptionId = subscriptionId,
                    ResourceGroup = resourceGroup
                },
            };
        }

        protected async Task<(string StorageAccountName, string StorageKey)> GetStorageInfo()
        {
            var workspaceKeys = await Fixture.GetWorkspaceKeys().ConfigureAwait(false);
            var storageAccountName = workspaceKeys.UserStorageArmId.Split('/').Last();
            var storageKey = workspaceKeys.UserStorageKey;

            return (storageAccountName, storageKey);
        }

        #region Sub-test methods under TestDatastoreCreateUpdateDelete
        private async Task<Resource<Datastore>> TestCreate(string datastoreName, string storageAccountName, string storageKey = null)
        {
            string subscriptionId = Guid.NewGuid().ToString();
            const string resourceGroup = "resourceGroup";
            var datastoreRequest = GenerateDatastore(storageAccountName, storageKey, subscriptionId, resourceGroup);

            Resource<Datastore> createdDatastore = await Controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName, datastoreRequest).ConfigureAwait(false);
            var datastore = (AzureBlobDatastore)createdDatastore.Properties;

            createdDatastore.Id.Should().BeEquivalentTo($"/subscriptions/{Fixture.SubscriptionId}/resourceGroups/{Fixture.ResourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{Fixture.WorkspaceName}/datastores/{datastoreName}");
            createdDatastore.Name.Should().BeEquivalentTo(datastoreName);
            createdDatastore.Type.Should().Be("Microsoft.MachineLearningServices/workspaces/datastores");
            createdDatastore.Properties.DatastoreType.Should().Be(DatastoreType.AzureBlob);
            datastore.SubscriptionId.Should().Be(subscriptionId);
            datastore.ResourceGroup.Should().Be(resourceGroup);
            datastore.AccountName.Should().Be(storageAccountName);
            datastore.ContainerName.Should().Be("azureml");
            datastore.Credentials.CredentialsType.Should().Be(storageKey != null ? CredentialsType.AccountKey : CredentialsType.None);

            if (datastore.Credentials is AccountKeyDatastoreCredentials accountKeyCreds)
            {
                accountKeyCreds.Secrets.Should().BeNull();
            }

            datastore.Endpoint.Should().Be("core.windows.net");
            datastore.Protocol.Should().Be("https");
            createdDatastore.Properties.Description.Should().Be("description");
            createdDatastore.Properties.IsDefault.Should().BeFalse();
            createdDatastore.Properties.Tags.Should().BeEquivalentTo(datastoreRequest.Properties.Tags);
            createdDatastore.SystemData.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, MfeTestingConstants.DatastoreControllerTimeout.Minutes());
            createdDatastore.SystemData.CreatedBy.Should().NotBeNullOrEmpty();
            createdDatastore.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);
            createdDatastore.SystemData.LastModifiedAt.Should().BeCloseTo(DateTime.UtcNow, MfeTestingConstants.DatastoreControllerTimeout.Minutes());
            createdDatastore.SystemData.LastModifiedBy.Should().NotBeNullOrEmpty();
            createdDatastore.SystemData.LastModifiedByType.Should().Be(SystemData.UserType.Application);

            return createdDatastore;
        }

        private async Task TestDelete(string datastoreName)
        {
            await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName).ConfigureAwait(false);
            Func<Task> retrievalTask = () => Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName);
            (await retrievalTask.Should().ThrowAsync<ServiceInvocationException>()).Which.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        private async Task<Resource<Datastore>> TestGet(string datastoreName, Resource<Datastore> createdDatastore)
        {
            var retrievedDatastore = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName).ConfigureAwait(false);
            retrievedDatastore.Should().BeEquivalentTo(createdDatastore, options => options.UsingCaseInsensitiveProperties());
            if (retrievedDatastore.Properties.Credentials is AccountKeyDatastoreCredentials accountKeyCreds)
            {
                accountKeyCreds.Secrets.Should().BeNull();
            }
            return retrievedDatastore;
        }

        private async Task<Resource<Datastore>> TestGetDefault(string datastoreName)
        {
            var defaultDatastores = await ListDatastores(isDefault: true).ConfigureAwait(false);

            if (defaultDatastores.Count == 0)
            {
                return null;
            }
            else
            {
                defaultDatastores.Should().HaveCount(1);
                defaultDatastores[0].Name.Should().NotBe(datastoreName);

                return defaultDatastores[0];
            }
        }

        private async Task<DatastoreSecrets> TestGetSecrets(string datastoreName, string storageKey)
        {
            var retrievedSecret = await Controller.GetSecrets(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName).ConfigureAwait(false);
            retrievedSecret.SecretsType.Should().Be(SecretsType.AccountKey);
            var accountKeySecrets = (AccountKeyDatastoreSecrets)retrievedSecret;
            accountKeySecrets.Key.Should().Be(storageKey);
            return retrievedSecret;
        }

        private async Task<IList<Resource<Datastore>>> TestList(string datastoreName, Resource<Datastore> retrievedDatastore)
        {
            Func<EquivalencyAssertionOptions<Resource<Datastore>>, EquivalencyAssertionOptions<Resource<Datastore>>> configOptions
                = options => options.Excluding(x => x.SystemData).UsingCaseInsensitiveProperties();

            var allDatastores = await ListDatastores().ConfigureAwait(false);
            allDatastores.Should().NotBeEmpty();
            allDatastores.First(x => x.Name == datastoreName).Should().BeEquivalentTo(retrievedDatastore, configOptions);

            var allDatastoresOrderByAsc = await ListDatastores(orderBy: "name", orderByAsc: true).ConfigureAwait(false);
            allDatastoresOrderByAsc.Should().NotBeEmpty();
            allDatastoresOrderByAsc.First(x => x.Name == datastoreName).Should().BeEquivalentTo(retrievedDatastore, configOptions);
            allDatastoresOrderByAsc.Should().BeInAscendingOrder(x => x.Name);

            var allDatastoresOrderByDesc = await ListDatastores(orderBy: "name", orderByAsc: false).ConfigureAwait(false);
            allDatastoresOrderByDesc.Should().NotBeEmpty();
            allDatastoresOrderByDesc.First(x => x.Name == datastoreName).Should().BeEquivalentTo(retrievedDatastore, configOptions);
            allDatastoresOrderByDesc.Should().BeInDescendingOrder(x => x.Name);

            var oneDatastoreByName = await ListDatastores(names: new[] { datastoreName }).ConfigureAwait(false);
            oneDatastoreByName.Count.Should().Be(1);
            oneDatastoreByName[0].Should().BeEquivalentTo(retrievedDatastore, options => configOptions(options));

            var oneDatastoreBySearchText = await ListDatastores(searchText: datastoreName[5..]).ConfigureAwait(false);
            oneDatastoreBySearchText.Count.Should().Be(1);
            oneDatastoreBySearchText[0].Should().BeEquivalentTo(retrievedDatastore, configOptions);

            Func<Task> noDatastoresByNameFunc = () => ListDatastores(names: new[] { Guid.NewGuid().ToString() });
            (await noDatastoresByNameFunc.Should().ThrowAsync<ServiceInvocationException>()).Which.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);

            var noDatastoresBySearchText = await ListDatastores(searchText: Guid.NewGuid().ToString()).ConfigureAwait(false);
            noDatastoresBySearchText.Should().BeEmpty();

            return allDatastores;
        }

        // Most of the assertions in this test are conditional because the default datastore is workspace-level global state,
        // so there are race conditions in the E2E environnment (but the isolated tests should exercise everything).
        private async Task<Resource<Datastore>> TestSetDefault(string datastoreName, string storageKey, Resource<Datastore> createdDatastore)
        {
            Func<EquivalencyAssertionOptions<Resource<Datastore>>, EquivalencyAssertionOptions<Resource<Datastore>>> configOptions = options => options
                .Excluding(x => x.Properties.IsDefault)
                .Excluding(x => x.SystemData)
                .UsingCaseInsensitiveProperties();

            var updateRequest = new Resource<Datastore>
            {
                Properties = JsonConvert.DeserializeObject<Datastore>(JsonConvert.SerializeObject(createdDatastore.Properties, _serializerSettings), _serializerSettings),
            };

            updateRequest.Properties.IsDefault.Should().BeFalse();

            updateRequest.Properties.DatastoreType.Should().Be(DatastoreType.AzureBlob);
            ((AzureBlobDatastore)updateRequest.Properties).Credentials = new AccountKeyDatastoreCredentials() { Secrets = new AccountKeyDatastoreSecrets { Key = storageKey } };
            updateRequest.Properties.IsDefault = true;

            await Controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName, updateRequest).ConfigureAwait(false);

            var defaultDatastores = await ListDatastores(isDefault: true).ConfigureAwait(false);
            if (defaultDatastores.Count > 0)
            {
                defaultDatastores.Should().HaveCount(1);
                defaultDatastores[0].Properties.IsDefault.Should().BeTrue();

                if (string.Equals(defaultDatastores[0].Name, datastoreName, StringComparison.OrdinalIgnoreCase))
                {
                    defaultDatastores[0].Should().BeEquivalentTo(createdDatastore, configOptions);
                }
            }

            var defaultDatastoresByName = await ListDatastores(isDefault: true, names: new[] { datastoreName }).ConfigureAwait(false);
            if (defaultDatastoresByName.Count > 0)
            {
                defaultDatastoresByName.Should().HaveCount(1);
                defaultDatastoresByName[0].Name.Should().BeEquivalentTo(datastoreName);
            }

            var defaultDatastoresBySearchText = await ListDatastores(isDefault: true, searchText: datastoreName.ToUpperInvariant()).ConfigureAwait(false);
            if (defaultDatastoresBySearchText.Count > 0)
            {
                defaultDatastoresBySearchText.Should().HaveCount(1);
                defaultDatastoresBySearchText[0].Name.Should().BeEquivalentTo(datastoreName);
            }

            var noDefaultDatastoresByName = await ListDatastores(isDefault: true, names: new[] { Guid.NewGuid().ToString() }).ConfigureAwait(false);
            noDefaultDatastoresByName.Should().BeEmpty();

            var retrievedDefaultDatastore = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName).ConfigureAwait(false);

            if (defaultDatastores.Count > 0 && string.Equals(retrievedDefaultDatastore.Name, defaultDatastores[0].Name, StringComparison.OrdinalIgnoreCase))
            {
                retrievedDefaultDatastore.Should().BeEquivalentTo(defaultDatastores[0], configOptions);
            }

            return retrievedDefaultDatastore;
        }

        private async Task<Resource<Datastore>> TestUpdate(string datastoreName, string storageKey, Resource<Datastore> defaultDatastore)
        {
            var updateRequest = new Resource<Datastore>
            {
                Properties = JsonConvert.DeserializeObject<Datastore>(JsonConvert.SerializeObject(defaultDatastore.Properties, _serializerSettings), _serializerSettings),
            };

            updateRequest.Properties.DatastoreType.Should().Be(DatastoreType.AzureBlob);
            ((AzureBlobDatastore)updateRequest.Properties).Credentials = new AccountKeyDatastoreCredentials() { Secrets = new AccountKeyDatastoreSecrets { Key = storageKey } };
            updateRequest.Properties.Description = "new-description";

            var updatedDatastore = await Controller.CreateOrUpdate(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, datastoreName, updateRequest).ConfigureAwait(false);
            updatedDatastore.Should().BeEquivalentTo(defaultDatastore, options => options.Excluding(x => x.Properties.Description).Excluding(x => x.SystemData).UsingCaseInsensitiveProperties());
            updatedDatastore.SystemData.LastModifiedAt.Should().BeCloseTo(DateTime.UtcNow, MfeTestingConstants.DatastoreControllerTimeout.Minutes());
            updatedDatastore.SystemData.LastModifiedBy.Should().NotBeNullOrEmpty();
            updatedDatastore.SystemData.LastModifiedByType.Should().Be(SystemData.UserType.Application);

            return updatedDatastore;
        }
        #endregion
    }
}
