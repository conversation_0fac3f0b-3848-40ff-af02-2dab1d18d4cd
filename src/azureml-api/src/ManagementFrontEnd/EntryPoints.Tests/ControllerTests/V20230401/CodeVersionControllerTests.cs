﻿// <copyright file="CodeVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FluentAssertions;
using FluentAssertions.Extensions;
using Microsoft.Azure.Storage.Blob;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401
{
    [MfeTest(TestCategory.Component, ScenarioScope.Code)]
    public class E2ECodeVersionControllerTests : CodeVersionControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2ECodeVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class CodeVersionControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public CodeVersionControllerTests(TFixture fixture)
        {
            Fixture = fixture;
            // TODO: Fix base class to use a non-deleted workspace
            fixture.WorkspaceId = Guid.Parse("fc481377-1aab-49ec-aa35-cd5b34898f1a");
            fixture.WorkspaceName = "servicestestworkspace02";

            CodeContainerController = Fixture.ManagementFrontEndClient.GetController<ICodeContainerController>();
            CodeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>();
        }

        public ICodeVersionController CodeVersionController { get; }

        public ICodeContainerController CodeContainerController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestCreateGetUpdateDeleteWithContainerSucceeds()
        {
            var paths = new List<string>() { "test1", "subdir1/test2", "subdir1/subdir2/test3" };
            var name = Guid.NewGuid().ToString();
            var version = "1";
            string hash = Guid.NewGuid().ToString();
            string hashVersion = "202208";
            await TestGetByHashEmpty(hash, hashVersion);
            var created = await this.TestCreateSucceeds(name, version, paths, hash: hash, hashVersion: hashVersion).ConfigureAwait(false);
            await TestGetSucceeds(name, version, created).ConfigureAwait(false);
            await TestGetByHashSucceeds(hash, hashVersion, created).ConfigureAwait(false);
            await TestUpdateSucceeds(name, version, created).ConfigureAwait(false);
            await TestAcrossApiVersions(name, version).ConfigureAwait(false);
            await TestDeleteSucceeds(name, version).ConfigureAwait(false);
        }

        [Fact]
        public async Task TestCreateGetUpdateDeleteWithSubdirectorySucceeds()
        {
            var paths = new List<string>() { "test1", "subdir1/test2", "subdir1/subdir2/test3" };
            var name = Guid.NewGuid().ToString();
            var version = "1";
            string hash = Guid.NewGuid().ToString();
            string hashVersion = "202208";
            await TestGetByHashEmpty(hash, hashVersion);
            var created = await this.TestCreateSucceeds(name, version, paths, "subdir1/subdir2", hash: hash, hashVersion: hashVersion).ConfigureAwait(false);
            await TestGetSucceeds(name, version, created).ConfigureAwait(false);
            await TestGetByHashSucceeds(hash, hashVersion, created).ConfigureAwait(false);
            await TestUpdateSucceeds(name, version, created).ConfigureAwait(false);
            await TestAcrossApiVersions(name, version).ConfigureAwait(false);
            await TestDeleteSucceeds(name, version).ConfigureAwait(false);
        }

        public async Task TestDeleteSucceeds(string name, string version)
        {
            await CodeVersionController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version).ConfigureAwait(false);

            Func<Task> task = () => CodeVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version);

            (await task.Should().ThrowAsync<ServiceInvocationException>()).Which.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        public async Task<Resource<CodeVersion>> TestGetSucceeds(string name, string version, Resource<CodeVersion> expected)
        {
            var result = await CodeVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version).ConfigureAwait(false);

            result.Should().BeEquivalentTo(expected, options => options.UsingCaseInsensitiveProperties());

            return result;
        }

        public async Task TestGetByHashEmpty(string hash, string hashVersion)
        {
            // Before we have created the snapshot, there should be no snapshots matching this hash
            var list = await CodeVersionController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                "placeholder",
                hash: hash,
                hashVersion: hashVersion).ConfigureAwait(false);
            list.Value.Count.Should().Be(0);
        }

        public async Task<Resource<CodeVersion>> TestGetByHashSucceeds(string hash, string hashVersion, Resource<CodeVersion> expected)
        {
            var list = await CodeVersionController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                "placeholder",
                hash: hash,
                hashVersion: hashVersion).ConfigureAwait(false);
            list.Value.Count.Should().Be(1);
            list.Value.First().Should().BeEquivalentTo(expected, options => options.UsingCaseInsensitiveProperties());

            return list.Value.First();
        }

        public async Task TestUpdateSucceeds(string name, string version, Resource<CodeVersion> current)
        {
            // Update allowed properties
            current.Properties.Properties.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());
            current.Properties.Tags.Add(Guid.NewGuid().ToString(), Guid.NewGuid().ToString());
            current.Properties.Description = "new description";

            var updated = await CodeVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version,
                current).ConfigureAwait(false);

            updated.Should().BeEquivalentTo(current, options => options.Excluding(x => x.SystemData).UsingCaseInsensitiveProperties());
            updated.SystemData.CreatedAt.Should().Be(current.SystemData.CreatedAt);
            updated.SystemData.CreatedBy.Should().Be(current.SystemData.CreatedBy);
            updated.SystemData.CreatedByType.Should().Be(current.SystemData.CreatedByType);
            updated.SystemData.LastModifiedAt.Should().BeAfter((DateTimeOffset)updated.SystemData.CreatedAt);
            updated.SystemData.LastModifiedBy.Should().Be(current.SystemData.CreatedBy);
            updated.SystemData.LastModifiedByType.Should().Be(current.SystemData.LastModifiedByType);
        }

        /// <summary>
        /// Creates or Updates CodeVersion. Generates Guids for blobUrl, tags, and properties.
        /// Validates returns the correct information.
        /// </summary>
        /// <param name="name">The name of the CodeVersion to create/update.</param>
        /// <returns>The resulting CodeVersion</returns>
        public async Task<Resource<CodeVersion>> TestCreateSucceeds(
            string name,
            string version,
            IEnumerable<string> paths = null,
            string subdir = null,
            string hash = null,
            string hashVersion = null)
        {
            var storageConnectionDto = await Fixture.GetStorageConnection().ConfigureAwait(false);
            var blobContainer = await UploadBlobsWithPendingUpload(name, version, paths);

            var request = new Resource<CodeVersion>()
            {
                Properties = new CodeVersion()
                {
                    CodeUri = blobContainer.Uri.ToString() + (subdir == null ? string.Empty : "/" + subdir),
                    Tags = new Dictionary<string, string>()
                    {
                        { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() }
                    },
                    Properties = new Dictionary<string, string>()
                    {
                        { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() },
                        { MfeConstants.CodeVersionContentHashProperty, hash },
                        { MfeConstants.CodeVersionHashVersionProperty, hashVersion }
                    }
                }
            };

            var result = await CodeVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                name,
                version,
                request).ConfigureAwait(false);

            var armId = MfeResourceVersionArmScope.ToString(Fixture.GetTestWorkspaceContext2(), MfeConstants.ArmTypeCodeContainer, name, version);
            result.Id.Should().BeEquivalentTo(armId);
            result.Properties.Should().BeEquivalentTo(request.Properties, options => options.UsingCaseInsensitiveProperties());
            result.SystemData.CreatedAt.Should().BeCloseTo(DateTimeOffset.UtcNow, 1.Minutes());
            result.SystemData.CreatedBy.Should().NotBeNullOrWhiteSpace();
            result.SystemData.CreatedByType.Should().Be(SystemData.UserType.Application);
            result.SystemData.LastModifiedAt.Should().Be(result.SystemData.CreatedAt);
            result.SystemData.LastModifiedBy.Should().Be(result.SystemData.CreatedBy);
            result.SystemData.LastModifiedByType.Should().Be(SystemData.UserType.Application);

            return result;
        }

        protected static Resource<CodeContainer> GenerateContainer()
        {
            return new Resource<CodeContainer>
            {
                Properties = new CodeContainer
                {
                    Description = "description",
                },
            };
        }

        private async Task<CloudBlobContainer> UploadBlobsWithPendingUpload(string codeName, string codeVersion, IEnumerable<string> paths = null)
        {
            var request = new PendingUploadRequestDto
            {
                PendingUploadType = PendingUploadType.TemporaryBlobReference
            };

            var response = await CodeVersionController.CreateOrGetStartPendingUpload(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.WorkspaceName,
                codeName,
                codeVersion,
                request);

            Assert.True(response.BlobReferenceForConsumption.Credential is SASCredentialDto);
            var containerSas = ((SASCredentialDto)response.BlobReferenceForConsumption.Credential).SASUri;
            var containerReference = new CloudBlobContainer(containerSas);

            foreach (var path in paths)
            {
                var fileName = $"{path}.txt";

                CloudBlockBlob cloudBlockBlob = containerReference.GetBlockBlobReference(fileName);
                await cloudBlockBlob.UploadTextAsync("Hello World!").ConfigureAwait(false);
            }

            return containerReference;
        }

        private async Task TestAcrossApiVersions(string name, string version)
        {
            await CrossVersionHelpers.TestResourceAcrossApiVersions<ICodeVersionController>(
                    Fixture,
                    controller => controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, name, version),
                    version => version >= MfeApiVersions.Oct2021 && version != MfeApiVersions.Oct2021DataplanePreview,
                    tryList: false) // Not implemented for code versions.
                .ConfigureAwait(false);
        }
    }
}
