﻿// <copyright file="BatchDeploymentControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.NonVersionedHelpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401
{
    [MfeTest(TestCategory.Integration, ScenarioScope.BatchServices)]
    public class E2EBatchDeploymentControllerTests : IStaticFixture<TestFixtureManagementFrontEnd>, IAsyncLifetime
    {
        private readonly IBatchEndpointController _endpointController;
        private readonly ICodeVersionController _codeVersionController;
        private readonly IList<string> _codeVersionTracker;

        public E2EBatchDeploymentControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
        {
            Fixture = fixture;
            Controller = Fixture.ManagementFrontEndClient.GetController<IBatchDeploymentController>();
            _endpointController = Fixture.ManagementFrontEndClient.GetController<IBatchEndpointController>();
            _codeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>();
            _codeVersionTracker = new List<string>();
        }

        public IBatchDeploymentController Controller { get; }

        public TestFixtureManagementFrontEnd Fixture { get; }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task BatchDeploymentE2EListSucceeds(bool isAnonymousModel)
        {
            // Setup
            var codeAsset = await EndpointHelpers.UploadAndRegisterScoringFile(Fixture, _codeVersionController, _codeVersionTracker).ConfigureAwait(false);

            // Test List on container that does not exist.
            Func<Task> func = () => TestHelpers.RetryUntilExceptionAsync(
                () => Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, Guid.NewGuid().ToString()),
                TimeSpan.FromSeconds(15));
            (await func.Should().ThrowAsync<ServiceInvocationException>()).And.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);

            // Test List on container with 0 versions.
            var endpoint = await BatchEndpointHelpers.CreateTestEndpoint(_endpointController, Fixture).ConfigureAwait(false);
            var deployments = await Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name).ConfigureAwait(false);
            deployments.Value.Should().BeEmpty();

            // Test List on container with at least 1 version.
            await BatchEndpointHelpers.CreateTestEndpointDeployment(Controller, Fixture, endpoint.Name, codeAsset.Id, isAnonymousModel).ConfigureAwait(false);
            deployments = await Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name).ConfigureAwait(false);
            deployments.Value.Should().ContainSingle();
        }

        [Theory]
        [InlineData("PUT", true)]
        [InlineData("PUT", false)]
        [InlineData("PATCH", true)]
        [InlineData("PATCH", false)]
        public async Task BatchDeploymentE2ECreateUpdateSucceeds(string updateType, bool isAnonymousModel)
        {
            var codeAsset = await EndpointHelpers.UploadAndRegisterScoringFile(Fixture, _codeVersionController, _codeVersionTracker).ConfigureAwait(false);
            var endpoint = await BatchEndpointHelpers.CreateTestEndpoint(_endpointController, Fixture).ConfigureAwait(false);

            var deployment = await BatchEndpointHelpers.CreateTestEndpointDeployment(Controller, Fixture, endpoint.Name, codeAsset.Id, isAnonymousModel).ConfigureAwait(false);
            deployment.Should().NotBeNull();

            var newDescription = deployment.Properties.Description = "new description";
            string tagKey = "tagKey1";
            string tagValue = "tagValue1";
            var updatedDeployment = await BatchEndpointHelpers.UpdateDeployment(
                Controller,
                Fixture,
                endpoint.Name,
                deployment.Name,
                updateType,
                deployment,
                tags: new CaseInsensitiveDictionary(new Dictionary<string, string> { { tagKey, tagValue } }),
                newDescription).ConfigureAwait(false);
            updatedDeployment.Should().NotBeNull();
            updatedDeployment.Properties.Description.Should().Be(newDescription);
            if (updatedDeployment.Properties.CodeConfiguration != null)
            {
                updatedDeployment.Properties.CodeConfiguration.ScoringScript.Should().Be(deployment.Properties.CodeConfiguration.ScoringScript);
                updatedDeployment.Properties.CodeConfiguration.CodeId.Should().BeEquivalentTo(deployment.Properties.CodeConfiguration.CodeId);
            }

            updatedDeployment.Tags.ContainsKey(tagKey);
            updatedDeployment.Tags[tagKey].Should().Be(tagValue);

            var updatedEndpoint = await BatchEndpointHelpers.UpdateTestEndpoint(_endpointController, Fixture, endpoint.Name, updatedDeployment.Name).ConfigureAwait(false);
            updatedEndpoint.Should().NotBeNull();

            var deploymentResult = await Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name, deployment.Name).ConfigureAwait(false);
            deploymentResult.Should().NotBeNull();

            var result = await Controller.List(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name).ConfigureAwait(false);

            result.Value.Should().NotBeNull();
            result.Value.Count.Should().Be(1);
            result.Value.First().Name.Should().BeEquivalentTo(deployment.Name);
            result.Value.First().Id.Should().BeEquivalentTo(deployment.Id);
            result.Value.First().Type.Should().Be(deployment.Type);
            result.Value.First().Properties.EnvironmentVariables.Count.Should().Be(1);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task BatchDeploymentE2EDeleteSucceeds(bool isAnonymousModel)
        {
            var codeAsset = await EndpointHelpers.UploadAndRegisterScoringFile(Fixture, _codeVersionController, _codeVersionTracker).ConfigureAwait(false);
            var endpoint = await BatchEndpointHelpers.CreateTestEndpoint(_endpointController, Fixture).ConfigureAwait(false);

            var deployment = await BatchEndpointHelpers.CreateTestEndpointDeployment(Controller, Fixture, endpoint.Name, codeAsset.Id, isAnonymousModel).ConfigureAwait(false);
            deployment.Should().NotBeNull();

            await Controller.Delete(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name, deployment.Name).ConfigureAwait(false);
            Func<Task> call = () => TestHelpers.RetryUntilExceptionAsync(
                () => Controller.Get(Fixture.SubscriptionId, Fixture.ResourceGroupName, Fixture.WorkspaceName, endpoint.Name, deployment.Name),
                BatchEndpointTestConstants.RetryIntervalUntilNotFoundAfterResourceDeletion,
                BatchEndpointTestConstants.MaxRetriesUntilNotFoundAfterResourceDeletion);
            (await call.Should().ThrowAsync<ServiceInvocationException>()).And.Response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        public async Task InitializeAsync()
        {
            await BatchEndpointHelpers.CleanUpEndpoints(_endpointController, Fixture).ConfigureAwait(false);
        }

        public async Task DisposeAsync()
        {
            await EndpointHelpers.CleanupCodeAssets(Fixture, _codeVersionController, _codeVersionTracker).ConfigureAwait(false);
        }
    }
}
