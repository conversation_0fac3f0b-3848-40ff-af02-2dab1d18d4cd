﻿// <copyright file="ModelVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.DataStore.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Controllers.Registries;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api.V20230401;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401.Registries
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Models)]
    public class E2EModelVersionControllerTests : ModelVersionControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2EModelVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class ModelVersionControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public ModelVersionControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            ModelContainerController = Fixture.ManagementFrontEndClient.GetController<IModelContainerController>(randomizeCase: false);
            ModelVersionController = Fixture.ManagementFrontEndClient.GetController<IModelVersionController>(randomizeCase: false);
            DatastoreController = ServiceInvoker.Create<IDataStoreController>(
                fixture.HttpClient,
                new Uri(fixture.GetHostService<IOptions<ExperimentationHostConfiguration>>().Value.ExperimentationHostUri));
        }

        public IModelVersionController ModelVersionController { get; }

        public IModelContainerController ModelContainerController { get; }

        public IDataStoreController DatastoreController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestModelContainerCreateGetUpdateListDelete()
        {
            var name = $"MfeRegistryModel_{Guid.NewGuid()}";
            var modelContainer = new ModelContainer()
            {
                Description = "description",
                IsArchived = false,
                Tags = new Dictionary<string, string>() { { "tagKey1", "tagValue1" } }
            };
            var newModelContainer = new ModelContainer()
            {
                Description = "new description",
                IsArchived = false,
                Tags = new Dictionary<string, string>() { { "newTagKey1", "newTagValue1" } }
            };

            var response = await ModelContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                new Resource<ModelContainer>() { Properties = modelContainer }).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

            var getModelContainer = await ModelContainerController.Get(
                 Fixture.SubscriptionId,
                 Fixture.ResourceGroupName,
                 Fixture.RegistryName,
                 name).ConfigureAwait(false);
            Assert.NotNull(getModelContainer);
            Assert.Equal(getModelContainer.Name, name);
            Assert.Equal(getModelContainer.Properties.Description, modelContainer.Description);
            Assert.Equal(getModelContainer.Properties.IsArchived, modelContainer.IsArchived);
            getModelContainer.Properties.Tags.Should().BeEquivalentTo(modelContainer.Tags);

            await ModelContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                new Resource<ModelContainer>() { Properties = newModelContainer }).ConfigureAwait(false);

            getModelContainer = await PollHelper.PollGetUntilComplete(
                () => ModelContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name)).ConfigureAwait(false);
            Assert.NotNull(getModelContainer);
            Assert.Equal(getModelContainer.Name, name);
            Assert.Equal(getModelContainer.Properties.Description, newModelContainer.Description);
            Assert.Equal(getModelContainer.Properties.IsArchived, newModelContainer.IsArchived);
            getModelContainer.Properties.Tags.Should().BeEquivalentTo(newModelContainer.Tags);

            response = await ModelContainerController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
        }

        [Fact]
        public async Task TestModelVersionCreateGetUpdateListDelete()
        {
            var modelName = $"MfeArmRegistryModel_{Guid.NewGuid()}";
            var modelVersion = "1";
            var model = new ModelVersion()
            {
                Description = "description",
                Tags = new Dictionary<string, string>() { { "tagKey1", "tagValue1" } },
                Properties = new Dictionary<string, string>() { { "propKey1", "propValue1" } },
                IsArchived = false,
            };

            var newModel = new ModelVersion()
            {
                Description = "new description",
                Tags = new Dictionary<string, string>() { { "tagKey1New", "tagValue1New" } },
                Properties = new Dictionary<string, string>() { { "propKey1", "propValue1" }, { "propKey1New", "propValue1New" } },
                IsArchived = false,
            };

            await CreateRegistryModel(modelName, modelVersion, model).ConfigureAwait(false);

            var getModel = await ModelVersionController.Get(
                 Fixture.SubscriptionId,
                 Fixture.ResourceGroupName,
                 Fixture.RegistryName,
                 modelName,
                 modelVersion).ConfigureAwait(false);
            Assert.NotNull(getModel);
            Assert.Equal(getModel.Name, modelName);
            Assert.Equal(getModel.Properties.Description, model.Description);
            Assert.Equal(getModel.Properties.IsArchived, model.IsArchived);
            getModel.Properties.Tags.Should().BeEquivalentTo(model.Tags);
            getModel.Properties.Properties.Should().BeEquivalentTo(model.Properties);

            newModel.ModelUri = getModel.Properties.ModelUri;
            var response = await ModelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion,
                new Resource<ModelVersion>() { Properties = newModel }).ConfigureAwait(false);

            getModel = await PollHelper.PollGetUntilComplete(
                () => ModelVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion)).ConfigureAwait(false);
            Assert.Equal(AssetProvisioningState.Succeeded, getModel.Properties.ProvisioningState);
            Assert.NotNull(getModel);
            Assert.Equal(getModel.Name, modelName);
            Assert.Equal(getModel.Properties.Description, newModel.Description);
            Assert.Equal(getModel.Properties.IsArchived, newModel.IsArchived);
            getModel.Properties.Tags.Should().BeEquivalentTo(newModel.Tags);
            getModel.Properties.Properties.Should().BeEquivalentTo(newModel.Properties);

            response = await ModelVersionController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
        }

        [Fact]
        public async Task TestModelVersionCreateThenDelete_VersionIsTombstoned()
        {
            var modelName = $"MfeArmRegistryModel_{Guid.NewGuid()}";
            var modelVersion = "1";
            var model = new ModelVersion()
            {
                Description = "description",
                Tags = new Dictionary<string, string>() { { "tagKey1", "tagValue1" } },
                Properties = new Dictionary<string, string>() { { "propKey1", "propValue1" } },
                IsArchived = false,
            };

            await CreateRegistryModel(modelName, modelVersion, model).ConfigureAwait(false);

            var getModel = await ModelVersionController.Get(
                 Fixture.SubscriptionId,
                 Fixture.ResourceGroupName,
                 Fixture.RegistryName,
                 modelName,
                 modelVersion).ConfigureAwait(false);
            Assert.NotNull(getModel);
            Assert.Equal(getModel.Name, modelName);
            Assert.Equal(getModel.Properties.Description, model.Description);
            Assert.Equal(getModel.Properties.IsArchived, model.IsArchived);
            getModel.Properties.Tags.Should().BeEquivalentTo(model.Tags);
            getModel.Properties.Properties.Should().BeEquivalentTo(model.Properties);

            var response = await ModelVersionController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion).ConfigureAwait(false);
            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);

            var getModelAction = () => ModelVersionController.Get(
                 Fixture.SubscriptionId,
                 Fixture.ResourceGroupName,
                 Fixture.RegistryName,
                 modelName,
                 modelVersion);
            await getModelAction.Should().ThrowAsync<ServiceInvocationException>().Where(sie => sie.Response.StatusCode == System.Net.HttpStatusCode.NotFound).ConfigureAwait(false);

            var createModelAction = () => ModelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion,
                new Resource<ModelVersion> { Properties = model });
            await createModelAction.Should().ThrowAsync<ServiceInvocationException>().Where(sie => sie.Response.StatusCode == System.Net.HttpStatusCode.BadRequest).ConfigureAwait(false);
        }

        [Fact]
        public async Task TestIpProtectedPresetFormattedModelVersionCreateGetUpdateListDelete()
        {
            var modelName = $"MfeArmRegistryIpProtectedPresetFormattedModel_{Guid.NewGuid()}";
            var modelVersion = "1";
            var model = new ModelVersion
            {
                Description = "Test MFE direct publish of IP-protected preset-formatted model to a registry",
                ModelType = ModelTypeHidden.presets_model.ToString(),
                ModelUri = "https://modelpresetsa.blob.core.windows.net/static-test/",
                Properties = new Dictionary<string, string>
                {
                    { "engineControllerManifestPath", "modelregistry/registrymodelcontrollertests/engine.manifest.json" },
                    { "intellectualPropertyPublisher", "contoso" },
                    { "componentVersion", "1" },
                    { "propKey1", "propValue1" }
                },
                Tags = new Dictionary<string, string>() { { "tagKey1", "tagValue1" } },
                IsArchived = false,
            };

            var newModel = new ModelVersion()
            {
                Description = "Test MFE direct publish of IP-protected preset-formatted model to a registry - updated",
                ModelType = ModelTypeHidden.presets_model.ToString(),
                ModelUri = "https://modelpresetsa.blob.core.windows.net/static-test/",
                Properties = new Dictionary<string, string>
                {
                    { "engineControllerManifestPath", "modelregistry/registrymodelcontrollertests/engine.manifest.json" },
                    { "intellectualPropertyPublisher", "contoso" },
                    { "componentVersion", "1" },
                    { "propKey1", "propValue1" },
                    { "propKey1New", "propValue1New" }
                },
                Tags = new Dictionary<string, string>() { { "tagKey1New", "tagValue1New" } },
                IsArchived = false,
            };

            bool isCreated = false;
            try
            {
                await CreateRegistryIpProtectedPresetFormattedModel(modelName, modelVersion, model).ConfigureAwait(false);
                isCreated = true;

                var getModel = await ModelVersionController.Get(
                     Fixture.SubscriptionId,
                     Fixture.ResourceGroupName,
                     Fixture.IpProtectedRegistryName,
                     modelName,
                     modelVersion).ConfigureAwait(false);
                Assert.NotNull(getModel);
                Assert.Equal(getModel.Name, modelName);
                Assert.Equal(getModel.Properties.Description, model.Description);
                Assert.Equal(getModel.Properties.IsArchived, model.IsArchived);
                getModel.Properties.Tags.Should().BeEquivalentTo(model.Tags);
                getModel.Properties.Properties.Should().BeEquivalentTo(model.Properties);

                newModel.ModelUri = getModel.Properties.ModelUri;
                var response = await ModelVersionController.CreateOrUpdate(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.IpProtectedRegistryName,
                    modelName,
                    modelVersion,
                    new Resource<ModelVersion>() { Properties = newModel }).ConfigureAwait(false);

                getModel = await PollHelper.PollGetUntilComplete(
                    () => ModelVersionController.Get(
                    Fixture.SubscriptionId,
                    Fixture.ResourceGroupName,
                    Fixture.IpProtectedRegistryName,
                    modelName,
                    modelVersion)).ConfigureAwait(false);
                Assert.Equal(AssetProvisioningState.Succeeded, getModel.Properties.ProvisioningState);
                Assert.NotNull(getModel);
                Assert.Equal(getModel.Name, modelName);
                Assert.Equal(getModel.Properties.Description, newModel.Description);
                Assert.Equal(getModel.Properties.IsArchived, newModel.IsArchived);
                getModel.Properties.Tags.Should().BeEquivalentTo(newModel.Tags);
                getModel.Properties.Properties.Should().BeEquivalentTo(newModel.Properties);
            }
            finally
            {
                if (isCreated)
                {
                    var response = await ModelVersionController.Delete(
                        Fixture.SubscriptionId,
                        Fixture.ResourceGroupName,
                        Fixture.IpProtectedRegistryName,
                        modelName,
                        modelVersion).ConfigureAwait(false);
                    await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
                }
            }
        }

        private async Task CreateRegistryModel(string modelName, string modelVersion, ModelVersion model)
        {
            var pendingUploadResponse = await ModelVersionController.CreateOrGetStartPendingUpload(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion,
                new PendingUploadRequestDto { PendingUploadType = PendingUploadType.TemporaryBlobReference }).ConfigureAwait(false);

            var feedCredentials = pendingUploadResponse.BlobReferenceForConsumption.Credential;
            var sasCredential = (SASCredentialDto)feedCredentials;

            var containerSas = sasCredential.SASUri;
            var containerReference = new CloudBlobContainer(containerSas);

            var cloudBlockBlob = containerReference.GetBlockBlobReference($"file1.txt");
            await cloudBlockBlob.UploadTextAsync("file1 content").ConfigureAwait(false);

            var response = await ModelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                modelName,
                modelVersion,
                GetModelForRegistration(
                    pendingUploadResponse.BlobReferenceForConsumption.BlobUri,
                    model.IsArchived,
                    model.Description,
                    model.Tags,
                    model.Properties)).ConfigureAwait(false);

            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
        }

        private async Task CreateRegistryIpProtectedPresetFormattedModel(string modelName, string modelVersion, ModelVersion model)
        {
            // For IP-protected preset-formatted registry models, there are no temporary data references
            // So we skip straight to publish
            var response = await ModelVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.IpProtectedRegistryName,
                modelName,
                modelVersion,
                new Resource<ModelVersion> { Properties = model }).ConfigureAwait(false);

            await PollHelper.PollAsyncOperationUntilComplete(response).ConfigureAwait(false);
        }

        private Resource<ModelVersion> GetModelForRegistration(
            Uri url,
            bool isArchived = false,
            string description = "",
            IDictionary<string, string> kvTags = null,
            IDictionary<string, string> properties = null)
        {
            return new Resource<ModelVersion>
            {
                Properties = new ModelVersion
                {
                    ModelUri = url.AbsoluteUri,
                    Description = description,
                    Tags = kvTags,
                    Properties = properties,
                    IsArchived = isArchived,
                },
            };
        }
    }
}
