﻿// <copyright file="CodeVersionControllerTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Storage.Blob;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.ArmCommon;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.V20230401.Controllers.Registries;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401.Helpers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.ControllerTests.V20230401.Registries
{
    [MfeTest(TestCategory.Integration, ScenarioScope.Code)]
    public class E2ECodeVersionControllerTests : CodeVersionControllerTests<TestFixtureManagementFrontEnd>, IStaticFixture<TestFixtureManagementFrontEnd>
    {
        public E2ECodeVersionControllerTests(StaticFixture<TestFixtureManagementFrontEnd> fixture)
            : base(fixture)
        {
        }
    }

    public abstract class CodeVersionControllerTests<TFixture>
        where TFixture : TestFixtureManagementFrontEnd
    {
        public CodeVersionControllerTests(TFixture fixture)
        {
            Fixture = fixture;

            CodeContainerController = Fixture.ManagementFrontEndClient.GetController<ICodeContainerController>(randomizeCase: false);
            CodeVersionController = Fixture.ManagementFrontEndClient.GetController<ICodeVersionController>(randomizeCase: false);
        }

        public ICodeVersionController CodeVersionController { get; }

        public ICodeContainerController CodeContainerController { get; }

        public TFixture Fixture { get; }

        [Fact]
        public async Task TestCodeContainerCreateGetUpdateListDelete()
        {
            var ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => CodeContainerController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                "testName",
                new Resource<CodeContainer>() { Properties = new CodeContainer() })).ConfigureAwait(false);
            Assert.Contains("CodeContainers_Registry_CreateOrUpdateV2 is not supported", ex.Message, StringComparison.InvariantCulture);

            ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => CodeContainerController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                "testName")).ConfigureAwait(false);
            Assert.Contains("CodeContainers_Registry_Get is not supported", ex.Message, StringComparison.InvariantCulture);

            ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => CodeContainerController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName)).ConfigureAwait(false);
            Assert.Contains("CodeContainers_Registry_List is not supported", ex.Message, StringComparison.InvariantCulture);

            ex = await Assert.ThrowsAsync<ServiceInvocationException>(() => CodeContainerController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                "testName")).ConfigureAwait(false);
            Assert.Contains("CodeContainers_Registry_DeleteV2 is not supported", ex.Message, StringComparison.InvariantCulture);
        }

        [Fact]
        public async Task TestCodeVersionCreateGetUpdateListDelete()
        {
            // Create CodeVersion and poll Azure-AsyncOperation header
            var name = $"MfeRegistryCodeVersion_{Guid.NewGuid()}";
            string version = "1";

            var codeUri = await UploadAndCreateCodeUri(name, version, $"{name}.{version}").ConfigureAwait(false);
            var response = await CodeVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                new Resource<CodeVersion>()
                {
                    Properties = new CodeVersion
                    {
                        CodeUri = codeUri,
                    }
                }).ConfigureAwait(false);
            Assert.True(response != null);

            if (response.Headers.TryGetValues(MfeConstants.AzureAsyncOperation, out var values))
            {
                var asyncOperationUri = values.First();
                var status = await PollHelper.PollAsyncOperationUntilComplete(asyncOperationUri).ConfigureAwait(false);
                Assert.Equal(AssetProvisioningState.Succeeded, status);
            }

            // Create CodeVersion and poll GET()
            name = $"MfeRegistryCodeVersion_{Guid.NewGuid()}";
            codeUri = await UploadAndCreateCodeUri(name, version, $"{name}.{version}").ConfigureAwait(false);
            response = await CodeVersionController.CreateOrUpdate(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version,
                new Resource<CodeVersion>()
                {
                    Properties = new CodeVersion
                    {
                        CodeUri = codeUri,
                    }
                }).ConfigureAwait(false);
            Assert.True(response != null);

            var codeVersion = await PollHelper.PollGetUntilComplete(
                () => CodeVersionController.Get(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name,
                version)).ConfigureAwait(false);
            Assert.NotNull(codeVersion);
            Assert.Equal($"azureml://registries/{Fixture.RegistryName}/codes/{name}/versions/{version}", codeVersion.Id);
            Assert.Equal($"{version}", codeVersion.Name);
            CheckCodeUri(codeUri, codeVersion.Properties.CodeUri);
            Assert.Equal(AssetProvisioningState.Succeeded, codeVersion.Properties.ProvisioningState);

            // List all versions
            var codeVersions = await CodeVersionController.List(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                name).ConfigureAwait(false);
            Assert.Single(codeVersions.Value);
            var firstCodeVersion = codeVersions.Value.First();
            Assert.Equal(codeVersion.Id, firstCodeVersion.Id);
            Assert.Equal(codeVersion.Name, firstCodeVersion.Name);
            CheckCodeUri(codeVersion.Properties.CodeUri, firstCodeVersion.Properties.CodeUri);

            var delResponse = await CodeVersionController.Delete(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                "testName",
                "testVersion").ConfigureAwait(false);
            Assert.Equal(System.Net.HttpStatusCode.NoContent, delResponse.StatusCode);
        }

        private void CheckCodeUri(string firstCodeUri, string secondCodeUri)
        {
            // The CodeUri that is returned from registry service may be on a different storage account then what we
            // started with due to replication.  The path part of the URI should be the same though.
            Assert.Equal(new Uri(firstCodeUri).AbsolutePath, new Uri(secondCodeUri).AbsolutePath);
        }

        private async Task<string> UploadAndCreateCodeUri(string codeName, string codeVersion, string content)
        {
            var request = new PendingUploadRequestDto
            {
                PendingUploadType = PendingUploadType.TemporaryBlobReference
            };

            var response = await CodeVersionController.CreateOrGetStartPendingUpload(
                Fixture.SubscriptionId,
                Fixture.ResourceGroupName,
                Fixture.RegistryName,
                codeName,
                codeVersion,
                request);

            Assert.True(response.BlobReferenceForConsumption.Credential is SASCredentialDto);
            var containerSas = ((SASCredentialDto)response.BlobReferenceForConsumption.Credential).SASUri;
            var containerReference = new CloudBlobContainer(containerSas);

            var cloudBlockBlob = containerReference.GetBlockBlobReference($"file.txt");
            await cloudBlockBlob.UploadTextAsync(content).ConfigureAwait(false);

            return response.BlobReferenceForConsumption.BlobUri.ToString();
        }
    }
}
