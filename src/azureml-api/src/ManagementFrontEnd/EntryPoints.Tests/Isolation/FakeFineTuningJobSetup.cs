// <copyright file="FakeFineTuningJobSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Jobs;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeFineTuningJobSetup
    {
        public static (string Id, FineTuningJob Job) GetTestFineTuningJob(WorkspaceContext2 workspace, string jobId = null, FineTuningJob copyJob = null)
        {
            jobId ??= Guid.NewGuid().ToString();

            var job = new FineTuningJob()
            {
                ExperimentName = copyJob?.ExperimentName ?? Guid.NewGuid().ToString(),
                ComputeConfig = copyJob?.ComputeConfig ?? new ComputeConfig()
                {
                    ComputeId = "compute_id",
                    Resources = new JobResources()
                    {
                        InstanceTypes = new List<string>() { "Standard_DS2_v2" }
                    },
                    QueueSettings = new JobQueueSettings()
                    {
                        JobTier = JobTier.Null
                    }
                },
                Description = copyJob?.Description ?? "Fine-tuning job description",
                Tags = copyJob?.Tags != null ? new Dictionary<string, string>(copyJob.Tags) : 
                    new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Properties = copyJob?.Properties != null ? new Dictionary<string, string>(copyJob.Properties) : 
                    new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Identity = copyJob?.Identity ?? new AmlToken(),
                Status = copyJob?.Status ?? JobStatus.NotStarted,
                DisplayName = Guid.NewGuid().ToString(),
            };
             
            return (jobId, job);
        }
    }
}
