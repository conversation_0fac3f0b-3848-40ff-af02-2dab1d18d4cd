﻿// <copyright file="FakeBlobSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FakeItEasy;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Storage;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeBlobSetup
    {
        // Simulation of cloud blob storage
        private static ConcurrentDictionary<string, List<CloudBlob>> cloudBlobStorage = new ConcurrentDictionary<string, List<CloudBlob>>();

        private static string urlPrefix = "https://fake.storage";

        public static void ConfigureFakes(IBlobContainerClientFactory blobClientFactory, IList<(string ContainerName, string Path)> precreateBlobs = null)
        {
            // Reset cloud blob storage across tests
            cloudBlobStorage = new ConcurrentDictionary<string, List<CloudBlob>>();

            if (precreateBlobs != null)
            {
                foreach (var (containerName, path) in precreateBlobs)
                {
                    AddToFakeCloudBlobStorage(containerName, path);
                }
            }

            A.CallTo(() => blobClientFactory.Create(A<CloudStorageAccount>._, A<string>._))
                .ReturnsLazily((CloudStorageAccount account, string containerName) => CreateFakeBlobClient(containerName));
            A.CallTo(() => blobClientFactory.CreateForWorkspaceStorage(A<WorkspaceContext2>._, A<string>._))
                .ReturnsLazily((WorkspaceContext2 account, string containerName) => CreateFakeBlobClient(containerName));
        }

        public static void AddToFakeCloudBlobStorage(string containerName, string path)
        {
            cloudBlobStorage.GetOrAdd(containerName, _ => new List<CloudBlob>() { new CloudBlob(new Uri($"{urlPrefix}/{containerName}/{path}")) });
        }

        private static IBlobContainerClient CreateFakeBlobClient(string containerName)
        {
            var blobClient = A.Fake<IBlobContainerClient>();

            A.CallTo(() => blobClient.EnumerateBlobPathsUnderPrefix(A<string>._))
                .ReturnsLazily((string prefix) =>
                    FilterBlobsByPrefix(containerName, prefix).Select(x => x.Name).ToList().ToAsyncEnumerable());

            A.CallTo(() => blobClient.GetBlobsFromPrefix(A<string>._))
                .ReturnsLazily((string prefix) =>
                    Task.FromResult((IList<CloudBlob>)FilterBlobsByPrefix(containerName, prefix).ToList()));

            return blobClient;
        }

        private static IEnumerable<CloudBlob> FilterBlobsByPrefix(string containerName, string prefix)
        {
            if (!cloudBlobStorage.ContainsKey(containerName))
            {
                throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "FAKED [uncertain of code]", "FAKED [uncertain of msg]");
            }

            return cloudBlobStorage[containerName]
                .Where(cloudBlob => cloudBlob.Uri.AbsoluteUri.StartsWith($"{urlPrefix}/{containerName}/{prefix}", StringComparison.Ordinal));
        }
    }
}
