﻿// <copyright file="FakeOperationsSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using FakeItEasy;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.ServiceInvokerInterfaces.ApiHost;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.WireContracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeOperationsSetup
    {
        private static Dictionary<string, AsyncOperationStatus> operationResponses = new Dictionary<string, AsyncOperationStatus>();

        public static void ConfigureFakes(IOperationsController operationsController)
        {
            operationResponses = new Dictionary<string, AsyncOperationStatus>();

            A.<PERSON>To(() => operationsController.GetAsyncOperationStatusAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var operationId = call.GetArgument<string>("id");

                    if (!operationResponses.ContainsKey(operationId))
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "OperationNotFound", $"There is no operation with id {operationId}");
                    }

                    return Task.FromResult(operationResponses[operationId]);
                });
        }

        public static void AddToOperationResponses(string id, AsyncOperationStatus status)
        {
            operationResponses.Add(id, status);
        }
    }
}
