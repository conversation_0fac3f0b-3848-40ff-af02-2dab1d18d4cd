﻿// <copyright file="FakeS2SClientFactory.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using FakeItEasy;
using FakeItEasy.Sdk;
using Microsoft.MachineLearning.Common.Core.Arm.Contracts;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Newtonsoft.Json;
using Polly;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public class FakeS2SClientFactory : IS2SClientFactory
    {
        private IDictionary<Type, object> _clients = new Dictionary<Type, object>();
        private IDictionary<Type, ServiceInvoker.AfterResponseHandler> _afterResponseHandlers = new Dictionary<Type, ServiceInvoker.AfterResponseHandler>();

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1054:Uri parameters should not be strings", Justification = "Test code")]
        public T CreateS2SClient<T>(string providerUri, bool useServiceOidInPrincipalIdentityAndByPassUserAuth = false)
        {
            return GetClient<T>();
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1054:Uri parameters should not be strings", Justification = "Test code")]
        public T CreateS2SClient<T>(string providerUri, string tenant, FirstPartyAppDetails firstPartyApp, string resource = null, bool useServiceOidInPrincipalIdentityAndByPassUserAuth = false)
        {
            return GetClient<T>();
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Design", "CA1054:Uri parameters should not be strings", Justification = "Test code")]
        public T CreateS2SClient<T>(string providerUri, TimeSpan? requestTimeout, bool useServiceOidInPrincipalIdentityAndByPassUserAuth = false)
        {
            return GetClient<T>();
        }

        public T CreateS2SClient<T>(Uri serviceAddress, ServiceInvoker.BeforeRequestHandler beforeRequest = null, ServiceInvoker.AfterResponseHandler afterResponse = null, IEnumerable<ServiceInvoker.IParameterFormatter> parameterFormatters = null, IEnumerable<ServiceInvoker.IParameterFormatter> queryParameterFormatters = null, ServiceInvoker.BeforeRequestAsyncHandler beforeRequestAsync = null, TimeSpan? requestTimeout = null, IAsyncPolicy retryPolicy = null, JsonSerializerSettings serializerSettings = null, bool useServiceOidInPrincipalIdentityAndByPassUserAuth = false, bool continueAcceptedRequests = false, string resource = null, string authority = null)
        {
            if (afterResponse != null)
            {
                _afterResponseHandlers[typeof(T)] = afterResponse;
            }

            return GetClient<T>();
        }

        public ServiceInvoker.AfterResponseHandler GetAfterResponseHandler<T>()
        {
            ServiceInvoker.AfterResponseHandler afterResponseHandler;

            if (!_afterResponseHandlers.TryGetValue(typeof(T), out afterResponseHandler))
            {
                _afterResponseHandlers[typeof(T)] = afterResponseHandler;
            }

            return afterResponseHandler;
        }

        public T GetClient<T>()
        {
            object client;

            if (!_clients.TryGetValue(typeof(T), out client))
            {
                _clients[typeof(T)] = client = Create.Fake(typeof(T));
            }

            return (T)client;
        }

        public void ResetClients()
        {
            foreach (var client in _clients.Values)
            {
#pragma warning disable CS0618 // Type or member is obsolete
                // ClearConfiguration will be removed in version 8.0.0. Prefer to discard the fake and create a new one.
                Fake.ClearConfiguration(client);
#pragma warning restore CS0618
            }
        }
    }
}
