﻿// <copyright file="IsolationTestHarness.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using FakeItEasy;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Clients;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Storage;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public class IsolationTestHarness : ITestHarness
    {
        public FakeS2SClientFactory FakeS2SClientFactory { get; } = new FakeS2SClientFactory();

        public void ConfigureServices(IServiceCollection services)
        {
            services.RemoveAll<IBlobContainerClientFactory>();
            services.AddSingleton(A.Fake<IBlobContainerClientFactory>());

            services.RemoveAll<IResourceStorageProvider>();
            services.AddSingleton(A.Fake<IResourceStorageProvider>());

            services.RemoveAll<IS2SClientFactory>();
            services.AddSingleton<IS2SClientFactory>(FakeS2SClientFactory);

            services.RemoveAll<IOboClient>();
            services.AddSingleton(A.Fake<IOboClient>());
        }
    }
}