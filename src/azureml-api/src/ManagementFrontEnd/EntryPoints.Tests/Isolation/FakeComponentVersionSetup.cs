﻿// <copyright file="FakeComponentVersionSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Common.Core.Contracts;
using FakeItEasy;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Component.DevPlatv2.Contracts.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.UnitTests;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Assets;
using Microsoft.MachineLearning.Project.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeComponentVersionSetup
    {
        public static void ConfigureCodeVersionFakes(TestFixtureIsolatedManagementFrontEnd fixture)
        {
            SnapshotDto snapshot = new SnapshotDto();
            snapshot.Name = ComponentVersionTestConstants.CodeName;
            snapshot.Version = ComponentVersionTestConstants.CodeVersion;
            fixture.SetupFakeS2SClient<ISnapshotControllerNewRoutes>(client => A.CallTo(() => client.GetSnapshotMetadata(A<Guid>._, A<string>._, A<string>._, A<string>._, A<Guid>._)).Returns(snapshot));
        }

        public static void ConfigureControllerFakes(IComponentVersionsController controller)
        {
            var components = new List<Resource<ComponentVersionV2>>();

            A.CallTo(() => controller.RegisterComponent(A<string>._, A<string>._, A<string>._, A<Resource<ComponentVersionV2>>._, A<string>._, A<string>._, false, true))
                .ReturnsLazily(call =>
                {
                    var componentVersion = call.GetArgument<Resource<ComponentVersionV2>>("request");
                    Resource<ComponentVersionV2> registeredComponentDto = AddNewComponent(componentVersion, components, componentVersion.GetComponentVersion());
                    return Task.FromResult(registeredComponentDto);
                });

            A.CallTo(() => controller.UpdateComponent(A<string>._, A<string>._, A<string>._, A<string>._, A<Resource<ComponentVersionV2>>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var componentVersion = call.GetArgument<Resource<ComponentVersionV2>>("request");
                    var name = componentVersion.GetComponentName();
                    var version = componentVersion.GetComponentVersion();

                    if (DoesComponentVersionExist(components, name, version))
                    {
                        Resource<ComponentVersionV2> component = components.Find(item => item.GetComponentName() == name && item.GetComponentVersion() == version);
                        component.Properties = componentVersion.Properties;
                        return Task.FromResult(component);
                    }
                    else
                    {
                        // this will not happen given CreateOrUpdate functionality
                        return Task.FromResult(componentVersion);
                    }
                });

            A.CallTo(() => controller.GetComponent(A<string>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("componentName");
                    var version = call.GetArgument<string>("componentVersion");

                    if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(version) && components.Any(x => x.GetComponentName() == name && x.GetComponentVersion() == version))
                    {
                        return Task.FromResult(components.Find(item => item.GetComponentName() == name && item.GetComponentVersion() == version));
                    }
                    else
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }
                });

            A.CallTo(() => controller.ListComponents(A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<string>._, A<string>._, A<int?>._, A<bool>._, A<string>._, A<ListViewType>._))
                .ReturnsLazily(call =>
                {
                    var componentName = call.GetArgument<string>("name");
                    var count = call.GetArgument<int?>("count");

                    var componentList = components
                        .Where(x => componentName == null || string.Equals(x.GetComponentName(), componentName, StringComparison.InvariantCulture))
                        .OrderByDescending(x => x.SystemData.CreatedAt)
                        .ToList();

                    if (count != null)
                    {
                        componentList = componentList.Take(count.Value).ToList();
                    }

                    var listResult = new PaginatedResult<Resource<ComponentVersionV2>>(componentList);

                    return Task.FromResult(listResult);
                });

            A.CallTo(() => controller.DeleteComponent(A<string>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .Invokes(call =>
                {
                    var name = call.GetArgument<string>("componentName");
                    var version = call.GetArgument<string>("componentVersion");

                    if (name != null && !string.IsNullOrEmpty(version) && components.Any(x => x.GetComponentName() == name && x.GetComponentVersion() == version))
                    {
                        if (components.Where(item => item.GetComponentName() == name).Count() == 1)
                        {
                            throw ServiceInvocationExceptionUtil.CreateResponseMessage(HttpStatusCode.BadRequest, "The request version " + version + " is the only active version");
                        }
                        components.Remove(components.Find(item => item.GetComponentName() == name && item.GetComponentVersion() == version));
                    }
                });
        }

        private static bool DoesComponentVersionExist(List<Resource<ComponentVersionV2>> components, string name, string version)
        {
            if (components.Any(x => x.GetComponentName() == name && x.GetComponentVersion() == version))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private static Resource<ComponentVersionV2> AddNewComponent(Resource<ComponentVersionV2> component, List<Resource<ComponentVersionV2>> components, string version)
        {
            if (DoesComponentVersionExist(components, component.GetComponentName(), version))
            {
                var e = ServiceInvocationExceptionUtil.Create(HttpStatusCode.Conflict);
                throw e;
            }
            component.SystemData.CreatedAt = DateTime.UtcNow;
            component.SystemData.CreatedBy = Guid.NewGuid().ToString();
            component.SystemData.LastModifiedAt = DateTime.UtcNow;
            component.SystemData.LastModifiedBy = Guid.NewGuid().ToString();
            components.Add(component);
            return component;
        }

        private static string GetComponentVersion(this Resource<ComponentVersionV2> component)
        {
            return component.Properties.ComponentSpec["version"].ToString();
        }

        private static string GetComponentName(this Resource<ComponentVersionV2> component)
        {
            return component.Properties.ComponentSpec["name"].ToString();
        }
    }
}
