﻿// <copyright file="FakeCodeVersionSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FakeItEasy;
using Microsoft.MachineLearning.Project.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeCodeVersionSetup
    {
        public static void ConfigureFakes(ISnapshotControllerV2NewRoutes snapshotV2Controller)
        {
            var snapshotDtos = new List<SnapshotDto>();

            A.CallTo(() => snapshotV2Controller.GetSnapshotDtoByNameVersion(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var version = call.GetArgument<string>("version");

                    var result = snapshotDtos
                        .Where(snapshotDto => snapshotDto.Name == name && snapshotDto.Version == version)
                        .SingleOrDefault();

                    if (result == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound);
                    }

                    return Task.FromResult(result.Clone());
                });

            A.CallTo(() => snapshotV2Controller.CreateOrUpdateSnapshotByNameVersionAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<CreateSnapshotDto>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var version = call.GetArgument<string>("version");
                    var createDto = call.GetArgument<CreateSnapshotDto>("createDto");

                    snapshotDtos.Add(ConvertToSnapshotDto(name, version, createDto));

                    // First snapshotDto is 'updated'
                    return Task.FromResult((ConvertToSnapshotDto(name, version, createDto).Clone(), false));
                }).Once()
                .Then
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var version = call.GetArgument<string>("version");
                    var createDto = call.GetArgument<CreateSnapshotDto>("createDto");

                    snapshotDtos.Add(ConvertToSnapshotDto(name, version, createDto));

                    // Subsequent snapshotDtos are 'created'
                    return Task.FromResult((ConvertToSnapshotDto(name, version, createDto).Clone(), true));
                });
        }

        private static SnapshotDto ConvertToSnapshotDto(string name, string version, CreateSnapshotDto createDto)
        {
            return new SnapshotDto()
            {
                Name = name,
                Version = version,
                Id = Guid.NewGuid(),
                Tags = createDto.Tags,
                Properties = createDto.Properties,
                Root = ConvertToDirTreeNode(createDto)
            };
        }

        private static DirTreeNode ConvertToDirTreeNode(CreateSnapshotDto createDto)
        {
            var node = new DirTreeNode(string.Empty, SnapshotDtoConstants.DirectoryType);

            var child = createDto.DirTree.Files[1];
            node.AddChild(new DirTreeNode(child.Name, child.IsFile ? SnapshotDtoConstants.FileType : SnapshotDtoConstants.DirectoryType, child.Hash));

            return node;
        }

        private static SnapshotDto Clone(this SnapshotDto snapshotDto)
        {
            return new SnapshotDto()
            {
                CreatedBy = snapshotDto.CreatedBy,
                CreatedTime = snapshotDto.CreatedTime,
                Description = snapshotDto.Description,
                Id = snapshotDto.Id,
                ModifiedBy = snapshotDto.ModifiedBy,
                ModifiedTime = snapshotDto.ModifiedTime,
                Name = snapshotDto.Name,
                Properties = new Dictionary<string, string>(snapshotDto.Properties),
                Root = new DirTreeNode(snapshotDto.Root.Name, snapshotDto.Root.Type)
                {
                    Children = snapshotDto.Root.Children,
                    Hash = snapshotDto.Root.Hash,
                    SasUrl = snapshotDto.Root.SasUrl,
                    SizeBytes = snapshotDto.Root.SizeBytes,
                    SizeSet = snapshotDto.Root.SizeSet,
                    Timestamp = snapshotDto.Root.Timestamp
                },
                Tags = new Dictionary<string, string>(snapshotDto.Tags),
                Version = snapshotDto.Version
            };
        }
    }
}
