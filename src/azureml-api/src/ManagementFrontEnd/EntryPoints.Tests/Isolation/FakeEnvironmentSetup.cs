﻿// <copyright file="FakeEnvironmentSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Common.Core.Contracts;
using Common.WebApi.Exceptions;
using FakeItEasy;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core.Exceptions;
using Microsoft.MachineLearning.EnvironmentManagement.Contracts;
using Microsoft.MachineLearning.Index.Contracts;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeEnvironmentSetup
    {
        public static void ConfigureFakes(IEnvironmentController environmentController, ICloudMaterializationControllerV2 cloudMaterializationControllerV2, IIndexEntitiesFilterController indexEntities<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IEnvironmentAssetConsumptionController environmentAssetConsumptionController)
        {
            (string, string)[] curatedEnvironmentList =
            {
                // Curated
                ("AzureML-Minimal", "37"), ("AzureML-Tutorial", "51"), ("AzureML-Chainer-5.1.0-CPU", "37"), ("AzureML-Chainer-5.1.0-GPU", "37"), ("AzureML-PyTorch-1.2-CPU", "37"), ("AzureML-PyTorch-1.2-GPU", "37"), ("AzureML-Scikit-learn-0.20.3", "37"), ("AzureML-TensorFlow-1.10-CPU", "37"), ("AzureML-TensorFlow-1.10-GPU", "38"), ("AzureML-AutoML", "37"), ("AzureML-Designer", "21"),
                // Not curated but used in Non-Isolated tests
                ("sklearntestenv", "1")
            };

            var fakeEnvironments = new Dictionary<string, Tuple<EnvironmentDefinition, Dictionary<string, string>>>();
            var fakeImages = new Dictionary<string, ImageDetails>();
            var fakeEndpoints = new Dictionary<string, Dictionary<EndpointRouteType, EndpointRoute>>();
            var fakeBuildDetails = new Dictionary<string, ImageBuildDetails>();
            var fakeBuildStatus = new Dictionary<string, ImageBuildStatus>();
            foreach (var curatedEnvironment in curatedEnvironmentList)
            {
                Tuple<EnvironmentDefinition, Dictionary<string, string>> fake = new Tuple<EnvironmentDefinition, Dictionary<string, string>>(
                    new EnvironmentDefinition()
                    {
                        Name = curatedEnvironment.Item1,
                        Version = curatedEnvironment.Item2,
                        Docker = new EnvironmentDefinition.DockerSection()
                        {
                            BaseDockerfile = "python:3.7-slim"
                        }
                    },
                    new Dictionary<string, string>()
                    {
                        ["curated_in"] = "eastus"
                    });
                fakeEnvironments.Add(curatedEnvironment.Item1, fake);
                fakeImages.Add(curatedEnvironment.Item1, new ImageDetails()
                {
                    ImageExistsInRegistry = false,
                    Ingredients = new ImageIngredients()
                    {
                        Dockerfile = "FROM mcr.microsoft.com/azureml/intelmpi2018.3-ubuntu16.04:20200821.v1@sha256:8cee6f674276dddb23068d2710da7f7f95b119412cc482675ac79ba45a4acf99\nUSER root\nRUN mkdir -p $HOME/.cache\nWORKDIR /\nCOPY azureml-environment-setup/99brokenproxy /etc/apt/apt.conf.d/\nRUN if dpkg --compare-versions `conda --version | grep -oE '[^ ]+$'` lt 4.4.11; then conda install conda==4.4.11; fi\nCOPY azureml-environment-setup/mutated_conda_dependencies.yml azureml-environment-setup/mutated_conda_dependencies.yml\nRUN ldconfig /usr/local/cuda/lib64/stubs && conda env create -p /azureml-envs/azureml_1b417bb747e35859ebf611fb43071e9c -f azureml-environment-setup/mutated_conda_dependencies.yml && rm -rf \"$HOME/.cache/pip\" && conda clean -aqy && CONDA_ROOT_DIR=$(conda info --root) && rm -rf \"$CONDA_ROOT_DIR/pkgs\" && find \"$CONDA_ROOT_DIR\" -type d -name __pycache__ -exec rm -rf {} + && ldconfig\n# AzureML Conda environment name: azureml_1b417bb747e35859ebf611fb43071e9c\nENV PATH /azureml-envs/azureml_1b417bb747e35859ebf611fb43071e9c/bin:$PATH\nENV AZUREML_CONDA_ENVIRONMENT_PATH /azureml-envs/azureml_1b417bb747e35859ebf611fb43071e9c\nENV LD_LIBRARY_PATH /azureml-envs/azureml_1b417bb747e35859ebf611fb43071e9c/lib:$LD_LIBRARY_PATH\nCOPY azureml-environment-setup/spark_cache.py azureml-environment-setup/log4j.properties /azureml-environment-setup/\nRUN if [ $SPARK_HOME ]; then /bin/bash -c '$SPARK_HOME/bin/spark-submit  /azureml-environment-setup/spark_cache.py'; fi\nENV AZUREML_ENVIRONMENT_IMAGE True\nCMD [\"bash\"]\n",
                        CondaSpecification = "channels:\n- conda-forge\ndependencies:\n- python=3.8.12\n- pip:\n  - azureml-defaults\nname: azureml_1b417bb747e35859ebf611fb43071e9c\n"
                    },
                    PythonEnvironment = new PythonEnvironmentMetadata()
                    {
                        InterpreterPath = "/azureml-envs/azureml_1b417bb747e35859ebf611fb43071e9c/bin/python",
                        CondaEnvironmentName = "azureml_1b417bb747e35859ebf611fb43071e9c",
                        CondaEnvironmentPath = "/azureml-envs/azureml_1b417bb747e35859ebf611fb43071e9c"
                    },
                    DockerImage = new DockerEnvironmentMetadata()
                    {
                        Name = "azureml/azureml_62f08a24db49e60165c3d2b8b8537c56",
                        Registry = new ContainerRegistry()
                        {
                            Address = "394c72a094954589968f9fd0a1c596ed.azurecr.io",
                            Username = "394c72a094954589968f9fd0a1c596ed"
                        }
                    },
                    Warnings = new List<string>()
                });
            }

            AddEnvironmentForInferenceCustomContainer(fakeEnvironments, fakeEndpoints);

            // Environment Definitions.
            A.CallTo(() => environmentController.GetLatestDefinition(A<Guid>._, A<string>._, A<string>._, A<string>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    var environment = fakeEnvironments.ContainsKey(name) ? fakeEnvironments[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "No environment with that name found in workspace");
                    return Task.FromResult(environment.Item1);
                });
            A.CallTo(() => environmentController.GetDefinition(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<string>.Ignored))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    var version = call.GetArgument<string>("version");
                    var environment = fakeEnvironments.ContainsKey(name) && fakeEnvironments[name].Item1.Version == version ? fakeEnvironments[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "No environment with that name and version found in workspace");
                    return Task.FromResult(environment.Item1);
                });

            A.CallTo(() => environmentController.GetLatestDefinitionAndMetadata(A<Guid>._, A<string>._, A<string>._, A<string>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    var environment = fakeEnvironments.ContainsKey(name) ? fakeEnvironments[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "No environment with that name found in workspace");

                    return Task.FromResult(new EnvironmentDefinitionAndMetadata()
                    {
                        Definition = environment.Item1,
                        DefinitionMetadata = new EnvironmentDefinitionMetadata()
                        {
                            Tags = fakeEnvironments[name].Item2,
                            CreatedTime = DateTimeOffset.Now,
                            CreatedBy = new User() { UserName = Guid.NewGuid().ToString() }
                        }
                    });
                });

            A.CallTo(() => environmentController.GetDefinitionAndMetadata(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<string>.Ignored))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    var version = call.GetArgument<string>("version");
                    var environment = fakeEnvironments.ContainsKey(name) && fakeEnvironments[name].Item1.Version == version ? fakeEnvironments[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "No environment with that name and version found in workspace");
                    var endpoints = fakeEnvironments.ContainsKey(name) && fakeEnvironments[name].Item1.Version == version && fakeEndpoints.ContainsKey(GetEndpointKey(name, version)) ? fakeEndpoints[GetEndpointKey(name, version)] : default;

                    return Task.FromResult(new EnvironmentDefinitionAndMetadata()
                    {
                        Definition = environment.Item1,
                        DefinitionMetadata = new EnvironmentDefinitionMetadata()
                        {
                            Endpoints = endpoints,
                            Tags = fakeEnvironments[name].Item2,
                            CreatedTime = DateTimeOffset.Now,
                            CreatedBy = new User() { UserName = Guid.NewGuid().ToString() }
                        }
                    });
                });

            A.CallTo(() => environmentAssetConsumptionController.GetEnvironmentDefinitionFromEnvironmentReference(A<EnvironmentAssetConsumptionRequestWithEnvironmentReferenceDto>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var environmentAssetConsumptionRequestWithEnvironmentReferenceDto = call.GetArgument<EnvironmentAssetConsumptionRequestWithEnvironmentReferenceDto>("environmentAssetConsumptionRequestWithEnvironmentReferenceDto");
                    AssetReference assetReference;
                    try
                    {
                        assetReference = AssetReference.Parse(environmentAssetConsumptionRequestWithEnvironmentReferenceDto.EnvironmentReference);
                    }
                    catch
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Invalid environment reference");
                    }
                    var name = assetReference.AssetResourceId;
                    var version = assetReference.Version;

                    Tuple<EnvironmentDefinition, Dictionary<string, string>> environment;
                    if (version != null)
                    {
                        environment = fakeEnvironments.ContainsKey(name) && fakeEnvironments[name].Item1.Version == version ? fakeEnvironments[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "No environment with that name and version found in workspace");
                    }
                    else
                    {
                        environment = fakeEnvironments.ContainsKey(name) ? fakeEnvironments[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "No environment with that name found in workspace");
                    }

                    return Task.FromResult(new EnvironmentDefinitionAndMetadata()
                    {
                        Definition = environment.Item1,
                        DefinitionMetadata = new EnvironmentDefinitionMetadata()
                        {
                            Endpoints = null,
                            Tags = environment.Item2,
                            CreatedTime = DateTimeOffset.Now,
                            CreatedBy = new User() { UserName = Guid.NewGuid().ToString() }
                        }
                    });
                });

            A.CallTo(() => environmentController.AddDefinitionWithMetadata(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<EnvironmentDefinitionWithSetMetadataDto>._, A<bool>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    var environmentDefinitionWithSetMetadataDto = call.GetArgument<EnvironmentDefinitionWithSetMetadataDto>("environmentDefinitionWithSetMetadataDto");
                    string version = call.GetArgument<string>("version");

                    var inputDefinition = environmentDefinitionWithSetMetadataDto.Definition;
                    var metadataDto = environmentDefinitionWithSetMetadataDto.Metadata;

                    if (name.StartsWith("AzureML-", StringComparison.OrdinalIgnoreCase))
                    {
                        throw new ValidationDetailsException(
                            "Environment name can not start with the prefix AzureML-",
                            new ValidationFailureDetails(
                                target: "EnvironmentDefinition",
                                code: ValidationErrorCode.Invalid,
                                message: "Environment name can not start with the prefix AzureML - "));
                    }

                    EnvironmentDefinition definitionToReturn = inputDefinition;
                    if (fakeEnvironments.ContainsKey(name))
                    {
                        EnvironmentDefinition currentDefinition = fakeEnvironments[name].Item1;
                        // use env variables to establish whether these are duplicate environments

                        if (currentDefinition.EnvironmentVariables.Count == inputDefinition.EnvironmentVariables.Count && !currentDefinition.EnvironmentVariables.Except(inputDefinition.EnvironmentVariables).Any())
                        {
                            // definitions are identical, so return the current one rather than the new one to match EMS behavior
                            definitionToReturn = currentDefinition;
                        }
                        else
                        {
                            fakeEnvironments[name] = new Tuple<EnvironmentDefinition, Dictionary<string, string>>(inputDefinition, fakeEnvironments[name].Item2);
                        }
                    }
                    else
                    {
                        fakeEnvironments.Add(name, new Tuple<EnvironmentDefinition, Dictionary<string, string>>(inputDefinition, new Dictionary<string, string>()));
                    }

                    var endpoints = metadataDto.Endpoints;
                    var tags = metadataDto.Tags;

                    if (fakeEnvironments.ContainsKey(name))
                    {
                        if (tags != null)
                        {
                            fakeEnvironments[name] = new Tuple<EnvironmentDefinition, Dictionary<string, string>>(fakeEnvironments[name].Item1, tags);
                        }
                        if (endpoints != null)
                        {
                            fakeEndpoints.Add(GetEndpointKey(name, version), endpoints);
                        }
                    }

                    var metadata = new EnvironmentDefinitionMetadata()
                    {
                        Endpoints = endpoints,
                        Tags = fakeEnvironments[name].Item2,
                        CreatedTime = DateTimeOffset.Now,
                        CreatedBy = new User() { UserName = Guid.NewGuid().ToString() }
                    };

                    return Task.FromResult(new EnvironmentDefinitionAndMetadata()
                    {
                        Definition = definitionToReturn,
                        DefinitionMetadata = metadata
                    });
                });

            A.CallTo(() => environmentController.AddDefinition(A<Guid>._, A<string>._, A<string>._, A<string>._, A<EnvironmentDefinition>._, A<bool>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    var inputDefinition = call.GetArgument<EnvironmentDefinition>("definition");
                    if (fakeEnvironments.ContainsKey(name))
                    {
                        EnvironmentDefinition currentDefinition = fakeEnvironments[name].Item1;
                        // use env variables to establish whether these are duplicate environments
                        if (currentDefinition.EnvironmentVariables.Count == inputDefinition.EnvironmentVariables.Count && !currentDefinition.EnvironmentVariables.Except(inputDefinition.EnvironmentVariables).Any())
                        {
                            // definitions are identical, so return the current one rather than the new one to match EMS behavior
                            return Task.FromResult(currentDefinition);
                        }
                        fakeEnvironments[name] = new Tuple<EnvironmentDefinition, Dictionary<string, string>>(inputDefinition, fakeEnvironments[name].Item2);
                    }
                    else
                    {
                        fakeEnvironments.Add(name, new Tuple<EnvironmentDefinition, Dictionary<string, string>>(inputDefinition, new Dictionary<string, string>()));
                    }
                    return Task.FromResult(inputDefinition);
                });

            A.CallTo(() => environmentController.AddDefinition(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<EnvironmentDefinition>._, A<bool>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("environmentName");
                    if (name.StartsWith("AzureML-", StringComparison.OrdinalIgnoreCase))
                    {
                        throw new ValidationDetailsException(
                            "Environment name can not start with the prefix AzureML-",
                            new ValidationFailureDetails(
                                target: "EnvironmentDefinition",
                                code: ValidationErrorCode.Invalid,
                                message: "Environment name can not start with the prefix AzureML - "));
                    }
                    EnvironmentDefinition inputDefinition = call.GetArgument<EnvironmentDefinition>("definition");
                    inputDefinition.Version = call.GetArgument<string>("version");
                    if (fakeEnvironments.ContainsKey(name))
                    {
                        EnvironmentDefinition currentDefinition = fakeEnvironments[name].Item1;
                        fakeEnvironments[name] = new Tuple<EnvironmentDefinition, Dictionary<string, string>>(inputDefinition, fakeEnvironments[name].Item2);
                    }
                    else
                    {
                        fakeEnvironments.Add(name, new Tuple<EnvironmentDefinition, Dictionary<string, string>>(inputDefinition, new Dictionary<string, string>()));
                    }
                    return Task.FromResult(inputDefinition);
                });

            A.CallTo(() => environmentController.ListDefinitions(A<Guid>._, A<string>._, A<string>._, A<bool>._, A<string>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    return Task.FromResult(new PaginatedResult<EnvironmentDefinition>((from x in fakeEnvironments.Values select x.Item1).ToArray()));
                });

            var fakeImageBuildDetails = new ImageBuildDetails()
            {
                LogUrl = "https://amlAccount.blob.core.windows.net/amlLogs/logs?sv=2020-05-26",
                BuildId = "mockBuildId"
            };

            var responseNoResult = A.Fake<ICheckableLongRunningOperationResponse<ImageBuildDetails>>();
            responseNoResult.CompletionResult = null;

            A.CallTo(() => responseNoResult.Check(A<LongRunningOperationCheckParameters>._, A<CancellationToken>._))
            .Invokes((LongRunningOperationCheckParameters checkParams, CancellationToken ct) =>
            {
                _ = checkParams.AfterResponse(new HttpResponseMessage() { Content = new StringContent(JsonConvert.SerializeObject(fakeImageBuildDetails)) });
            })
            .Returns(A.Fake<ICheckableLongRunningOperationResponse<ImageBuildDetails>>());

            A.CallTo(() => cloudMaterializationControllerV2.StartImageBuild(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    return Task.FromResult(responseNoResult);
                });

            A.CallTo(() => environmentController.SetDefinitionDocumentMetadata(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<EnvironmentDefinitionSetMetadataDto>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    string name = call.GetArgument<string>("environmentName");
                    string version = call.GetArgument<string>("version");
                    EnvironmentDefinitionSetMetadataDto metadataDto = call.GetArgument<EnvironmentDefinitionSetMetadataDto>("environmentDefinitionSetMetadataDto");

                    var endpoints = metadataDto.Endpoints;
                    var tags = metadataDto.Tags;

                    if (fakeEnvironments.ContainsKey(name))
                    {
                        if (tags != null)
                        {
                            fakeEnvironments[name] = new Tuple<EnvironmentDefinition, Dictionary<string, string>>(fakeEnvironments[name].Item1, tags);
                        }
                        if (endpoints != null)
                        {
                            fakeEndpoints.Add(GetEndpointKey(name, version), endpoints);
                        }
                    }
                    return Task.FromResult(0);
                });

            A.CallTo(() => indexEntitiesFilterController.GetEntities(A<IndexEntitiesRequest>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    IndexEntitiesRequest indexEntitiesRequest = call.GetArgument<IndexEntitiesRequest>("request");
                    string name = (from f in indexEntitiesRequest.Filters where f.Field == "name" select f.Values.FirstOrDefault()).FirstOrDefault();
                    IndexEntitiesResponse indexEntitiesResponse = null;
                    if (fakeEnvironments.ContainsKey(name))
                    {
                        indexEntitiesResponse = new IndexEntitiesResponse()
                        {
                            TotalCount = 1,
                            Value = new List<IndexEntityResponse>()
                            {
                                new IndexEntityResponse()
                                {
                                    Annotations = new IndexAnnotations()
                                    {
                                        Tags = fakeEnvironments[name].Item2
                                    },
                                    Properties = new IndexProperties()
                                    {
                                        CreationContext = new Schema.Contracts.CreationContext()
                                        {
                                            CreatedBy = new Schema.Contracts.CreatedBy
                                            {
                                                UserName = Guid.NewGuid().ToString()
                                            },
                                            CreatedTime = DateTimeOffset.Now
                                        }
                                    }
                                }
                            },
                            NextSkip = 0,
                            ContinuationToken = null
                        };
                    }
                    else
                    {
                        indexEntitiesResponse = new IndexEntitiesResponse()
                        {
                            TotalCount = 0,
                            Value = Array.Empty<IndexEntityResponse>(),
                            NextSkip = 0,
                            ContinuationToken = null
                        };
                    }
                    return Task.FromResult(indexEntitiesResponse);
                });
        }

        private static string GetEndpointKey(string name, string version)
            => name + "_" + version;

        private static void AddEnvironmentForInferenceCustomContainer(
            Dictionary<string, Tuple<EnvironmentDefinition, Dictionary<string, string>>> fakeEnvironments,
            Dictionary<string, Dictionary<EndpointRouteType, EndpointRoute>> fakeEndpoints)
        {
            (string, string) inferenceCustomContainerEnvironment = ("inferenceCustomContainerEnvironment", "1");

            fakeEnvironments.Add(inferenceCustomContainerEnvironment.Item1, GetInferenceCustomContainerEnvironment());
            fakeEndpoints.Add(
                GetEndpointKey(inferenceCustomContainerEnvironment.Item1, inferenceCustomContainerEnvironment.Item2),
                AddEndpoint());

            Tuple<EnvironmentDefinition, Dictionary<string, string>> GetInferenceCustomContainerEnvironment()
                => new Tuple<EnvironmentDefinition, Dictionary<string, string>>(
                    new EnvironmentDefinition()
                    {
                        Name = inferenceCustomContainerEnvironment.Item1,
                        Version = inferenceCustomContainerEnvironment.Item2,
                        Docker = new EnvironmentDefinition.DockerSection()
                        {
                            BaseImage = "python:3.7-slim",
                            Platform = new DockerImagePlatform
                            {
                                Os = "Linux"
                            }
                        }
                    },
                    new Dictionary<string, string>()
                    {
                        ["curated_in"] = "eastus"
                    });

            static Dictionary<EndpointRouteType, EndpointRoute> AddEndpoint()
                => new Dictionary<EndpointRouteType, EndpointRoute>
                {
                    { EndpointRouteType.InferenceLiveness, new EndpointRoute() },
                    { EndpointRouteType.InferenceReadiness, new EndpointRoute() },
                    { EndpointRouteType.InferenceScoring, new EndpointRoute() }
                };
        }
    }
}