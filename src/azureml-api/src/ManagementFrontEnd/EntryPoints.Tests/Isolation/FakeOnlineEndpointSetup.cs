﻿// <copyright file="FakeOnlineEndpointSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using FakeItEasy;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.MachineLearning.Common.Msi.Contracts;
using Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.ControllerInterfaces;
using Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.Endpoint;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters;
using Microsoft.MachineLearning.ModelManagement.Contracts.Common;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.Common;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.ServiceInvokerInterfaces.ApiHost;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.WireContracts;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.WireContracts.OnlineEndpoints;
using Newtonsoft.Json;
using V20211001 = Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.Endpoint;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeOnlineEndpointSetup
    {
        private static IMapper _mapper;

#pragma warning disable CA1810 // Initialize reference type static fields inline
        static FakeOnlineEndpointSetup()
#pragma warning restore CA1810 // Initialize reference type static fields inline
        {
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new MFEContractMappings());
            });

            _mapper = mapperConfig.CreateMapper();
        }

        public static void ConfigureFakes(
            IOnlineEndpointController mmsEndpointController,
            IEndpointController idpEndpointController)
        {
            var endpointResponses = new List<OnlineEndpointResponseBase>();

            A.CallTo(() => mmsEndpointController.GetEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");

                    var result = endpointResponses.Where(endpointResponse => endpointResponse.Name == name).SingleOrDefault();
                    if (result == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "NoSuchEndpoint", "Test error message");
                    }

                    return Task.FromResult(result);
                });

            A.CallTo(() => idpEndpointController.GetEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("endpointName");

                    var result = endpointResponses.Where(endpointResponse => endpointResponse.Name == name).SingleOrDefault();
                    if (result == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "NoSuchEndpoint", "Test error message");
                    }

                    return Task.FromResult(ConvertToResponseBase(result));
                });

            A.CallTo(() => mmsEndpointController.CreateEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<PutOnlineEndpointRequest>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var request = call.GetArgument<PutOnlineEndpointRequest>("request");
                    var endpointResponse = ConvertToResponseBase(name, request);

                    endpointResponses.Add(endpointResponse);

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                }).Once()
                .Then
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var request = call.GetArgument<PutOnlineEndpointRequest>("request");
                    var endpointResponse = ConvertToResponseBase(name, request);

                    // Remove-then-add serves as an "update"
                    endpointResponses = endpointResponses.Where(inner => inner.Name != endpointResponse.Name).ToList();
                    endpointResponses.Add(endpointResponse);

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.OK));
                });

            A.CallTo(() => idpEndpointController.CreateEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<V20211001.CreateEndpointRequest>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("endpointName");
                    var request = call.GetArgument<V20211001.CreateEndpointRequest>("request");
                    var endpointResponse = ConvertToResponseBase(name, request);
                    var bodyResponse = JsonConvert.SerializeObject(ConvertToResponseBase(endpointResponse));

                    endpointResponses.Add(endpointResponse);

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.DpOperationLocationHeader, new List<string>() { operationId } }
                    }, content: bodyResponse));
                }).Once()
                .Then
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("endpointName");
                    var request = call.GetArgument<V20211001.CreateEndpointRequest>("request");
                    var endpointResponse = ConvertToResponseBase(name, request);
                    var bodyResponse = JsonConvert.SerializeObject(ConvertToResponseBase(endpointResponse));

                    // Remove-then-add serves as an "update"
                    endpointResponses = endpointResponses.Where(inner => inner.Name != endpointResponse.Name).ToList();
                    endpointResponses.Add(endpointResponse);

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.OK, content: bodyResponse));
                });

            A.CallTo(() => mmsEndpointController.GetEndpointsAsync(A<Guid>._, A<string>._, A<string>._, A<ListOnlineEndpointsRequest>._))
                .ReturnsLazily(call =>
                {
                    var request = call.GetArgument<ListOnlineEndpointsRequest>("request");
                    var filteredResult = endpointResponses
                        .Where(x => request.Name == null || x.Name == request.Name)
                        .Where(x => request.ComputeType == default || x.ComputeConfiguration?.ComputeType == request.ComputeType)
                        .Where(x => FilterProperties(x.Tags, request.Tags))
                        .Where(x => FilterProperties(x.Properties, request.Properties))
                        .Take(request.Count ?? int.MaxValue)
                        .ToList();

                    return Task.FromResult(new PagedResponse<OnlineEndpointResponseBase, ListOnlineEndpointsRequest>()
                    {
                        Value = filteredResult,
                        ContinuationToken = null
                    });
                });

            A.CallTo(() => idpEndpointController.GetEndpointsAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<int>._, A<string>._, A<string>._, A<string>._, A<InferenceDeployment.WebApi.Contracts.Unversioned.OrderByString>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var tags = call.GetArgument<string>("tags");
                    var properties = call.GetArgument<string>("properties");
                    var count = call.GetArgument<int?>("count");

                    var filteredResult = endpointResponses
                        .Where(x => name == null || x.Name == name)
                        ////.Where(x => request.ComputeType == default || x.ComputeConfiguration?.ComputeType == request.ComputeType)
                        .Where(x => FilterProperties(x.Tags, tags))
                        .Where(x => FilterProperties(x.Properties, properties))
                        .Take(count ?? int.MaxValue)
                        .Select(x => ConvertToResponseBase(x))
                        .ToList();

                    return Task.FromResult(new InferenceDeployment.WebApi.Contracts.V20211001.Common.PagedResponse<EndpointResponse>()
                    {
                        Value = filteredResult,
                        NextLink = null,
                        SkipToken = null
                    });
                });

            A.CallTo(() => mmsEndpointController.DeleteEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");

                    var updatedList = endpointResponses.Where(endpointResponse => endpointResponse.Name != name).ToList();
                    var noContent = updatedList.Count == endpointResponses.Count;
                    endpointResponses = updatedList;

                    if (noContent)
                    {
                        return Task.FromResult(GenerateResponseMessage(HttpStatusCode.NoContent));
                    }

                    FakeOnlineDeploymentSetup.DeleteServices(name);

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                });

            A.CallTo(() => idpEndpointController.DeleteEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("endpointName");

                    var updatedList = endpointResponses.Where(endpointResponse => endpointResponse.Name != name).ToList();
                    var noContent = updatedList.Count == endpointResponses.Count;
                    endpointResponses = updatedList;

                    if (noContent)
                    {
                        return Task.FromResult(GenerateResponseMessage(HttpStatusCode.NoContent));
                    }

                    FakeOnlineDeploymentSetup.DeleteServices(name);

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.DpOperationLocationHeader, new List<string>() { operationId } }
                    }));
                });

            A.CallTo(() => mmsEndpointController.PatchEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<JsonPatchDocument<UpdateOnlineEndpointRequest>>._))
                .ReturnsLazily(call =>
                {
                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                }).Once()
                .Then
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var request = call.GetArgument<JsonPatchDocument<UpdateOnlineEndpointRequest>>("request");
                    var endpointResponse = endpointResponses.Where(inner => inner.Name == name).First();
                    endpointResponse.Tags = (Dictionary<string, string>)(request.Operations.Where(o => o.path.Contains("tags", StringComparison.OrdinalIgnoreCase)).First().value ?? new Dictionary<string, string>());
                    endpointResponse.TrafficRules = (Dictionary<string, int>)(request.Operations.Where(o => o.path.Contains("trafficrules", StringComparison.OrdinalIgnoreCase)).First().value ?? new Dictionary<string, int>());

                    endpointResponses = endpointResponses.Where(inner => inner.Name != name).ToList();
                    endpointResponses.Add(endpointResponse);

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.OK));
                });

            A.CallTo(() => idpEndpointController.UpdateEndpointAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<V20211001.UpdateEndpointRequest>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("endpointName");
                    var request = call.GetArgument<V20211001.UpdateEndpointRequest>("request");
                    var endpointResponse = endpointResponses.Where(inner => inner.Name == name).First();
                    endpointResponse.Tags = request.Tags;
                    endpointResponse.TrafficRules = request.DeploymentWeights;
                    var bodyResponse = JsonConvert.SerializeObject(ConvertToResponseBase(endpointResponse));

                    // Remove-then-add serves as an "update"
                    endpointResponses = endpointResponses.Where(inner => inner.Name != name).ToList();
                    endpointResponses.Add(endpointResponse);

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.DpOperationLocationHeader, new List<string>() { operationId } }
                    }, content: bodyResponse));
                }).Once()
                .Then
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("endpointName");
                    var request = call.GetArgument<V20211001.UpdateEndpointRequest>("request");
                    var endpointResponse = endpointResponses.Where(inner => inner.Name == name).First();
                    endpointResponse.Tags = request.Tags;
                    endpointResponse.TrafficRules = request.DeploymentWeights;
                    var bodyResponse = JsonConvert.SerializeObject(ConvertToResponseBase(endpointResponse));

                    // Remove-then-add serves as an "update"
                    endpointResponses = endpointResponses.Where(inner => inner.Name != name).ToList();
                    endpointResponses.Add(endpointResponse);

                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.OK, content: bodyResponse));
                });
        }

        private static OnlineEndpointResponseBase ConvertToResponseBase(string name, PutOnlineEndpointRequest request, OnlineEndpointProvisioningState state = OnlineEndpointProvisioningState.Succeeded)
        {
            return new OnlineEndpointResponseBase()
            {
                ComputeConfiguration = request.ComputeConfiguration,
                Identity = request.Identity,
                Location = request.Location,
                Description = request.Description,
                Properties = request.Properties,
                State = state,
                Endpoint = new Uri("https://test.endpoint/"),
                SwaggerEndpoint = new Uri("https://test.swagger.endpoint"),
                AuthMode = request.AuthMode,
                Name = name,
                CreatedTime = DateTimeOffset.UtcNow,
                TrafficRules = request.TrafficRules,
                Tags = request.Tags,
            };
        }

        private static OnlineEndpointResponseBase ConvertToResponseBase(string name, V20211001.CreateEndpointRequest request, OnlineEndpointProvisioningState state = OnlineEndpointProvisioningState.Succeeded)
        {
            ComputeConfiguration computeConfiguration = string.IsNullOrEmpty(request.ComputeTarget?.ComputeTargetName)
                ? new ManagedInferenceEndpointConfiguration { ComputeType = ComputeEnvironmentType.MIRGA }
                : new AksEndpointConfiguration { ComputeType = ComputeEnvironmentType.AMLARC, ComputeName = request.ComputeTarget.ComputeTargetName };

            return new OnlineEndpointResponseBase()
            {
                ComputeConfiguration = computeConfiguration,
                Identity = _mapper.Map<ManagedIdentity>(request.Identity),
                Location = request.Location,
                Description = request.Description,
                Properties = request.Properties,
                State = state,
                Endpoint = new Uri("https://test.endpoint/"),
                SwaggerEndpoint = new Uri("https://test.swagger.endpoint"),
                AuthMode = Enum.Parse<AuthMode>(request.AuthMode),
                Name = name,
                CreatedTime = DateTimeOffset.UtcNow,
                TrafficRules = request.DeploymentWeights ?? new Dictionary<string, int>(),
                Tags = request.Tags,
            };
        }

        private static EndpointResponse ConvertToResponseBase(OnlineEndpointResponseBase onlineEndpointResponseBase)
        {
            if (onlineEndpointResponseBase == null)
            {
                return null;
            }

            ComputeTarget computeTarget = null;
            if (onlineEndpointResponseBase.ComputeConfiguration.ComputeType == ComputeEnvironmentType.AMLARC)
            {
                computeTarget = new ComputeTarget()
                {
                    Namespace = "ns",
                    ComputeTargetName = "c1"
                };
            }

            return new EndpointResponse()
            {
                ComputeTarget = computeTarget,
                Identity = _mapper.Map<EndpointIdentity>(onlineEndpointResponseBase.Identity),
                Location = onlineEndpointResponseBase.Location,
                Description = onlineEndpointResponseBase.Description,
                Properties = onlineEndpointResponseBase.Properties,
                ProvisioningState = onlineEndpointResponseBase.State.ToString(),
                EndpointUri = new Uri("https://test.endpoint/"),
                AuthMode = onlineEndpointResponseBase.AuthMode.ToString(),
                Name = onlineEndpointResponseBase.Name,
                CreatedTime = DateTime.UtcNow,
                DeploymentWeights = onlineEndpointResponseBase.TrafficRules,
                Tags = onlineEndpointResponseBase.Tags,
                OperationId = onlineEndpointResponseBase.OperationId,
                CreatedBy = onlineEndpointResponseBase.CreatedBy
            };
        }

        // Based on logic in MMS' CosmosDbQueryHelper.GenerateGetOnlineEndpointsQuery().
        private static bool FilterProperties(IDictionary<string, string> props, string queries)
        {
            return string.IsNullOrWhiteSpace(queries) || queries.Split(',', StringSplitOptions.RemoveEmptyEntries).All(query =>
                query.Split('=', StringSplitOptions.RemoveEmptyEntries) switch
                {
                    { Length: 1 } comps => props != null && props.ContainsKey(comps[0]),
                    { Length: 2 } comps => props != null && props.TryGetValue(comps[0], out var value) && comps[1] == value,
                    _ => true
                });
        }

        private static HttpResponseMessage GenerateResponseMessage(HttpStatusCode statusCode, Dictionary<string, IEnumerable<string>> headerValues = null, string content = null)
        {
            content ??= string.Empty;

            var message = new HttpResponseMessage(statusCode)
            {
                Content = new StringContent(content)
            };

            foreach (var entry in headerValues.EmptyIfNull())
            {
                message.Headers.Add(entry.Key, entry.Value);
            }

            return message;
        }
    }
}
