﻿// <copyright file="FakeCommandJobSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.EnvironmentManagement.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Jobs;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.RunHistory.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeCommandJobSetup
    {
        public static (string Id, CommandJob Job) GetTestCommandJob(WorkspaceContext2 workspace, string jobId = null, CommandJob copyJob = null)
        {
            jobId ??= Guid.NewGuid().ToString();

            var environmentNameVersion = "AzureML-Minimal";
            string testEnvironmentId;
            if (copyJob == null || copyJob.EnvironmentId == null)
            {
                testEnvironmentId = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeEnvironmentContainer, environmentNameVersion);
            }
            else
            {
                testEnvironmentId = copyJob.EnvironmentId;
            }

            var testDataBinding = new InputDataBinding()
            {
                DataId = Guid.NewGuid().ToString(),
                PathOnCompute = Guid.NewGuid().ToString(),
                Mode = DataBindingMode.Mount
            };
            var inputDataBindings = (copyJob?.InputDataBindings != null) ?
                new Dictionary<string, InputDataBinding>(copyJob.InputDataBindings) :
                new Dictionary<string, InputDataBinding>()
                {
                    { Guid.NewGuid().ToString(), testDataBinding }
                };

            var testOutputDataBinding = new OutputDataBinding()
            {
                DatastoreId = Guid.NewGuid().ToString(),
                PathOnCompute = Guid.NewGuid().ToString(),
                PathOnDatastore = Guid.NewGuid().ToString(),
                Mode = DataBindingMode.Mount
            };
            var outputDataBindings = (copyJob?.OutputDataBindings != null) ?
                new Dictionary<string, OutputDataBinding>(copyJob.OutputDataBindings) :
                new Dictionary<string, OutputDataBinding>()
                {
                    { Guid.NewGuid().ToString(), testOutputDataBinding }
                };

            var job = new CommandJob()
            {
                ExperimentName = copyJob?.ExperimentName ?? Guid.NewGuid().ToString(),
                Compute = copyJob?.Compute ?? new ComputeConfiguration()
                {
                    Target = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeCompute, Guid.NewGuid().ToString()),
                    InstanceCount = 1
                },
                CodeId = copyJob?.CodeId ?? MfeResourceVersionArmScope.ToString(workspace, MfeConstants.ArmTypeCodeContainer, Guid.NewGuid().ToString(), "1"),
                Command = "python helloWorld.py",
                Description = copyJob?.Description ?? "job description",
                InputDataBindings = inputDataBindings,
                OutputDataBindings = outputDataBindings,
                EnvironmentId = testEnvironmentId,
                EnvironmentVariables = copyJob?.EnvironmentVariables != null ?
                    new Dictionary<string, string>(copyJob.EnvironmentVariables) :
                    new Dictionary<string, string>()
                    {
                        { "test", "test" }
                    },
                Output = new JobOutputArtifacts
                {
                    DatastoreId = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeDatastore, "fakestorageaccount_azureml"),
                    Path = $"ExperimentRun/dcid.{jobId}"
                },
                Tags = (copyJob?.Tags != null) ? new Dictionary<string, string>(copyJob.Tags) : new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Properties = (copyJob?.Properties != null) ? new Dictionary<string, string>(copyJob.Properties) : new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Status = copyJob?.Status ?? JobStatus.NotStarted,
                Identity = copyJob?.Identity ?? new AmlToken(),
                DisplayName = Guid.NewGuid().ToString(),
            };

            return (jobId, job);
        }

        public static ExperimentDto GetTestExperimentDto(string experimentName)
        {
            return new ExperimentDto()
            {
                Name = experimentName
            };
        }

        public static EnvironmentDefinition GetTestEnvironmentDto()
        {
            return new EnvironmentDefinition()
            {
                Python = new EnvironmentDefinition.PythonSection
                {
                    UserManagedDependencies = true
                },
                Docker = new EnvironmentDefinition.DockerSection()
                {
                    BaseImage = MfeConstants.DefaultCpuImage
                }
            };
        }

        public static Resource<EnvironmentVersion> GetTestEnvironmentVersion()
        {
            return new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    Docker = new DockerImage()
                    {
                        DockerImageUri = MfeConstants.DefaultCpuImage,
                        Platform = new InternalSharedServices.Contracts.DockerImagePlatform()
                        {
                            OperatingSystemType = OperatingSystemType.Linux
                        }
                    }
                }
            };
        }
    }
}
