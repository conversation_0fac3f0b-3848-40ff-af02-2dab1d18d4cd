﻿// <copyright file="FakeStorageProviderSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using FakeItEasy;
using Microsoft.MachineLearning.Common.AzureStorage;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.DataStore.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeStorageProviderSetup
    {
        public static void ConfigureFakes(IResourceStorageProvider storageProvider)
        {
            A.CallTo(() => storageProvider.GetBlobDataStoreAccount(A<DataStoreDto>._, A<WorkspaceMetadata>._, A<bool>._))
                .ReturnsLazily((DataStoreDto datastore, WorkspaceMetadata workspace, bool useConnectionString) => ConnectionStringResourceStorage.GetConnectionResourceStorageUsingSas(datastore.AzureStorageSection.AccountName, "dummysas", "dummyendpoint", datastore.AzureStorageSection.ContainerName));
        }
    }
}
