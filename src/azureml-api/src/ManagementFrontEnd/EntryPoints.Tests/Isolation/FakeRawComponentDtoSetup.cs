﻿// <copyright file="FakeRawComponentDtoSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using FakeItEasy;
using Microsoft.MachineLearning.Component.DevPlatv2.Contracts;
using Microsoft.MachineLearning.Component.DevPlatv2.Contracts.Controllers;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.UnitTests;
using Microsoft.MachineLearning.Project.Contracts;
using Microsoft.MachineLearning.Schema.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeRawComponentDtoSetup
    {
        public static void ConfigureCodeVersionFakes(TestFixtureIsolatedManagementFrontEnd fixture)
        {
            SnapshotDto snapshot = new SnapshotDto();
            snapshot.Name = ComponentVersionTestConstants.CodeName;
            snapshot.Version = ComponentVersionTestConstants.CodeVersion;
            fixture.SetupFakeS2SClient<ISnapshotControllerNewRoutes>(client => A.CallTo(() => client.GetSnapshotMetadata(A<Guid>._, A<string>._, A<string>._, A<string>._, A<Guid>._)).Returns(snapshot));
        }

        public static void ConfigureControllerFakes(IRawComponentsController controller)
        {
            var components = new List<RawComponentDto>();

            A.CallTo(() => controller.RegisterComponent(A<string>._, A<string>._, A<string>._, A<RawComponentDto>._, false, true))
                .ReturnsLazily(call =>
                {
                    var rawComponentDto = call.GetArgument<RawComponentDto>("request");
                    rawComponentDto.SnapshotId = Guid.NewGuid().ToString();
                    RawComponentDto registeredComponentDto = AddNewComponent(rawComponentDto, components, rawComponentDto.Version);
                    return Task.FromResult(registeredComponentDto);
                });

            A.CallTo(() => controller.UpdateComponent(A<string>._, A<string>._, A<string>._, A<string>._, A<RawComponentDto>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var rawComponentDto = call.GetArgument<RawComponentDto>("request");
                    var name = rawComponentDto.Name;
                    var version = rawComponentDto.Version;

                    if (DoesComponentVersionExist(components, name, version))
                    {
                        RawComponentDto component = components.Find(item => item.Name == name && item.Version == version);
                        component.Description = rawComponentDto.Description;
                        component.Tags = rawComponentDto.Tags;
                        component.Properties = rawComponentDto.Properties;
                        return Task.FromResult(component);
                    }
                    else
                    {
                        // this will not happen given CreateOrUpdate functionality
                        return Task.FromResult(rawComponentDto);
                    }
                });

            A.CallTo(() => controller.GetComponent(A<string>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("componentName");
                    var version = call.GetArgument<string>("componentVersion");

                    if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(version) && components.Any(x => x.Name == name && x.Version == version))
                    {
                        return Task.FromResult(components.Find(item => item.Name == name && item.Version == version));
                    }
                    else
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }
                });

            A.CallTo(() => controller.GetComponentVersions(A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var componentName = call.GetArgument<string>("componentName");

                    var componentList = components
                        .Where(x => componentName == null || string.Equals(x.Name, componentName, StringComparison.InvariantCulture))
                        .ToDictionary(x => x.Version, x => x);

                    return Task.FromResult(componentList);
                });

            A.CallTo(() => controller.DeleteComponent(A<string>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .Invokes(call =>
                {
                    var name = call.GetArgument<string>("componentName");
                    var version = call.GetArgument<string>("componentVersion");

                    if (name != null && !string.IsNullOrEmpty(version) && components.Any(x => x.Name == name && x.Version == version))
                    {
                        if (components.Where(item => item.Name == name).Count() == 1)
                        {
                            throw ServiceInvocationExceptionUtil.CreateResponseMessage(HttpStatusCode.BadRequest, "The request version " + version + " is the only active version");
                        }
                        components.Remove(components.Find(item => item.Name == name && item.Version == version));
                    }
                });
        }

        private static bool DoesComponentVersionExist(List<RawComponentDto> components, string name, string version)
        {
            if (components.Any(x => x.Name == name && x.Version == version))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private static RawComponentDto AddNewComponent(RawComponentDto component, List<RawComponentDto> components, string version)
        {
            if (DoesComponentVersionExist(components, component.Name, version))
            {
                var e = ServiceInvocationExceptionUtil.Create(HttpStatusCode.Conflict);
                throw e;
            }
            component.CreatedDate = DateTime.UtcNow;
            component.CreatedBy = new CreatedBy { UserName = Guid.NewGuid().ToString() };
            component.LastModifiedDate = DateTime.UtcNow;
            components.Add(component);
            return component;
        }
    }
}
