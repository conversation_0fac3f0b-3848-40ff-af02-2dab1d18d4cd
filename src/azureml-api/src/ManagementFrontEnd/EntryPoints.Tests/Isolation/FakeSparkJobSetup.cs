﻿// <copyright file="FakeSparkJobSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.EnvironmentManagement.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Jobs;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.RunHistory.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    internal class FakeSparkJobSetup
    {
        public static (string Id, SparkJob Job) GetTestSparkJob(WorkspaceContext2 workspace, string jobId = null, IdentityConfiguration identityToUse = null, SparkJob copyJob = null)
        {
            jobId ??= Guid.NewGuid().ToString();

            var environmentNameVersion = "AzureML-Minimal";
            string testEnvironmentId = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeEnvironmentContainer, environmentNameVersion);

            var testInputDataBindingUriFileWorkspaceblobstore = new InputDataBinding()
            {
                AssetUri = "azureml://datastores/workspaceblobstore/paths/input1/dir/file",
                Mode = DataBindingMode.Direct,
                JobInputType = JobInputType.UriFile
            };
            var inputDataBindings = new Dictionary<string, InputDataBinding>() { { "testInputDataBindingUriFileWorkspaceblobstore", testInputDataBindingUriFileWorkspaceblobstore } };

            var testOutputDataBindingUriFileWorkspaceblobstore = new OutputDataBinding()
            {
                Uri = new UriReference() { File = "azureml://datastores/workspaceblobstore/paths/output1/dir/file" },
                IsAssetJobOutput = true,
                Mode = DataBindingMode.Direct,
                JobOutputType = JobOutputType.UriFile
            };
            var outputDataBindings = new Dictionary<string, OutputDataBinding>() { { "testOutputDataBindingUriFileWorkspaceblobstore", testOutputDataBindingUriFileWorkspaceblobstore } };

            var job = new SparkJob()
            {
                Args = null,
                Conf = GetSparkConf(),
                ExperimentName = copyJob?.ExperimentName ?? Guid.NewGuid().ToString(),
                Compute = copyJob?.Compute ?? new ComputeConfiguration() { Target = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeCompute, Guid.NewGuid().ToString()) },
                CodeId = copyJob?.CodeId ?? MfeResourceVersionArmScope.ToString(workspace, MfeConstants.ArmTypeCodeContainer, Guid.NewGuid().ToString(), "1"),
                Entry = copyJob?.Entry ?? new SparkJobEntry { File = "testprogram.py" },
                Description = copyJob?.Description ?? "job description",
                InputDataBindings = (copyJob?.InputDataBindings != null) ? new Dictionary<string, InputDataBinding>(copyJob.InputDataBindings) : inputDataBindings,
                OutputDataBindings = (copyJob?.OutputDataBindings != null) ? new Dictionary<string, OutputDataBinding>(copyJob.OutputDataBindings) : outputDataBindings,
                EnvironmentId = copyJob?.EnvironmentId ?? testEnvironmentId,
                Tags = (copyJob?.Tags != null) ? new Dictionary<string, string>(copyJob.Tags) : new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Properties = (copyJob?.Properties != null) ? new Dictionary<string, string>(copyJob.Properties) : new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Status = copyJob?.Status ?? JobStatus.NotStarted,
                Identity = copyJob?.Identity ?? identityToUse ?? new UserIdentity(),
                DisplayName = copyJob?.DisplayName ?? Guid.NewGuid().ToString(),
            };

            return (jobId, job);
        }

        public static Dictionary<string, string> GetSparkConf()
        {
            var conf = new Dictionary<string, string>()
            {
                { "spark.executor.cores", "1" },
                { "spark.driver.cores", "1" },
                { "spark.executor.memory", "1g" },
                { "spark.driver.memory", "1g" },
                { "spark.executor.instances", "1" },
            };

            return conf;
        }

        public static Dictionary<string, InputDataBinding> GetJobInput()
        {
            return new Dictionary<string, InputDataBinding>()
            {
                ["fake_test_input_file1"] = new InputDataBinding() { AssetUri = "random/uri", Mode = DataBindingMode.Direct, JobInputType = JobInputType.UriFile },
                ["fake_test_input_folder1"] = new InputDataBinding() { AssetUri = "random/uri", Mode = DataBindingMode.Direct, JobInputType = JobInputType.UriFolder },
                ["fake_test_input_literal1"] = new InputDataBinding() { Value = "2.0", JobInputType = JobInputType.Literal },
            };
        }

        public static Dictionary<string, OutputDataBinding> GetJobOutput(JobOutputType outputType = JobOutputType.UriFile, DataBindingMode mode = DataBindingMode.Direct)
        {
            return new Dictionary<string, OutputDataBinding>()
            {
                ["fake_test_output1"] = new OutputDataBinding() { AssetUri = "random/uri", Mode = mode, JobOutputType = outputType }
            };
        }

        public static ExperimentDto GetTestExperimentDto(string experimentName)
        {
            return new ExperimentDto()
            {
                Name = experimentName
            };
        }

        public static EnvironmentDefinition GetTestEnvironmentDto()
        {
            return new EnvironmentDefinition()
            {
                Python = new EnvironmentDefinition.PythonSection
                {
                    UserManagedDependencies = true
                },
                Docker = new EnvironmentDefinition.DockerSection()
                {
                    BaseImage = MfeConstants.DefaultCpuImage
                }
            };
        }

        public static Resource<EnvironmentVersion> GetTestEnvironmentVersion()
        {
            return new Resource<EnvironmentVersion>()
            {
                Properties = new EnvironmentVersion()
                {
                    Docker = new DockerImage()
                    {
                        DockerImageUri = MfeConstants.DefaultCpuImage,
                        Platform = new InternalSharedServices.Contracts.DockerImagePlatform()
                        {
                            OperatingSystemType = OperatingSystemType.Linux
                        }
                    }
                }
            };
        }
    }
}
