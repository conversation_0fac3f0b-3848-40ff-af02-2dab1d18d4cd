﻿// <copyright file="FakeDatastoreV1Setup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Common.Core.Contracts;
using FakeItEasy;
using Microsoft.MachineLearning.DataStore.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeDatastoreV1Setup
    {
        private static List<DataStoreDto> datastores = new List<DataStoreDto>
            {
                CreateDataStoreDto("datastore 1", MfeTestingConstants.StorageContainer),
                CreateDataStoreDto("datastore 2", "Sdfd"),
                CreateDataStoreDto("datastore 3", "Sdfs"),
                CreateDataStoreDto("datastore 4", "Sds")
            };

        public static void ConfigureFakeBlobDatastore(IDataStoreController controller)
        {
            A.CallTo(() => controller.Get(A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<bool>._, A<int>._, false))
                .Returns(new DataStoreDto
                {
                    DataStoreType = DataStoreType.AzureBlob,
                    AzureStorageSection = new AzureStorageDto
                    {
                        AccountName = "fakestorageaccount",
                        ContainerName = "fakecontainer",
                    },
                });
        }

        /// <summary>
        /// Reasonably functional default behavior.
        /// </summary>
        public static void ConfigureFakes(IDataStoreController controller, IDataStoreDefaultController defaultController, string containerName)
        {
            datastores = new List<DataStoreDto>
            {
                CreateDataStoreDto("datastore 1", containerName),
                CreateDataStoreDto("datastore 2", containerName),
                CreateDataStoreDto("datastore 3", containerName),
                CreateDataStoreDto("datastore 4", containerName)
            };

            string defaultName = MfeTestingConstants.DatastoreName;

            A.CallTo(() => controller.Create(A<string>._, A<string>._, A<string>._, A<DataStoreDto>._, A<bool>._, A<bool>._))
                .Invokes(call =>
                {
                    var datastore = call.GetArgument<DataStoreDto>("dto");
                    if (datastores.Any(x => x.Name.Equals(datastore.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.Conflict, DatastoreErrorCodes.DatastoreNameInUse.ToString());
                    }
                    else
                    {
                        datastore.CreatedBy = datastore.ModifiedBy = new User { UserName = Guid.NewGuid().ToString() };
                        datastore.CreatedTime = datastore.ModifiedTime = DateTimeOffset.UtcNow;
                        datastores.Add(datastore);
                    }
                });

            A.CallTo(() => controller.Delete(A<string>._, A<string>._, A<string>._, A<string>._,A<bool>._))
                .Invokes(call => datastores.RemoveAll(x => x.Name.Equals(call.GetArgument<string>("name"), StringComparison.OrdinalIgnoreCase)));

            A.CallTo(() => controller.DeleteAll(A<string>._, A<string>._, A<string>._))
                .Invokes(call => datastores.Clear());

            A.CallTo(() => controller.Get(A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<bool>._, A<int>._,false))
                .ReturnsLazily(call => Task.FromResult(datastores.FirstOrDefault(x => x.Name.Equals(call.GetArgument<string>("name"), StringComparison.OrdinalIgnoreCase))
                    ?? throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found")));

            A.CallTo(() => controller.List(A<string>._, A<string>._, A<string>._, A<List<string>>._, A<string>._, A<int>._, A<bool>._, A<string>._, A<string>._, A<bool>._, A<List<string>>._,false))
                .ReturnsLazily(call =>
                {
                    var continuationToken = call.GetArgument<string>("continuationToken");
                    var containerNames = call.GetArgument<List<string>>("dataStoreNames");
                    var searchText = call.GetArgument<string>("searchText");
                    var orderBy = call.GetArgument<string>("orderBy");
                    var orderByAsc = call.GetArgument<bool>("orderByAsc");

                    var index = continuationToken == null ? 0 : int.Parse(continuationToken, CultureInfo.InvariantCulture);

                    // Datastore treats the containerNames more like a batch GET.
                    if (containerNames.Any(name => !datastores.Any(x => x.Name.Equals(name, StringComparison.OrdinalIgnoreCase))))
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }

                    var filteredDatastores = datastores
                        .Where(x => containerNames.Count == 0 || containerNames.Any(name => x.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
                        .Where(x => searchText == null || x.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase))
                        .OrderBy(
                            x => orderBy == "name" ? x.Name : string.Empty,
                            Comparer<string>.Create((a, b) => orderByAsc ? string.Compare(a, b, StringComparison.OrdinalIgnoreCase) : string.Compare(b, a, StringComparison.OrdinalIgnoreCase)))
                        .ToList();

                    return Task.FromResult(new PaginatedResult<DataStoreDto>
                    {
                        Value = index < filteredDatastores.Count ? new List<DataStoreDto> { filteredDatastores[index] } : new List<DataStoreDto>(),
                        ContinuationToken = index + 1 < filteredDatastores.Count ? (index + 1).ToString(CultureInfo.InvariantCulture) : null,
                    });
                });

            A.CallTo(() => controller.Update(A<string>._, A<string>._, A<string>._, A<string>._, A<DataStoreDto>._, A<bool>._, A<bool>._))
                .Invokes(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var datastore = call.GetArgument<DataStoreDto>("dto");
                    datastore.ModifiedBy = new User { UserName = Guid.NewGuid().ToString() };
                    datastore.ModifiedTime = DateTimeOffset.UtcNow;
                    datastores.RemoveAll(x => x.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
                    datastores.Add(datastore);
                });

            A.CallTo(() => defaultController.Get(A<string>._, A<string>._, A<string>._, A<bool>._, false))
                .ReturnsLazily(() => datastores.FirstOrDefault(x => x.Name.Equals(defaultName, StringComparison.OrdinalIgnoreCase))
                    ?? throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found"));

            A.CallTo(() => defaultController.Set(A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    defaultName = call.GetArgument<string>("name");
                    return Task.CompletedTask;
                });
        }

        public static void AddToDatastores(string name, string containerName)
        {
            datastores.Add(CreateDataStoreDto(name, containerName));
        }

        private static DataStoreDto CreateDataStoreDto(string name, string containerName)
        {
            return new DataStoreDto
            {
                Name = name,
                DataStoreType = DataStoreType.AzureBlob,
                HasBeenValidated = true,
                AzureStorageSection = new AzureStorageDto
                {
                    AccountName = "fakestorageaccount",
                    AccountKey = "account key",
                    ContainerName = containerName,
                    CredentialType = AzureStorageCredentialTypes.AccountKey,
                    Credential = "account key",
                    IsSas = false,
                },
            };
        }
    }
}
