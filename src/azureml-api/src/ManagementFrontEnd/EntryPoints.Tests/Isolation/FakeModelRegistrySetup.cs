﻿// <copyright file="FakeModelRegistrySetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Common.Core.Contracts;
using FakeItEasy;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.ModelRegistry.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeModelRegistrySetup
    {
        public static void ConfigureFakes(IAssetController assetController, IModelContainerController containerController, IModelController modelController)
        {
            var assets = new Dictionary<string, Asset>();

            var containers = new Dictionary<string, ModelContainerResponse>
            {
                { "container1", new ModelContainerResponse { Name = "container1" } },
            };

            var models = new Dictionary<string, Model>();

            var mapper = new MapperConfiguration(config =>
            {
                config.CreateMap<ModelContainerRequest, ModelContainerResponse>();
                config.CreateMap<ModelContainerResponse, ModelContainerRequest>();
                config.CreateMap<ModelContainerRequest, ModelContainerResponse>();
                config.CreateMap<Model, UpdateModel>();
                config.CreateMap<UpdateModel, Model>();
            }).CreateMapper();

            // Assets.
            A.CallTo(() => assetController.CreateAsset(A<Guid>._, A<string>._, A<string>._, A<Asset>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var asset = call.GetArgument<Asset>("asset");
                    asset.Id = Guid.NewGuid().ToString();
                    assets[asset.Id] = asset;
                    return Task.FromResult(assets[asset.Id]);
                });

            A.CallTo(() => assetController.GetAsset(A<Guid>._, A<string>._, A<string>._, A<string>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var id = call.GetArgument<string>("id");
                    var asset = assets.ContainsKey(id) ? assets[id] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    return Task.FromResult(asset);
                });

            // Model containers.
            A.CallTo(() => containerController.CreateModelContainer(A<Guid>._, A<string>._, A<string>._, A<ModelContainerRequest>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var request = call.GetArgument<ModelContainerRequest>("modelContainer");
                    containers[request.Name] = mapper.Map<ModelContainerResponse>(request);
                    containers[request.Name].CreatedBy = containers[request.Name].ModifiedBy = new User { UserName = Guid.NewGuid().ToString() };
                    containers[request.Name].CreatedTime = containers[request.Name].ModifiedTime = DateTime.UtcNow;
                    containers[request.Name].LatestVersion = null;
                    containers[request.Name].NextVersion = "1";
                    return Task.FromResult(containers[request.Name]);
                });

            A.CallTo(() => containerController.DeleteModelContainer(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var removed = containers.Remove(name);
                    return Task.FromResult(new HttpResponseMessage(removed ? HttpStatusCode.OK : HttpStatusCode.NoContent));
                });

            A.CallTo(() => containerController.GetModelContainer(A<Guid>._, A<string>._, A<string>._, A<string>._, A<List<string>>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var container = containers.ContainsKey(name) ? containers[name] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    container.LatestVersions = models.Values.GroupBy(x => x.Stage).Select(g => Enumerable.MaxBy(g, x => x.CreatedTime)).ToList();
                    return Task.FromResult(container);
                });

            A.CallTo(() => containerController.ListModelContainers(A<Guid>._, A<string>._, A<string>._, A<int?>._, A<string>._, A<CancellationToken>._, A<ListViewType>._))
                .ReturnsLazily(call => Task.FromResult(new PaginatedResult<ModelContainerResponse>(containers.Values)));

            A.CallTo(() => containerController.UpdateModelContainer(A<Guid>._, A<string>._, A<string>._, A<string>._, A<JsonPatchDocument<ModelContainerRequest>>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("name");
                    var patch = call.GetArgument<JsonPatchDocument<ModelContainerRequest>>("patch");
                    var request = mapper.Map<ModelContainerRequest>(containers[name]);
                    patch.ApplyTo(request);
                    mapper.Map(request, containers[name]);
                    containers[name].ModifiedTime = DateTime.UtcNow;
                    return Task.FromResult(containers[name]);
                });

            // Models.
            A.CallTo(() => modelController.DeleteModelAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var id = call.GetArgument<string>("id");
                    var removed = models.Remove(id);
                    return Task.FromResult(new HttpResponseMessage(removed ? HttpStatusCode.OK : HttpStatusCode.NoContent));
                });

            A.CallTo(() => modelController.GetModelAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<bool>._, A<bool>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var id = call.GetArgument<string>("id");
                    var model = models.ContainsKey(id) ? models[id] : throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    return Task.FromResult(model);
                });

            A.CallTo(() => modelController.ListModelsAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<int?>._, A<int?>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, null, false, A<string>._, A<string>._, A<ListViewType>._, A<bool>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    return Task.FromResult(new PaginatedResult<Model>(models.Values));
                });

            A.CallTo(() => modelController.ListModelsAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, 1, A<int?>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, A<string>._, OrderString.CreatedAtDesc.ToString(), A<bool>._, A<string>._, A<string>._, A<ListViewType>._, A<bool>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    return Task.FromResult(new PaginatedResult<Model>(new List<Model>() { models.Values.Last() }));
                });

            A.CallTo(() => modelController.RegisterModel(A<Guid>._, A<string>._, A<string>._, A<Model>._, A<bool>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var model = call.GetArgument<Model>("model");
                    containers[model.Name] = new ModelContainerResponse() { Name = model.Name };
                    models[model.Id] = model;
                    models[model.Id].CreatedBy = containers[model.Name].CreatedBy = new User { UserName = Guid.NewGuid().ToString() };
                    models[model.Id].CreatedTime = containers[model.Name].CreatedTime = DateTime.UtcNow;
                    models[model.Id].ModifiedBy = containers[model.Name].ModifiedBy = models[model.Id].CreatedBy;
                    models[model.Id].ModifiedTime = containers[model.Name].ModifiedTime = models[model.Id].CreatedTime;
                    containers[model.Name].LatestVersion = $"{model.Version}";
                    containers[model.Name].NextVersion = $"{model.Version + 1}";

                    return Task.FromResult(models[model.Id]);
                });

            A.CallTo(() => modelController.UpdateModel(A<Guid>._, A<string>._, A<string>._, A<string>._, A<JsonPatchDocument<UpdateModel>>._, A<CancellationToken>._))
                .ReturnsLazily(call =>
                {
                    var id = call.GetArgument<string>("id");
                    var patch = call.GetArgument<JsonPatchDocument<UpdateModel>>("patch");
                    var updateModel = mapper.Map<UpdateModel>(models[id]);
                    patch.ApplyTo(updateModel);
                    mapper.Map(updateModel, models[id]);
                    models[id].ModifiedTime = DateTime.UtcNow;
                    return Task.FromResult(models[id]);
                });
        }
    }
}
