﻿// <copyright file="FakeDatasetVersionSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Common.Core.Contracts;
using FakeItEasy;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Dataset.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeDatasetVersionSetup
    {
        public static void ConfigureFakes(IDatasetsController controller)
        {
            var datasets = new List<DatasetDto>();

            // create: "versioning" update is supported through register (new version update), throw an error on conflict
            A.CallTo(() => controller.RegisterFromDataPath(A<Guid>._, A<string>._, A<string>._, A<DatasetRequestDto>._, A<bool>._, A<bool>._, A<bool>._, A<bool>._, A<string>._))
               .ReturnsLazily(call =>
               {
                   var datasetRequestDto = call.GetArgument<DatasetRequestDto>("request");
                   var version = call.GetArgument<string>("userVersionId");

                   DatasetDto translatedDataset = ConvertDataRequestDto(datasetRequestDto, version);
                   var datasetDto = AddNewDataset(translatedDataset, datasets, translatedDataset.Latest.VersionId);
                   return Task.FromResult(datasetDto);
               });

            A.CallTo(() => controller.Register(A<Guid>._, A<string>._, A<string>._, A<DatasetDto>._, A<bool>._, A<bool>._, A<bool>._, A<bool>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var datasetDto = call.GetArgument<DatasetDto>("datasetDto");
                    datasetDto = AddNewDataset(datasetDto, datasets, datasetDto.Latest.VersionId);
                    return Task.FromResult(datasetDto);
                });

            A.CallTo(() => controller.UpdateDataset(A<Guid>._, A<string>._, A<string>._, A<Guid>._, A<DatasetDto>._, true))
                .ReturnsLazily(call =>
                {
                    var dataDto = call.GetArgument<DatasetDto>("dto");
                    var name = dataDto.Name;
                    var version = dataDto.Latest.VersionId;

                    if (DoesDatasetVersionExist(datasets, name, version))
                    {
                        DatasetDto dataset = datasets.Find(item => string.Equals(item.Name, name, StringComparison.OrdinalIgnoreCase) && item.Latest.VersionId == version);
                        dataset.Description = dataDto.Description;
                        dataset.Tags = dataDto.Tags;
                        return Task.FromResult(dataset);
                    }
                    else
                    {
                        // this will not happen given CreateOrUpdate functionality
                        return Task.FromResult(dataDto);
                    }
                });

            A.CallTo(() => controller.GetDatasetByName(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var name = call.GetArgument<string>("datasetName");
                    var version = call.GetArgument<string>("versionId");

                    // "latest"
                    if (string.IsNullOrEmpty(version) && datasets.Any(x => string.Equals(x.Name, name, StringComparison.OrdinalIgnoreCase)))
                    {
                        var datasetListSearch = datasets
                            .Where(x => x.Name.Contains(name, StringComparison.OrdinalIgnoreCase))
                            .Reverse()
                            .ToList();
                        return Task.FromResult(datasetListSearch.FirstOrDefault());
                    }

                    if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(version) && datasets.Any(x => string.Equals(x.Name, name, StringComparison.OrdinalIgnoreCase) && x.Latest.VersionId == version))
                    {
                        return Task.FromResult(datasets.Find(item => string.Equals(item.Name, name, StringComparison.OrdinalIgnoreCase) && item.Latest.VersionId == version));
                    }
                    else
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }
                });

            A.CallTo(() => controller.List(A<Guid>._, A<string>._, A<string>._, A<List<string>>._, A<string>._, A<bool>._, A<string>._, A<string>._, A<int?>._, A<bool>._, A<string>._, A<bool>._, A<List<string>>._))
                .ReturnsLazily(call =>
                {
                    var searchText = call.GetArgument<string>("searchText");

                    var datasetList = datasets
                        .Where(x => searchText == null || string.Equals(x.Name, searchText, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                    return Task.FromResult(new PaginatedResult<DatasetDto>
                    {
                        Value = datasetList,
                        ContinuationToken = call.GetArgument<string>("continuationToken"),
                    });
                });

            A.CallTo(() => controller.GetAllDatasetDefinitions(A<Guid>._, A<string>._, A<string>._, A<Guid>._, A<string>._, A<int?>._, A<ListViewType>._))
                .ReturnsLazily(call =>
                {
                    var datasetId = call.GetArgument<Guid>("datasetId");

                    var datasetList = datasets
                        .Where(x => x.DatasetId.Equals(datasetId))
                        .Select(x => x.Latest)
                        .ToList();
                    return Task.FromResult(new PaginatedResult<DatasetDefinitionDto>
                    {
                        Value = datasetList,
                        ContinuationToken = call.GetArgument<string>("continuationToken"),
                    });
                });

            A.CallTo(() => controller.Unregister(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .Invokes(call => datasets.RemoveAll(x => string.Equals(x.Name, call.GetArgument<string>("name"), StringComparison.OrdinalIgnoreCase)));

            A.CallTo(() => controller.DeleteDatasetDefinition(A<Guid>._, A<string>._, A<string>._, A<Guid>._, A<string>._))
                .Invokes(call =>
                {
                    var datasetId = call.GetArgument<Guid>("datasetId");
                    var version = call.GetArgument<string>("version");

                    if (!string.IsNullOrEmpty(version))
                    {
                        datasets.RemoveAll(item => item.DatasetId == datasetId && item.Latest.VersionId == version);
                    }
                });
        }

        private static DatasetDto ConvertDataRequestDto(DatasetRequestDto datasetRequestDto, string version)
        {
            var datasetDto = new DatasetDto()
            {
                Name = datasetRequestDto.Name,
                Description = datasetRequestDto.Description,
                DatasetId = Guid.NewGuid(),
                Tags = datasetRequestDto.Tags,
                Latest = new DatasetDefinitionDto
                {
                    SavedDatasetId = Guid.NewGuid().ToString(),
                    FileType = datasetRequestDto.General.FileType,
                    Properties = datasetRequestDto.General.Properties,
                    DataPath = new DatasetPathDto()
                    {
                        HttpUrl = datasetRequestDto.DataPath.HttpUrl,
                    },
                    VersionId = version
                },
                IsVisible = datasetRequestDto.IsVisible
            };

            if (string.IsNullOrEmpty(datasetRequestDto.DataPath.HttpUrl))
            {
                datasetDto.Latest.DataPath.DatastoreName = datasetRequestDto.DataPath.DatastoreName;
                datasetDto.Latest.DataPath.RelativePath = datasetRequestDto.DataPath.RelativePath;
            }
            return datasetDto;
        }

        // copy DatasetDto only properties and version to Definition
        private static DatasetDto AddDatasetDefinition(DatasetDto datasetDto, string version)
        {
            datasetDto.Latest.VersionId = version;
            datasetDto.Latest.Tags = datasetDto.Tags;
            datasetDto.Latest.Description = datasetDto.Description;
            datasetDto.Latest.CreatedTime = DateTimeOffset.UtcNow;
            datasetDto.Latest.CreatedBy = new User { UserName = Guid.NewGuid().ToString() };
            datasetDto.Latest.ModifiedBy = new User { UserName = Guid.NewGuid().ToString() };
            datasetDto.Latest.ModifiedTime = DateTimeOffset.UtcNow;
            return datasetDto;
        }

        private static bool DoesDatasetVersionExist(List<DatasetDto> datasets, string name, string version)
        {
            if (datasets.Any(x => string.Equals(x.Name, name, StringComparison.OrdinalIgnoreCase) && x.Latest.VersionId == version))
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        private static DatasetDto AddNewDataset(DatasetDto datasetDto, List<DatasetDto> datasets, string version)
        {
            if (DoesDatasetVersionExist(datasets, datasetDto.Name, version))
            {
                var e = ServiceInvocationExceptionUtil.Create(HttpStatusCode.Conflict);
                e.ErrorResponse.Error.DebugInfo = new DebugInfoResponse();
                e.ErrorResponse.Error.DebugInfo.Type = "Common.WebApi.Exceptions.ResourceConflictException";
                throw e;
            }

            datasetDto.DatasetId = Guid.NewGuid();
            datasetDto.Latest.SavedDatasetId = Guid.NewGuid().ToString();
            if (datasets.Any(x => string.Equals(x.Name, datasetDto.Name, StringComparison.OrdinalIgnoreCase)))
            {
                var sameDatasetFamily = datasets.Find(item => string.Equals(item.Name, datasetDto.Name, StringComparison.OrdinalIgnoreCase));
                datasetDto.DatasetId = sameDatasetFamily.DatasetId;
            }
            datasetDto = AddDatasetDefinition(datasetDto, version);
            datasets.Add(datasetDto);
            return datasetDto;
        }
    }
}
