﻿// <copyright file="FakeTriggerSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeTriggerSetup
    {
        public static PipelineJob GetPipelineJobWithTrigger()
        {
            var pipelineJob = PipelineJobTestConstants.GetNewPipelineJobMfeV20220201Preview();
            pipelineJob.Schedule = GetRecurrenceSchedule();
            return pipelineJob;
        }

        public static ScheduleBase GetRecurrenceSchedule()
        {
            return new ScheduleBase
            {
                ScheduleStatus = ScheduleStatus.Enabled,
                ScheduleType = ScheduleType.Recurrence,
                StartTime = DateTime.UtcNow,
                TimeZone = "UTC",
                Frequency = RecurrenceFrequency.Minute,
                Interval = 10
            };
        }

        public static ScheduleBase GetCronSchedule()
        {
            return new ScheduleBase
            {
                ScheduleStatus = ScheduleStatus.Enabled,
                ScheduleType = ScheduleType.Cron,
                StartTime = DateTime.UtcNow,
                TimeZone = "UTC",
                Expression = "* * * * *"
            };
        }
    }
}
