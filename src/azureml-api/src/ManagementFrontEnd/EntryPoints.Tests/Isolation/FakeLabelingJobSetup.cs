﻿// <copyright file="FakeLabelingJobSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using Common.Core.Contracts;
using FakeItEasy;
using Microsoft.MachineLearning.Labeling.API.Contracts;
using Microsoft.MachineLearning.Labeling.API.EntryPoints;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeLabelingJobSetup
    {
        public const string TestLabelCategory = "DefaultCategory";
        public const string TestLabelClass1 = "TestLabelClass1";
        public const string TestLabelClass2 = "TestLabelClass2";

        private static LabelExportState labelExportState = LabelExportState.Completed;

        private static List<ProjectDetails> projects = new List<ProjectDetails>();
        private static Dictionary<string, Labeling.API.Contracts.ExportFormatType> projectExports = new Dictionary<string, Labeling.API.Contracts.ExportFormatType>();

        public static void SetLabelExportState(LabelExportState exportState = LabelExportState.Completed)
        {
            labelExportState = exportState;
        }

        public static void SetAnnotationType(string projectName, int annotationType)
        {
            ProjectDetails match = projects.Find(x => x.Name == projectName);
            if (match != null)
            {
                switch (match.MediaProperties.MediaType)
                {
                    case Labeling.API.Contracts.MediaType.Image:
                        var imageMediaProperties = match.MediaProperties as ImageMediaProperties;
                        imageMediaProperties.AnnotationType = (Labeling.API.Contracts.ImageAnnotationType)annotationType;
                        break;
                    case Labeling.API.Contracts.MediaType.Text:
                        var textMediaProperties = match.MediaProperties as TextMediaProperties;
                        textMediaProperties.AnnotationType = (Labeling.API.Contracts.TextAnnotationType)annotationType;
                        break;
                }
            }
        }

        public static void SetMediaType(string projectName, int mediaType)
        {
            ProjectDetails match = projects.Find(x => x.Name == projectName);
            if (match != null)
            {
                match.MediaProperties.MediaType = (Labeling.API.Contracts.MediaType)mediaType;
            }
        }

        public static void ConfigureFakes(ILabelingS2SController controller)
        {
            A.CallTo(() => controller.ProjectCreateOrUpdateProject(A<Guid>._, A<string>._, A<string>._, A<string>._, A<ProjectDetails>._))
                .ReturnsLazily(call =>
                {
                    ProjectDetails project = call.GetArgument<ProjectDetails>("project");
                    string projectName = call.GetArgument<string>("projectName");
                    project.Name = projectName;

                    ProjectDetails match = projects.Find(x => x.Name == project.Name);
                    if (match != null)
                    {
                        projects.Remove(match);
                    }

                    ProjectDetails result = GetProjectDetails(project, match != null);
                    projects.Add(result);

                    return Task.FromResult(result);
                });

            A.CallTo(() => controller.ProjectGetProject(A<Guid>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    bool includeProjectInstructions = call.GetArgument<bool>("includeProjectInstructions");
                    bool includeLabelCategories = call.GetArgument<bool>("includeLabelCategories");

                    string projectName = call.GetArgument<string>("projectName");

                    ProjectDetails project = projects.FirstOrDefault(x => x.Name == projectName);
                    if (project == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }

                    if (includeProjectInstructions != true)
                    {
                        project.Instructions = null;
                    }
                    if (includeLabelCategories != true)
                    {
                        project.LabelCategories = null;
                    }

                    return Task.FromResult(project);
                });

            A.CallTo(() => controller.ProjectGetProjects(A<Guid>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<string>._, A<int?>._))
                .ReturnsLazily(call =>
                {
                    string continuationToken = call.GetArgument<string>("continuationToken");
                    int? pageSize = call.GetArgument<int?>("pageSize");
                    string annotationTypes = call.GetArgument<string>("annotationTypes");

                    int index = continuationToken == null ? 0 : int.Parse(continuationToken, CultureInfo.InvariantCulture);

                    IEnumerable<ProjectDetails> result = null;
                    if (!string.IsNullOrEmpty(annotationTypes))
                    {
                        IDictionary<string, string[]> annotationTypesDictionary = null;
                        annotationTypesDictionary = JsonConvert.DeserializeObject<IDictionary<string, string[]>>(HttpUtility.UrlDecode(annotationTypes));
                        List<ProjectDetails> filteredProjects = new List<ProjectDetails>();
                        foreach (var item in annotationTypesDictionary)
                        {
                            string mediaType = item.Key;
                            foreach (var project in projects)
                            {
                                if (project.MediaProperties.MediaType.ToString() == mediaType)
                                {
                                    foreach (var annotationType in item.Value)
                                    {
                                        switch (project.MediaProperties.MediaType)
                                        {
                                            case Labeling.API.Contracts.MediaType.Image:
                                                var imageMediaProperties = project.MediaProperties as ImageMediaProperties;
                                                if (imageMediaProperties.AnnotationType.ToString() == annotationType)
                                                {
                                                    filteredProjects.Add(project);
                                                }
                                                break;
                                            case Labeling.API.Contracts.MediaType.Text:
                                                var textMediaProperties = project.MediaProperties as TextMediaProperties;
                                                if (textMediaProperties.AnnotationType.ToString() == annotationType)
                                                {
                                                    filteredProjects.Add(project);
                                                }
                                                break;
                                        }
                                    }
                                }
                            }
                        }
                        result = filteredProjects.Skip(index);
                    }
                    else
                    {
                        result = projects.Skip(index);
                    }

                    if (pageSize != null)
                    {
                        result = result.Take((int)pageSize);
                    }

                    return Task.FromResult(new PaginatedResult<ProjectDetails>
                    {
                        Value = index < projects.Count ? result.ToList() : new List<ProjectDetails>(),
                        ContinuationToken = pageSize != null && index + pageSize < projects.Count ? (index + pageSize).ToString() : null,
                    });
                });

            A.CallTo(() => controller.ProjectDeleteProject(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .Invokes(call => projects.RemoveAll(x => x.Name == call.GetArgument<string>("projectName")));

            A.CallTo(() => controller.ProjectPauseProject(A<Guid>._, A<string>._, A<string>._, A<string>._))
               .Invokes(call =>
               {
                   ProjectDetails project = projects.FirstOrDefault(p => p.Name == call.GetArgument<string>("projectName"));

                   if (projects == null)
                   {
                       throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                   }
                   else if (project.State == ProjectState.Running || project.State == ProjectState.Paused)
                   {
                       project.State = ProjectState.Paused;
                   }
                   else
                   {
                       throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.BadRequest, "UserError", "Invalid state to pause.");
                   }
               });

            A.CallTo(() => controller.ProjectResumeProject(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .Invokes(call =>
                {
                    ProjectDetails project = projects.FirstOrDefault(p => p.Name == call.GetArgument<string>("projectName"));

                    if (projects == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }
                    else if (project.State == ProjectState.Paused || project.State == ProjectState.Running || project.State == ProjectState.Resuming)
                    {
                        project.State = ProjectState.Running;
                    }
                    else
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.BadRequest, "UserError", "Invalid state to resume.");
                    }
                });

            A.CallTo(() => controller.ExportLabelsExportLabels(A<Guid>._, A<string>._, A<string>._, A<string>._, A<Labeling.API.Contracts.ExportFormatType>._))
               .ReturnsLazily(call =>
               {
                   string projectName = call.GetArgument<string>("projectName");
                   Labeling.API.Contracts.ExportFormatType type = call.GetArgument<Labeling.API.Contracts.ExportFormatType>("exportFormatType");

                   ProjectDetails project = projects.FirstOrDefault(p => p.Name == projectName);
                   if (projects == null)
                   {
                       throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                   }
                   else
                   {
                       Guid exportId = Guid.NewGuid();
                       projectExports[projectName] = type;

                       return Task.FromResult(exportId);
                   }
               });

            A.CallTo(() => controller.ExportLabelsGetExportSummary(A<Guid>._, A<string>._, A<string>._, A<string>._, A<Guid>._))
                .ReturnsLazily(call =>
                {
                    string projectName = call.GetArgument<string>("projectName");
                    Guid exportId = call.GetArgument<Guid>("exportId");

                    if (!projectExports.ContainsKey(projectName))
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "UserError", "Not found");
                    }
                    else
                    {
                        return Task.FromResult(GetExportSummary(projectExports[projectName]));
                    }

                    Labeling.API.Contracts.ExportSummary GetExportSummary(Labeling.API.Contracts.ExportFormatType type)
                        => type switch
                        {
                            Labeling.API.Contracts.ExportFormatType.Coco => new Labeling.API.Contracts.CocoExportSummary(
                                exportId,
                                projectName,
                                requestStartTimeUtc: DateTime.UtcNow,
                                requestEndTimeUtc: DateTime.UtcNow,
                                exportedRowCount: 10,
                                labelExportState,
                                snapshotPath: "/",
                                containerName: string.Empty),
                            Labeling.API.Contracts.ExportFormatType.Dataset => new Labeling.API.Contracts.DatasetExportSummary(
                                exportId,
                                projectName,
                                requestStartTimeUtc: DateTime.UtcNow,
                                requestEndTimeUtc: DateTime.UtcNow,
                                exportedRowCount: 10,
                                labelExportState,
                                labeledDatasetId: Guid.NewGuid(),
                                labeledDatasetName: Guid.NewGuid().ToString()),
                            Labeling.API.Contracts.ExportFormatType.CSV => new Labeling.API.Contracts.CsvExportSummary(
                                exportId,
                                projectName,
                                requestStartTimeUtc: DateTime.UtcNow,
                                requestEndTimeUtc: DateTime.UtcNow,
                                exportedRowCount: 10,
                                labelExportState,
                                snapshotPath: "/",
                                containerName: string.Empty),
                            _ => throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.BadRequest, "UserError", "Export format not supported")
                        };
                });
        }

        private static ProjectDetails GetProjectDetails(ProjectDetails project, bool isUpdate)
        {
            MediaPropertiesBase mediaProperties;

            if (project.MediaProperties.MediaType == Labeling.API.Contracts.MediaType.Image)
            {
                mediaProperties = new ImageMediaProperties(Labeling.API.Contracts.ImageAnnotationType.BoundingBox);
            }
            else
            {
                mediaProperties = new TextMediaProperties(Labeling.API.Contracts.TextAnnotationType.Classification);
            }

            return new ProjectDetails(
                id: Guid.NewGuid(),
                name: project.Name,
                mediaProperties: mediaProperties,
                datasetConfiguration: project.DatasetConfiguration,
                mlAssistConfiguration: project.MlAssistConfiguration,
                labelCategories: project.LabelCategories,
                instructions: project.Instructions,
                createdTimeUtc: DateTime.UtcNow,
                createdBy: null,
                state: isUpdate ? ProjectState.Paused : ProjectState.Running,
                projectVersion: new ProjectVersion(major: 1, minor: 0, reason: string.Empty),
                progressMetrics: new Labeling.API.Contracts.ProgressMetrics(
                    totalDatapointCount: 0,
                    completedDatapointCount: 0,
                    skippedDatapointCount: 0,
                    incrementalDatasetLastRefreshTime: DateTime.UtcNow),
                statusMessages: new List<Labeling.API.Contracts.StatusMessage>());
        }
    }
}
