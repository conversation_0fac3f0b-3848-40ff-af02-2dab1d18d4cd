﻿// <copyright file="FakeSweepJobSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Net.Http;
using FakeItEasy;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.WebApi.TeamAccounts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Jobs;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Microsoft.MachineLearning.ManagementFrontEnd.Services;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Clients;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Internal;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.ServiceContracts;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeSweepJobSetup
    {
        public static void ConfigureFakesForCreate(SweepJob createSweepJob, TestFixtureIsolatedManagementFrontEnd fixture, ISweepJobConverter converter)
        {
            var parentRunId = Guid.NewGuid().ToString();
            var hyperDriveRunId = Guid.NewGuid().ToString();
            var experimentName = createSweepJob.ExperimentName ?? Guid.NewGuid().ToString();
            WorkspaceContext2 context = new WorkspaceContext2(fixture.WorkspaceId, fixture.WorkspaceName, fixture.ResourceGroupName, fixture.SubscriptionId, null, null, null);
            var (_, testSweepJob) = GetTestSweepJob(fixture.SubscriptionId.ToString(), fixture.ResourceGroupName, fixture.WorkspaceName, createSweepJob);
            var resource = new Resource<SweepJob>() { Properties = testSweepJob };
            var discoveryClient = A.Fake<IDiscoveryServiceClient>();
            A.CallTo(() => discoveryClient.StudioEndpoint).Returns(MfeConstants.PublicPortalDomain);
            A.CallTo(() => discoveryClient.ApiEndpoint).Returns(MfeConstants.PublicPortalDomain);
            var jobConverter = A.Fake<IJobConverter>();
            var dataVersionService = A.Fake<IDataVersionServiceOld>();
            var codeVersionService = A.Fake<ICodeVersionService>();
            converter = new SweepJobConverter(A.Fake<IOptionsMonitor<ExperimentationHostConfiguration>>(), discoveryClient, jobConverter, A.Fake<ILoggerFactory>());

            fixture.SetupFakeS2SClient<IHyperDriveControllerMfe>(client => A.CallTo(() => client.SubmitExperiment(fixture.SubscriptionId, fixture.ResourceGroupName, fixture.WorkspaceName, experimentName, hyperDriveRunId, A.Fake<MultipartFormDataContent>()))
                .Returns(new SubmitResponseDto()));
        }

        public static (string Id, SweepJob Job) GetTestSweepJob(WorkspaceContext2 workspace, InternalSharedServices.Contracts.SweepJob copyJob = null, string jobId = null)
        {
            return GetTestSweepJob(workspace.SubscriptionId.ToString(), workspace.ResourceGroup, workspace.WorkspaceName, copyJob, jobId);
        }

        public static (string Id, SweepJob Job) GetTestSweepJob(string subscriptionId, string resourceGroup, string workspaceName, SweepJob copySweepJob = null, string jobId = null, string environmentNameVersion = null)
        {
            jobId ??= Guid.NewGuid().ToString();
            var workspace = new WorkspaceContext2(Guid.NewGuid(), workspaceName, resourceGroup, Guid.Parse(subscriptionId), null, null, null);
            environmentNameVersion ??= "AzureML-Minimal";
            var sweepJob = new SweepJob()
            {
                ExperimentName = copySweepJob?.ExperimentName ?? Guid.NewGuid().ToString(),
                Compute = copySweepJob?.Compute ?? new ComputeConfiguration()
                {
                    Target = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeCompute, Guid.NewGuid().ToString()),
                    InstanceCount = 1
                },
                Objective = copySweepJob?.Objective ?? new Objective()
                {
                    Goal = Goal.Maximize,
                    PrimaryMetric = "primary-metric-name"
                },
                SearchSpace = new Dictionary<string, string>(),
                Limits = new InternalSharedServices.Contracts.Jobs.SweepJobLimits(TimeSpan.FromSeconds(199), 10, 10),
                SamplingAlgorithmType = SamplingAlgorithmType.Random,
                EarlyTermination = new TruncationSelectionPolicy()
                {
                    EvaluationInterval = 10,
                    DelayEvaluation = 10,
                    PolicyType = EarlyTerminationPolicyType.TruncationSelection,
                    TruncationPercentage = 50
                },
                Trial = copySweepJob?.Trial ?? new TrialComponent()
                {
                    EnvironmentId = MfeResourceArmScope.ToString(workspace, MfeConstants.ArmTypeEnvironmentContainer, environmentNameVersion),
                    CodeId = MfeResourceVersionArmScope.ToString(workspace, MfeConstants.ArmTypeCodeContainer, Guid.NewGuid().ToString(), "1"),
                    Command = "python Data\train_iris.py"
                },
                Identity = copySweepJob?.Identity ?? new AmlToken(),
                Tags = copySweepJob?.Tags ?? new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
                Properties = copySweepJob?.Properties ?? new Dictionary<string, string>() { { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() } },
            };

            return (jobId, sweepJob);
        }
    }
}
