﻿// <copyright file="FakeOnlineDeploymentSetup.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using FakeItEasy;
using Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.ControllerInterfaces;
using Microsoft.MachineLearning.InferenceDeployment.WebApi.Contracts.V20211001.Deployment;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints;
using Microsoft.MachineLearning.ModelManagement.Contracts.Common;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.Common;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.ServiceInvokerInterfaces.ApiHost;
using Microsoft.MachineLearning.ModelManagement.Contracts.Mms.WireContracts;
using Newtonsoft.Json;
using MmsEnvironmentReference = Microsoft.MachineLearning.ModelManagement.Contracts.Mms.WireContracts.EnvironmentReference;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class FakeOnlineDeploymentSetup
    {
        private static List<ServiceResponseBase> serviceResponses = new List<ServiceResponseBase>();

        public static void ConfigureFakes(IServiceController serviceController, IEndpointController idpEndpointController, IDeploymentController idpDeploymentController)
        {
            serviceResponses = new List<ServiceResponseBase>();

            A.CallTo(() => serviceController.CreateServicePutAsync(A<Guid>._, A<string>._, A<string>._, A<CreateServiceRequest>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var subscriptionId = call.GetArgument<Guid>("subscriptionId");
                    var resourceGroup = call.GetArgument<string>("resourceGroup");
                    var workspace = call.GetArgument<string>("workspace");
                    var request = call.GetArgument<CreateServiceRequest>("request");
                    var operationId = Guid.NewGuid().ToString();
                    var serviceResponse = ConvertToServiceResponse(subscriptionId, resourceGroup, workspace, request, operationId);

                    serviceResponses.Add(serviceResponse);

                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                }).Once()
                .Then
                .ReturnsLazily(call =>
                {
                    var subscriptionId = call.GetArgument<Guid>("subscriptionId");
                    var resourceGroup = call.GetArgument<string>("resourceGroup");
                    var workspace = call.GetArgument<string>("workspace");
                    var request = call.GetArgument<CreateServiceRequest>("request");
                    var operationId = Guid.NewGuid().ToString();
                    var serviceResponse = ConvertToServiceResponse(subscriptionId, resourceGroup, workspace, request, operationId);

                    // Keep non-mutable properties the same
                    var oldResponse = serviceResponses.FirstOrDefault(inner => inner.Name != serviceResponse.Name);
                    if (oldResponse != null)
                    {
                        serviceResponse.Properties = oldResponse.Properties;
                    }

                    // Remove-then-add serves as an "update"
                    serviceResponses = serviceResponses.Where(inner => inner.Name != serviceResponse.Name).ToList();
                    serviceResponses.Add(serviceResponse);

                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.OK, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                });

            A.CallTo(() => idpDeploymentController.CreateDeploymentV2Async(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<CreateDeploymentRequestV2>._, A<bool>._))
                .ReturnsLazily(async call =>
                {
                    var subscriptionId = call.GetArgument<Guid>("subscriptionId");
                    var resourceGroup = call.GetArgument<string>("resourceGroupName");
                    var workspace = call.GetArgument<string>("workspaceName");
                    var endpointName = call.GetArgument<string>("endpointName");
                    var deploymentName = call.GetArgument<string>("deploymentName");
                    var request = call.GetArgument<CreateDeploymentRequestV2>("request");
                    var operationId = Guid.NewGuid().ToString();

                    var computeType = await GetComputeType(idpEndpointController, subscriptionId, resourceGroup, workspace, endpointName);

                    var serviceResponse = ConvertToServiceResponse(subscriptionId, resourceGroup, workspace, endpointName, deploymentName, request, operationId, computeType);
                    var strResponse = JsonConvert.SerializeObject(ConvertToResponseBase(CopyServiceBase(serviceResponse)));

                    serviceResponses.Add(serviceResponse);

                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }, strResponse);
                }).Once()
                .Then
                .ReturnsLazily(async call =>
                {
                    var subscriptionId = call.GetArgument<Guid>("subscriptionId");
                    var resourceGroup = call.GetArgument<string>("resourceGroupName");
                    var workspace = call.GetArgument<string>("workspaceName");
                    var endpointName = call.GetArgument<string>("endpointName");
                    var deploymentName = call.GetArgument<string>("deploymentName");
                    var request = call.GetArgument<CreateDeploymentRequestV2>("request");
                    var operationId = Guid.NewGuid().ToString();

                    var computeType = await GetComputeType(idpEndpointController, subscriptionId, resourceGroup, workspace, endpointName);

                    var serviceResponse = ConvertToServiceResponse(subscriptionId, resourceGroup, workspace, endpointName, deploymentName, request, operationId, computeType);
                    var strResponse = JsonConvert.SerializeObject(ConvertToResponseBase(CopyServiceBase(serviceResponse)));

                    // Keep non-mutable properties the same
                    var oldResponse = serviceResponses.FirstOrDefault(inner => inner.Name != serviceResponse.Name);
                    if (oldResponse != null)
                    {
                        serviceResponse.Properties = oldResponse.Properties;
                    }

                    // Remove-then-add serves as an "update"
                    serviceResponses = serviceResponses.Where(inner => inner.Name != serviceResponse.Name).ToList();
                    serviceResponses.Add(serviceResponse);

                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return GenerateResponseMessage(HttpStatusCode.OK, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }, strResponse);
                });

            A.CallTo(() => idpDeploymentController.UpdateDeploymentV2Async(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._, A<UpdateDeploymentRequestV2>._, A<bool>._))
                .ReturnsLazily(async call =>
                {
                    var subscriptionId = call.GetArgument<Guid>("subscriptionId");
                    var resourceGroup = call.GetArgument<string>("resourceGroupName");
                    var workspace = call.GetArgument<string>("workspaceName");
                    var endpointName = call.GetArgument<string>("endpointName");
                    var deploymentName = call.GetArgument<string>("deploymentName");
                    var request = call.GetArgument<UpdateDeploymentRequestV2>("request");
                    var operationId = Guid.NewGuid().ToString();

                    var computeType = await GetComputeType(idpEndpointController, subscriptionId, resourceGroup, workspace, endpointName);

                    var serviceResponse = ConvertToServiceResponse(subscriptionId, resourceGroup, workspace, endpointName, deploymentName, request, operationId, computeType);
                    var strResponse = JsonConvert.SerializeObject(ConvertToResponseBase(CopyServiceBase(serviceResponse)));

                    // Keep non-mutable properties the same
                    var oldResponse = serviceResponses.FirstOrDefault(inner => inner.Name != serviceResponse.Name);
                    if (oldResponse != null)
                    {
                        serviceResponse.Properties = oldResponse.Properties;
                    }

                    // Remove-then-add serves as an "update"
                    serviceResponses = serviceResponses.Where(inner => inner.Name != serviceResponse.Name).ToList();
                    serviceResponses.Add(serviceResponse);

                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }, strResponse);
                });

            A.CallTo(() => serviceController.GetServiceAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<bool>._, A<bool>._))
                .ReturnsLazily(call =>
                {
                    var endpointDeploymentName = call.GetArgument<string>("id");

                    var result = serviceResponses.Where(serviceResponse => serviceResponse.Name == endpointDeploymentName).SingleOrDefault();

                    if (endpointDeploymentName.Contains("504GatewayTimeoutTest", StringComparison.InvariantCultureIgnoreCase))
                    {
                        throw ServiceInvocationExceptionUtil.CreateRaw(HttpStatusCode.GatewayTimeout, MfeTestingConstants.ResponseBody504);
                    }

                    if (result == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "NoSuchService", "Test error message");
                    }

                    return Task.FromResult(CopyServiceBase(result));
                });

            A.CallTo(() => idpDeploymentController.GetDeploymentAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var endpointName = call.GetArgument<string>("endpointName");
                    var deploymentName = call.GetArgument<string>("deploymentName");
                    var endpointDeploymentName = $"{deploymentName}-{endpointName}";

                    var result = serviceResponses.Where(serviceResponse => serviceResponse.Name == endpointDeploymentName).SingleOrDefault();

                    if (endpointDeploymentName.Contains("504GatewayTimeoutTest", StringComparison.InvariantCultureIgnoreCase))
                    {
                        throw ServiceInvocationExceptionUtil.CreateRaw(HttpStatusCode.GatewayTimeout, MfeTestingConstants.ResponseBody504);
                    }

                    if (result == null)
                    {
                        throw ServiceInvocationExceptionUtil.Create(HttpStatusCode.NotFound, "NoSuchService", "Test error message");
                    }

                    return Task.FromResult(ConvertToResponseBase(CopyServiceBase(result)));
                });

            A.CallTo(() => serviceController.GetServicesAsync(A<Guid>._, A<string>._, A<string>._, A<ListServicesRequest>._))
                .ReturnsLazily(call =>
                {
                    var request = call.GetArgument<ListServicesRequest>("request");

                    var filteredResult = serviceResponses
                        .Where(serviceResponse => request.Properties == null || serviceResponse.Properties?[request.Properties.Split("=")[0]] == request.Properties.Split("=")[1])
                        .Select(result => CopyServiceBase(result))
                        .ToList();

                    return Task.FromResult(new PagedResponse<ServiceResponseBase, ListServicesRequest>()
                    {
                        Value = filteredResult,
                        ContinuationToken = "dummy-token"
                    });
                });

            A.CallTo(() => idpDeploymentController.ListDeploymentAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var endpointName = call.GetArgument<string>("endpointName");

                    return Task.FromResult(serviceResponses
                        .Where(serviceResponse => serviceResponse.EndpointName == endpointName)
                        .Select(serviceResponse => ConvertToResponseBase(CopyServiceBase(serviceResponse)))
                        .ToList());
                });

            A.CallTo(() => serviceController.DeleteServiceAsync(A<Guid>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var endpointDeploymentName = call.GetArgument<string>("id");

                    var updatedList = serviceResponses.Where(serviceResponse => serviceResponse.Name != endpointDeploymentName).ToList();
                    var noContent = updatedList.Count == serviceResponses.Count;
                    serviceResponses = updatedList;

                    if (noContent)
                    {
                        return Task.FromResult(GenerateResponseMessage(HttpStatusCode.NoContent));
                    }

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                });

            A.CallTo(() => idpDeploymentController.DeleteDeploymentAsync(A<Guid>._, A<string>._, A<string>._, A<string>._, A<string>._))
                .ReturnsLazily(call =>
                {
                    var endpointName = call.GetArgument<string>("endpointName");
                    var deploymentName = call.GetArgument<string>("deploymentName");
                    var endpointDeploymentName = $"{deploymentName}-{endpointName}";

                    var updatedList = serviceResponses.Where(serviceResponse => serviceResponse.Name != endpointDeploymentName).ToList();
                    var noContent = updatedList.Count == serviceResponses.Count;
                    serviceResponses = updatedList;

                    if (noContent)
                    {
                        return Task.FromResult(GenerateResponseMessage(HttpStatusCode.NoContent));
                    }

                    var operationId = Guid.NewGuid().ToString();
                    FakeOperationsSetup.AddToOperationResponses(operationId, new AsyncOperationStatus() { State = AsyncOperationState.Succeeded });
                    return Task.FromResult(GenerateResponseMessage(HttpStatusCode.Accepted, new Dictionary<string, IEnumerable<string>>()
                    {
                        { MfeConstants.MmsOperationLocationHeader, new List<string>() { operationId } }
                    }));
                });
        }

        public static void DeleteServices(string endpointName)
        {
            serviceResponses = serviceResponses.Where(serviceResponse => serviceResponse.Name.Split("-")[1] != endpointName).ToList();
        }

        private async static Task<EndpointComputeType> GetComputeType(IEndpointController idpEndpointController, Guid subscriptionId, string resourceGroupName, string workspaceName, string endpointName)
        {
            var endpoint = await idpEndpointController.GetEndpointAsync(subscriptionId, resourceGroupName, workspaceName, endpointName);
            return endpoint?.ComputeTarget?.ComputeTargetName == null
                ? EndpointComputeType.Managed
                : EndpointComputeType.K8S;
        }

        private static ServiceResponseBase ConvertToServiceResponse(Guid subscriptionId, string resourceGroupName, string workspaceName, string endpointName, string deploymentName, CreateDeploymentRequestV2 request, string operationId, EndpointComputeType computeType, WebServiceState state = WebServiceState.Healthy)
        {
            CreateServiceRequest mmsRequest;
            if (computeType == EndpointComputeType.Managed)
            {
                mmsRequest = new ManagedInferenceCreateServiceRequest
                {
                    ComputeType = ComputeEnvironmentType.MIRGA,
                    EndpointName = endpointName,
                    AutoScaler = new AutoScaler
                    {
                        AutoScaleEnabled = request.ScaleSettings?.ScaleType == ScaleType.Auto,
                        MinReplicas = request.ScaleSettings?.MinimumInstanceCount,
                        MaxReplicas = request.ScaleSettings?.MaximumInstanceCount,
                    },
                    Sku = request.VmSize,
                    NumReplicas = request.ScaleSettings?.InstanceCount,
                    EnvironmentVariables = request.EnvironmentVariables?.ToDictionary(kv => kv.Key, kv => kv.Value),
                };
            }
            else // k8s
            {
                mmsRequest = new AKSServiceCreateRequest
                {
                    ComputeType = ComputeEnvironmentType.AKS,
                    EndpointName = endpointName,
                    AutoScaler = new AutoScaler
                    {
                        AutoScaleEnabled = request.ScaleSettings?.ScaleType == ScaleType.Auto,
                        MinReplicas = request.ScaleSettings?.MinimumInstanceCount,
                        MaxReplicas = request.ScaleSettings?.MaximumInstanceCount,
                    },
                    NumReplicas = request.ScaleSettings?.InstanceCount,
                    EnvironmentVariables = request.EnvironmentVariables?.ToDictionary(kv => kv.Key, kv => kv.Value),
                };
            }
            mmsRequest.Name = $"{deploymentName}-{endpointName}";
            mmsRequest.Description = request.Description;
            mmsRequest.Location = request.Location;
            mmsRequest.EnvironmentImageRequest = new EnvironmentImageRequest
            {
                Assets = request.Assets?.Select(url => new ImageAsset { Url = new Uri(url) }).ToList(),
                DriverProgram = request.DriverProgram,
                ModelIds = request.ModelIds,
                Environment = new EnvironmentManagement.Contracts.EnvironmentDefinition
                {
                    Name = request.EnvironmentReference?.Name,
                    Version = request.EnvironmentReference?.Version,
                },
                EnvironmentReference = new MmsEnvironmentReference(request.EnvironmentReference?.Name, request.EnvironmentReference?.Version),
            };
            mmsRequest.Properties = request.Properties?.ToDictionary(kv => kv.Key, kv => kv.Value);
            mmsRequest.KvTags = request.Tags?.ToDictionary(kv => kv.Key, kv => kv.Value);

            return ConvertToServiceResponse(subscriptionId, resourceGroupName, workspaceName, mmsRequest, operationId, state);
        }

        private static ServiceResponseBase ConvertToServiceResponse(Guid subscriptionId, string resourceGroupName, string workspaceName, string endpointName, string deploymentName, UpdateDeploymentRequestV2 request, string operationId, EndpointComputeType computeType, WebServiceState state = WebServiceState.Healthy)
        {
            CreateServiceRequest mmsRequest;
            if (computeType == EndpointComputeType.Managed)
            {
                mmsRequest = new ManagedInferenceCreateServiceRequest
                {
                    ComputeType = ComputeEnvironmentType.MIRGA,
                    EndpointName = endpointName,
                    AutoScaler = new AutoScaler
                    {
                        AutoScaleEnabled = request.ScaleSettings?.ScaleType == ScaleType.Auto,
                        MinReplicas = request.ScaleSettings?.MinimumInstanceCount,
                        MaxReplicas = request.ScaleSettings?.MaximumInstanceCount,
                    },
                    NumReplicas = request.ScaleSettings?.InstanceCount,
                    EnvironmentVariables = request.EnvironmentVariables?.ToDictionary(kv => kv.Key, kv => kv.Value),
                };
            }
            else // k8s
            {
                mmsRequest = new AKSServiceCreateRequest
                {
                    ComputeType = ComputeEnvironmentType.AKS,
                    EndpointName = endpointName,
                    AutoScaler = new AutoScaler
                    {
                        AutoScaleEnabled = request.ScaleSettings?.ScaleType == ScaleType.Auto,
                        MinReplicas = request.ScaleSettings?.MinimumInstanceCount,
                        MaxReplicas = request.ScaleSettings?.MaximumInstanceCount,
                    },
                    NumReplicas = request.ScaleSettings?.InstanceCount,
                    EnvironmentVariables = request.EnvironmentVariables?.ToDictionary(kv => kv.Key, kv => kv.Value),
                };
            }
            mmsRequest.Name = $"{deploymentName}-{endpointName}";
            mmsRequest.Description = request.Description;
            mmsRequest.EnvironmentImageRequest = new EnvironmentImageRequest
            {
                Assets = request.Assets?.Select(url => new ImageAsset { Url = new Uri(url) }).ToList(),
                DriverProgram = request.DriverProgram,
                ModelIds = request.ModelIds,
                Environment = new EnvironmentManagement.Contracts.EnvironmentDefinition
                {
                    Name = request.EnvironmentReference?.Name,
                    Version = request.EnvironmentReference?.Version,
                },
                EnvironmentReference = new MmsEnvironmentReference(request.EnvironmentReference?.Name, request.EnvironmentReference?.Version),
            };
            mmsRequest.KvTags = request.Tags?.ToDictionary(kv => kv.Key, kv => kv.Value);

            return ConvertToServiceResponse(subscriptionId, resourceGroupName, workspaceName, mmsRequest, operationId, state);
        }

        private static ServiceResponseBase ConvertToServiceResponse(Guid subscriptionId, string resourceGroupName, string workspaceName, CreateServiceRequest request, string operationId, WebServiceState state = WebServiceState.Healthy)
        {
            ServiceResponseBase response = null;
            var endpointName = request.Name.Split("-")[1];

            request.KvTags ??= new Dictionary<string, string>();
            request.Properties ??= new Dictionary<string, string>();
            // MfeConstants.AzureMLCodeArtifactProperty
            // MfeConstants.AzureMLOnlineEndpointProperty

            switch (request.ComputeType)
            {
                case ComputeEnvironmentType.MIRAMLCOMPUTE:
                case ComputeEnvironmentType.MIRGA:
                    var mirRequest = (ManagedInferenceCreateServiceRequest)request;
                    response = new ManagedInferenceServiceResponse()
                    {
                        // Unique props
                        EndpointName = mirRequest.EndpointName,
                        AutoScaler = mirRequest.AutoScaler,
                        NumReplicas = mirRequest.NumReplicas,
                        Sku = mirRequest.Sku,
                        OSType = mirRequest.OSType,
                        ApplicationInsightsEnabled = mirRequest.ApplicationInsightsEnabled,
                        MaxConcurrentRequestsPerContainer = mirRequest.MaxConcurrentRequestsPerContainer,
                        ScoringTimeoutMs = mirRequest.ScoringTimeoutMs,
                        LivenessProbeRequirements = mirRequest.LivenessProbeRequirements,
                        ReadinessProbeRequirements = mirRequest.ReadinessProbeRequirements,
                        ContainerResourceRequirements = mirRequest.ContainerResourceRequirements,
                        EnvironmentImageRequest = new EnvironmentImageRequest()
                        {
                            Assets = mirRequest.EnvironmentImageRequest?.Assets,
                            DriverProgram = mirRequest.EnvironmentImageRequest?.DriverProgram,
                            ModelIds = mirRequest.EnvironmentImageRequest?.ModelIds,
                            Models = mirRequest.EnvironmentImageRequest?.Models,
                            Environment = new EnvironmentManagement.Contracts.EnvironmentDefinition()
                            {
                                Name = mirRequest.EnvironmentImageRequest?.EnvironmentReference?.Name,
                                Version = mirRequest.EnvironmentImageRequest?.EnvironmentReference?.Version,
                            }
                        },
                        EnvironmentVariables = mirRequest.EnvironmentVariables ?? new Dictionary<string, string>()
                    };
                    break;
                case ComputeEnvironmentType.AKS:
                    var aksRequest = (AKSServiceCreateRequest)request;
                    response = new AKSServiceResponse()
                    {
                        // Unique props
                        EndpointName = aksRequest.EndpointName,
                        AutoScaler = aksRequest.AutoScaler,
                        NumReplicas = aksRequest.NumReplicas,
                        AppInsightsEnabled = aksRequest.AppInsightsEnabled.GetValueOrDefault(),
                        MaxConcurrentRequestsPerContainer = aksRequest.MaxConcurrentRequestsPerContainer,
                        ScoringTimeoutMs = aksRequest.ScoringTimeoutMs,
                        LivenessProbeRequirements = aksRequest.LivenessProbeRequirements,
                        ContainerResourceRequirements = aksRequest.ContainerResourceRequirements,
                        EnvironmentImageRequest = new EnvironmentImageRequest()
                        {
                            Assets = aksRequest.EnvironmentImageRequest?.Assets,
                            DriverProgram = aksRequest.EnvironmentImageRequest?.DriverProgram,
                            ModelIds = aksRequest.EnvironmentImageRequest?.ModelIds,
                            Models = aksRequest.EnvironmentImageRequest?.Models,
                            Environment = new EnvironmentManagement.Contracts.EnvironmentDefinition()
                            {
                                Name = aksRequest.EnvironmentImageRequest?.EnvironmentReference?.Name,
                                Version = aksRequest.EnvironmentImageRequest?.EnvironmentReference?.Version,
                            }
                        },
                        AadAuthEnabled = aksRequest.AadAuthEnabled.GetValueOrDefault(),
                        AuthEnabled = aksRequest.AuthEnabled.GetValueOrDefault(),
                        ComputeName = aksRequest.ComputeName,
                        EnvironmentVariables = aksRequest.EnvironmentVariables ?? new Dictionary<string, string>()
                    };
                    break;

                // default: should throw an appropriate error, but I don't know what that error is...
            }

            if (response != null)
            {
                // Shared props
                response.Name = request.Name;
                response.Description = request.Description;
                response.State = state;
                response.Tags = request.Tags; // Not too sure what the conceptual difference is between this and KvTags...
                response.KvTags = request.KvTags;
                response.Properties = request.Properties;
                response.CreatedTime = DateTime.UtcNow;
                response.UpdatedTime = DateTime.UtcNow;
                response.OperationId = operationId;
                response.ComputeType = request.ComputeType;
                response.Id = $"/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.MachineLearningServices/workspaces/{workspaceName}/onlineEndpoints/{endpointName}";
            }

            return response;
        }

        private static ServiceResponseBase CopyServiceBase(ServiceResponseBase response)
        {
            ServiceResponseBase copy = null;

            switch (response.ComputeType)
            {
                case ComputeEnvironmentType.MIRAMLCOMPUTE:
                case ComputeEnvironmentType.MIRGA:
                    var mirResponse = (ManagedInferenceServiceResponse)response;
                    copy = new ManagedInferenceServiceResponse()
                    {
                        // Unique props
                        EndpointName = mirResponse.EndpointName,
                        AutoScaler = mirResponse.AutoScaler,
                        NumReplicas = mirResponse.NumReplicas,
                        Sku = mirResponse.Sku,
                        OSType = mirResponse.OSType,
                        ApplicationInsightsEnabled = mirResponse.ApplicationInsightsEnabled,
                        MaxConcurrentRequestsPerContainer = mirResponse.MaxConcurrentRequestsPerContainer,
                        ScoringTimeoutMs = mirResponse.ScoringTimeoutMs,
                        LivenessProbeRequirements = mirResponse.LivenessProbeRequirements,
                        ReadinessProbeRequirements = mirResponse.ReadinessProbeRequirements,
                        ContainerResourceRequirements = mirResponse.ContainerResourceRequirements,
                        EnvironmentImageRequest = mirResponse.EnvironmentImageRequest,
                        EnvironmentVariables = mirResponse.EnvironmentVariables
                    };
                    break;
                case ComputeEnvironmentType.AKS:
                    var aksResponse = (AKSServiceResponse)response;
                    copy = new AKSServiceResponse()
                    {
                        // Unique props
                        EndpointName = aksResponse.EndpointName,
                        AutoScaler = aksResponse.AutoScaler,
                        NumReplicas = aksResponse.NumReplicas,
                        AppInsightsEnabled = aksResponse.AppInsightsEnabled,
                        MaxConcurrentRequestsPerContainer = aksResponse.MaxConcurrentRequestsPerContainer,
                        ScoringTimeoutMs = aksResponse.ScoringTimeoutMs,
                        LivenessProbeRequirements = aksResponse.LivenessProbeRequirements,
                        ContainerResourceRequirements = aksResponse.ContainerResourceRequirements,
                        EnvironmentImageRequest = aksResponse.EnvironmentImageRequest,
                        AadAuthEnabled = aksResponse.AadAuthEnabled,
                        AuthEnabled = aksResponse.AuthEnabled,
                        ComputeName = aksResponse.ComputeName,
                        EnvironmentVariables = aksResponse.EnvironmentVariables
                    };
                    break;
            }

            if (response != null)
            {
                // Shared props
                copy.Name = response.Name;
                copy.Description = response.Description;
                copy.State = response.State;
                copy.Tags = response.Tags != null ? new List<string>(response.Tags) : null;
                copy.KvTags = response.KvTags != null ? new Dictionary<string, string>(response.KvTags) : null;
                copy.Properties = response.Properties != null ? new Dictionary<string, string>(response.Properties) : null;
                copy.CreatedTime = response.CreatedTime;
                copy.UpdatedTime = response.UpdatedTime;
                copy.OperationId = response.OperationId;
                copy.ComputeType = response.ComputeType;
                copy.Id = response.Id;
            }

            return copy;
        }

        // FakeOnlineEndpoint also uses this same method, move to helper if necessary
        private static HttpResponseMessage GenerateResponseMessage(HttpStatusCode statusCode, Dictionary<string, IEnumerable<string>> headerValues = null, string content = null)
        {
            content ??= string.Empty;

            var message = new HttpResponseMessage(statusCode)
            {
                Content = new StringContent(content)
            };

            foreach (var entry in headerValues.EmptyIfNull())
            {
                message.Headers.Add(entry.Key, entry.Value);
            }

            return message;
        }

        private static DeploymentResponse ConvertToResponseBase(ServiceResponseBase serviceResponse)
        {
            var response = new DeploymentResponse
            {
                Tags = serviceResponse.KvTags,
                EndpointName = serviceResponse.Name.Split("-")[1],
                DeploymentName = serviceResponse.Name.Split("-")[0],
                Description = serviceResponse.Description,
                Properties = serviceResponse.Properties,
                CreatedTime = serviceResponse.CreatedTime,
                CreatedBy = serviceResponse.CreatedBy,
                UpdatedTime = serviceResponse.UpdatedTime,
                ScaleSettings = new ScaleSettings(),
            };

            switch (serviceResponse.ComputeType)
            {
                case ComputeEnvironmentType.MIRAMLCOMPUTE:
                case ComputeEnvironmentType.MIRGA:
                    var mirResponse = (ManagedInferenceServiceResponse)serviceResponse;
                    response.ApplicationInsightsEnabled = mirResponse.ApplicationInsightsEnabled ?? false;
                    response.ScaleSettings.InstanceCount = mirResponse.NumReplicas;
                    response.ScaleSettings.MinimumInstanceCount = mirResponse.AutoScaler?.MinReplicas;
                    response.ScaleSettings.MaximumInstanceCount = mirResponse.AutoScaler?.MaxReplicas;
                    response.ScaleSettings.ScaleType = mirResponse.AutoScaler?.AutoScaleEnabled ?? false ? ScaleType.Auto : ScaleType.Manual;
                    response.VmSize = mirResponse.Sku;
                    response.OsType = mirResponse.OSType.ToString();
                    break;
                case ComputeEnvironmentType.AKS:
                    var aksResponse = (AKSServiceResponse)serviceResponse;
                    response.ApplicationInsightsEnabled = aksResponse.AppInsightsEnabled;
                    response.ScaleSettings.InstanceCount = aksResponse.NumReplicas;
                    response.ScaleSettings.MinimumInstanceCount = aksResponse.AutoScaler?.MinReplicas;
                    response.ScaleSettings.MaximumInstanceCount = aksResponse.AutoScaler?.MaxReplicas;
                    response.ScaleSettings.TargetUtilizationPercentage = aksResponse.AutoScaler?.TargetUtilizationPercentage;
                    response.ScaleSettings.ScaleType = aksResponse.AutoScaler?.AutoScaleEnabled ?? false ? ScaleType.Auto : ScaleType.Manual;
                    response.VmSize = aksResponse.InstanceType;
                    break;
            }

            return response;
        }
    }
}
