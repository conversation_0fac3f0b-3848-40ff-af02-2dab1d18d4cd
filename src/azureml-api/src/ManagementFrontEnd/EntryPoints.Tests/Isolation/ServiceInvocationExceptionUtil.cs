﻿// <copyright file="ServiceInvocationExceptionUtil.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Net;
using System.Net.Http;
using Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Core;
using Newtonsoft.Json;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Isolation
{
    public static class ServiceInvocationExceptionUtil
    {
        public static ServiceInvocationException Create(HttpStatusCode statusCode, string errorCode = "UserError", string errorMessage = "Error message")
        {
            var errorResponse = new ErrorResponse
            {
                Error = new RootError
                {
                    Code = errorCode,
                    Message = errorMessage,
                },
            };

            return new ServiceInvocationException(new HttpRequestMessage(), new HttpResponseMessage(statusCode), JsonConvert.SerializeObject(errorResponse), errorResponse);
        }

        public static ServiceInvocationException CreateResponseMessage(HttpStatusCode statusCode, string reasonPhrase, string errorCode = "UserError", string errorMessage = "Error message")
        {
            var errorResponse = new ErrorResponse
            {
                Error = new RootError
                {
                    Code = errorCode,
                    Message = errorMessage,
                },
            };

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = statusCode,
                ReasonPhrase = reasonPhrase
            };

            return new ServiceInvocationException(new HttpRequestMessage(), responseMessage, JsonConvert.SerializeObject(errorResponse), errorResponse);
        }

        public static ServiceInvocationException CreateRaw(HttpStatusCode statusCode, string errorMessage = "Error message")
        {
            return new ServiceInvocationException(new HttpRequestMessage(), new HttpResponseMessage(statusCode), errorMessage);
        }
    }
}
