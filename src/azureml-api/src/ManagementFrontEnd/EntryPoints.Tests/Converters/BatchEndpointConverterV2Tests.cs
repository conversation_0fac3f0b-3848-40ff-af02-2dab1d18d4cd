﻿// <copyright file="BatchEndpointConverterV2Tests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using AutoMapper;
using FakeItEasy;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Msi.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Endpoints.Batch.Contracts.ControlPlane.V1;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.BatchInference;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Xunit;
using EndpointProvisioningState = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.EndpointProvisioningState;
using ResourceIdentityInArm = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.ArmCommon.ResourceIdentityInArm;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters.Tests
{
    [MfeTest(TestCategory.Unit, ScenarioScope.BatchServices)]
    public class BatchEndpointConverterV2Tests
    {
        private const string _testApiVersion = "500 BC";

        private readonly IArmContextAccessor _armContextAccessor;
        private readonly IBatchEndpointConverterV2 _converter;
        private readonly DefaultHttpContext _context;
        private readonly IMapper _mapper;

        public BatchEndpointConverterV2Tests()
        {
            _context = new DefaultHttpContext();
            IHttpContextAccessor httpContextAccessor = A.Fake<IHttpContextAccessor>();
            A.CallTo(() => httpContextAccessor.HttpContext).Returns(_context);

            _armContextAccessor = A.Fake<IArmContextAccessor>();
            _mapper = A.Fake<IMapper>();
            _converter = new BatchEndpointConverterV2(_armContextAccessor, httpContextAccessor, _mapper);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void ConvertToCreateRequestSucceeds(bool useDefaults)
        {
            // Arrange
            SetApiVersion(_testApiVersion);
            TrackedResource<BatchEndpoint> endpoint = new ()
            {
                Location = "location",
                Properties = new BatchEndpoint
                {
                    AuthMode = EndpointAuthMode.AADToken,
                    Description = "description",
                    Properties = new Dictionary<string, string>(),
                },
                Tags = new CaseInsensitiveDictionary()
                {
                    { "tag1", "value1" },
                    { "TAG2", "value2" },
                },
            };

            if (useDefaults)
            {
                endpoint.Properties.Defaults = new BatchEndpointDefaults
                {
                    DeploymentName = "blue",
                };
            }
            else
            {
                endpoint.Properties.Traffic = new Dictionary<string, int>
                {
                    { "blue", 100 }
                };
            }

            CreateEndpointRequest expectedResult = new ()
            {
                AuthMode = AuthMode.AADToken,
                Defaults = new EndpointDefaults
                {
                    DeploymentName = "blue",
                },
                Description = "description",
                Identity = new ManagedIdentity { Type = "SystemAssigned" },
                Location = "location",
                Properties = new Dictionary<string, string>()
                {
                    { "BatchEndpointCreationApiVersion", _testApiVersion }
                },
                Tags = new Dictionary<string, string>()
                {
                    { "tag1", "value1" },
                    { "tag2", "value2" },
                }
            };

            ManagedIdentity identity = new ManagedIdentity { Type = "SystemAssigned" };

            // Act
            CreateEndpointRequest result = _converter.ConvertToCreateRequest(endpoint, identity);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void ConvertToCreateRequestFailsInvalidTraffic(bool useMultipleDeploymentTraffic)
        {
            // Arrange
            SetApiVersion(_testApiVersion);
            TrackedResource<BatchEndpoint> endpoint = new ()
            {
                Location = "location",
                Properties = new BatchEndpoint
                {
                    AuthMode = EndpointAuthMode.AADToken,
                    Description = "description",
                    Properties = new Dictionary<string, string>(),
                },
                Tags = new CaseInsensitiveDictionary()
                {
                    { "tag1", "value1" },
                    { "TAG2", "value2" },
                },
            };

            endpoint.Properties.Traffic = useMultipleDeploymentTraffic
                ? new Dictionary<string, int>
                  {
                      { "blue", 50 },
                      { "green", 50 }
                  }
                : new Dictionary<string, int>
                  {
                      { "blue", 70 }
                  };

            // Act
            Func<CreateEndpointRequest> act = () => _converter.ConvertToCreateRequest(endpoint);

            // Assert
            act.Should()
                .Throw<Exception>()
                .WithMessage($"The specified traffic rule for batch endpoint '{endpoint.Name}' is invalid. " +
                    "Please check that the traffic rule specifies exactly one deployment name with traffic weight set to 100. " +
                    "This will ensure the deployment is set as default for the endpoint.");
        }

        [Fact]
        public void ConvertToCreateRequestSucceedsMinimumPayload()
        {
            // Arrange
            SetApiVersion(_testApiVersion);
            TrackedResource<BatchEndpoint> endpoint = new ()
            {
                Location = "location",
                Properties = new BatchEndpoint
                {
                    AuthMode = EndpointAuthMode.AADToken,
                    Properties = new Dictionary<string, string>()
                },
                Tags = new CaseInsensitiveDictionary(),
            };

            CreateEndpointRequest expectedResult = new ()
            {
                AuthMode = AuthMode.AADToken,
                Identity = new ManagedIdentity { Type = "SystemAssigned" },
                Location = "location",
                Tags = new Dictionary<string, string>(),
                Properties = new Dictionary<string, string>()
                {
                    { "BatchEndpointCreationApiVersion", _testApiVersion }
                }
            };

            ManagedIdentity identity = new ManagedIdentity { Type = "SystemAssigned" };

            // Act
            CreateEndpointRequest result = _converter.ConvertToCreateRequest(endpoint, identity);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void ConvertToCreateRequestSucceedsOnMissingRequiredFields()
        {
            // Arrange
            SetApiVersion(_testApiVersion);
            TrackedResource<BatchEndpoint> endpoint = new ()
            {
                Location = "location",
                Properties = new BatchEndpoint(),
                Tags = new CaseInsensitiveDictionary(),
            };

            CreateEndpointRequest expectedResult = new ()
            {
                AuthMode = AuthMode.AADToken,
                Location = "location",
                Tags = new Dictionary<string, string>(),
                Properties = new Dictionary<string, string>()
                {
                    { "BatchEndpointCreationApiVersion", _testApiVersion }
                }
            };

            // Act
            CreateEndpointRequest result = _converter.ConvertToCreateRequest(endpoint);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void ConvertToResourceArmDtoSucceeds()
        {
            // Arrange
            string expectedOperationUri = "https://www.microsoft.com/steel-gray-dolphin";
            A.CallTo(() => _armContextAccessor.GetOperationsStatusUri(A<WorkspaceContext2>._, A<string>._, A<string>._))
                .Returns(new Uri(expectedOperationUri));

            DateTimeOffset createdTime = new DateTimeOffset(DateTime.Now);
            WorkspaceContext2 wsContext = new WorkspaceContext2(
                Guid.NewGuid(),
                "myworkspace",
                "myresourcegroup",
                Guid.NewGuid(),
                "mylocation",
                "mytenant",
                new Dictionary<string, string>());

            ManagedIdentity identityInResponse = new () { Type = "None" };
            EndpointResponse response = new EndpointResponse()
            {
                AuthMode = AuthMode.AADToken,
                Defaults = new EndpointDefaults
                {
                    DeploymentName = "blue",
                },
                Description = "description",
                Identity = identityInResponse,
                Location = "location",
                Properties = new Dictionary<string, string>()
                {
                    { "BatchEndpointCreationApiVersion", _testApiVersion },
                    { "AzureAsyncOperationUri", Guid.NewGuid().ToString() }
                },
                Tags = new Dictionary<string, string>()
                {
                    { "tag1", "value1" },
                    { "tag2", "value2" },
                },
                ScoringUri = new Uri("http://myuri.com"),
                ProvisioningState = Endpoints.Batch.Contracts.ControlPlane.V1.EndpointProvisioningState.Creating,
                Name = "myEndpointName",
                SystemData = new Endpoints.Batch.Contracts.SystemData
                {
                    CreatedBy = "a",
                    CreatedTime = createdTime,
                    LastModifiedTime = createdTime,
                }
            };

            ResourceIdentityInArm convertedIdentity = new () { Type = "None" };
            TrackedResource<BatchEndpoint> expectedResult = new ()
            {
                Location = "location",
                Properties = new BatchEndpoint
                {
                    AuthMode = EndpointAuthMode.AADToken,
                    Defaults = new BatchEndpointDefaults
                    {
                        DeploymentName = "blue",
                    },
                    Description = "description",
                    Properties = new Dictionary<string, string>()
                    {
                        { "BatchEndpointCreationApiVersion", _testApiVersion },
                        { "AzureAsyncOperationUri", expectedOperationUri }
                    },
                    ScoringUri = new Uri("http://myuri.com"),
                    ProvisioningState = EndpointProvisioningState.Creating,
                },
                Tags = new CaseInsensitiveDictionary()
                {
                    { "tag1", "value1" },
                    { "TAG2", "value2" },
                },
                Name = "myEndpointName",
                Id = MfeResourceArmScope.ToString(wsContext, MfeConstants.ArmTypeBatchEndpoint, response.Name),
                Identity = convertedIdentity,
                Type = MfeConstants.ArmFullTypeBatchEndpoint,
                SystemData = new SystemData
                {
                    CreatedBy = "a",
                    CreatedAt = createdTime,
                    LastModifiedAt = createdTime,
                },
            };

            A.CallTo(() => _mapper.Map<ResourceIdentityInArm>(identityInResponse))
                .Returns(convertedIdentity);

            // Act
            TrackedResource<BatchEndpoint> result = _converter.ConvertToResourceArmDto(wsContext, response, true);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void ConvertToUpdateRequestSucceeds(bool useDefaults)
        {
            // Arrange
            PartialTrackedResource<PartialBatchEndpoint> endpoint = new ()
            {
                Properties = new PartialBatchEndpoint(),
                Tags = new CaseInsensitiveDictionary()
                {
                    { "tag1", "value1" },
                    { "TAG2", "value2" },
                },
            };

            if (useDefaults)
            {
                endpoint.Properties.Defaults = new BatchEndpointDefaults
                {
                    DeploymentName = "blue",
                };
            }
            else
            {
                endpoint.Properties.Traffic = new Dictionary<string, int>
                {
                    { "blue", 100 }
                };
            }

            UpdateEndpointRequest expectedResult = new ()
            {
                Defaults = new EndpointDefaults
                {
                    DeploymentName = "blue",
                },
                Identity = new ManagedIdentity { Type = "None" },
                Tags = new Dictionary<string, string>()
                {
                    { "tag1", "value1" },
                    { "tag2", "value2" },
                }
            };

            ManagedIdentity identity = new ManagedIdentity { Type = "None" };

            // Act
            UpdateEndpointRequest result = _converter.ConvertToUpdateRequest("testendpoint", endpoint, identity);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public void ConvertToUpdateRequestSucceedsFailsInvalidTraffic(bool useMultipleDeploymentTraffic)
        {
            // Arrange
            string endpointName = "testendpoint";
            PartialTrackedResource<PartialBatchEndpoint> endpoint = new ()
            {
                Properties = new PartialBatchEndpoint(),
                Tags = new CaseInsensitiveDictionary()
                {
                    { "tag1", "value1" },
                    { "TAG2", "value2" },
                },
            };

            endpoint.Properties.Traffic = useMultipleDeploymentTraffic
                ? new Dictionary<string, int>
                  {
                      { "blue", 50 },
                      { "green", 50 }
                  }
                : new Dictionary<string, int>
                  {
                      { "blue", 70 }
                  };

            // Act
            Func<UpdateEndpointRequest> act = () => _converter.ConvertToUpdateRequest(endpointName, endpoint);

            // Assert
            act.Should()
                .Throw<Exception>()
                .WithMessage($"The specified traffic rule for batch endpoint '{endpointName}' is invalid. " +
                    "Please check that the traffic rule specifies exactly one deployment name with traffic weight set to 100. " +
                    "This will ensure the deployment is set as default for the endpoint.");
        }

        [Fact]
        public void ConvertToUpdateRequestOnlyUpdateTagsSucceeds()
        {
            // Arrange
            PartialTrackedResource<PartialBatchEndpoint> endpoint = new ()
            {
                Tags = new CaseInsensitiveDictionary()
                {
                    { "tag1", "value1" },
                    { "TAG2", "value2" },
                },
            };

            UpdateEndpointRequest expectedResult = new ()
            {
                Tags = new Dictionary<string, string>()
                {
                    { "tag1", "value1" },
                    { "tag2", "value2" },
                }
            };

            // Act
            UpdateEndpointRequest result = _converter.ConvertToUpdateRequest("testendpoint", endpoint);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData("Cancelled")]
        [InlineData("Failed")]
        [InlineData("NotStarted")]
        [InlineData("Running")]
        [InlineData("Succeeded")]
        [InlineData("Unknown")]
        public void TestConvertToEndpointProvisioningStateSuccees(string operationStatus)
        {
            EndpointProvisioningState result = _converter.ConvertToEndpointProvisioningState(operationStatus);
            result.Should().BeOneOf(
                EndpointProvisioningState.Canceled,
                EndpointProvisioningState.Failed,
                EndpointProvisioningState.Succeeded,
                EndpointProvisioningState.Updating);
        }

        [Fact]
        public void TestConvertToEndpointProvisioningStateInvalid()
        {
            Func<EndpointProvisioningState> act = () => _converter.ConvertToEndpointProvisioningState("invalid");

            act.Should()
                .Throw<Exception>()
                .WithMessage("Unexpected operation status of 'invalid'.");
        }

        private void SetApiVersion(string apiVersion)
        {
            Dictionary<string, StringValues> queryParameters = new ()
            {
                { "api-version", new StringValues(apiVersion) },
            };
            _context.Request.Query = new QueryCollection(queryParameters);
        }
    }
}