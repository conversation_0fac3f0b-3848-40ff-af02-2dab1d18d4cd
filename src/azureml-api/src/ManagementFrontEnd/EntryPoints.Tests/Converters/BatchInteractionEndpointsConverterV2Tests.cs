﻿// <copyright file="BatchInteractionEndpointsConverterV2Tests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Endpoints.Batch.Contracts.DataPlane.V1;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters;
using Xunit;
using MfeJobEndpoint = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.JobEndpoint;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Converters
{
    [MfeTest(TestCategory.Unit, ScenarioScope.BatchServices)]
    public class BatchInteractionEndpointsConverterV2Tests
    {
        private readonly IBatchInteractionEndpointsConverterV2 _target;

        public BatchInteractionEndpointsConverterV2Tests()
        {
            _target = new BatchInteractionEndpointsConverterV2();
        }

        [Fact]
        public void ConvertBesToMfeInteractionEndpointsSucceeds()
        {
            IDictionary<string, MfeJobEndpoint> result = _target.ToExternal(GetBesInteractionEndpoints());

            result.Should().BeEquivalentTo(GetMfeInteractionEndpoints());
        }

        private Dictionary<string, JobEndpoint> GetBesInteractionEndpoints()
             => new Dictionary<string, JobEndpoint>
             {
                {
                    "studio", new JobEndpoint
                    {
                        Endpoint = "https://microsoft.com"
                    }
                }
             };

        private Dictionary<string, MfeJobEndpoint> GetMfeInteractionEndpoints()
             => new Dictionary<string, MfeJobEndpoint>
             {
                {
                    "studio", new MfeJobEndpoint
                    {
                        Endpoint = "https://microsoft.com"
                    }
                }
             };
    }
}
