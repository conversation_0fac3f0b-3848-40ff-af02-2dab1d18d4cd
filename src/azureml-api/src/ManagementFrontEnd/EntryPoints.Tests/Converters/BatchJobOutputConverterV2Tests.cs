﻿// <copyright file="BatchJobOutputConverterV2Tests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Endpoints.Batch.Contracts.DataPlane.V1;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Assets;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters;
using Xunit;
using MfeJobOutput = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.JobOutput;
using MfeUriFolderJobOutput = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.UriFolderJobOutput;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Converters
{
    [MfeTest(TestCategory.Unit, ScenarioScope.BatchServices)]
    public class BatchJobOutputConverterV2Tests
    {
        private readonly IBatchJobOutputConverterV2 _target;

        public BatchJobOutputConverterV2Tests()
        {
            _target = new BatchJobOutputConverterV2();
        }

        [Fact]
        public void ConvertBesToMfeOutputDataSucceeds()
        {
            IDictionary<string, MfeJobOutput> result = _target.ToExternal(GetBesOutputData());

            result.Should().BeEquivalentTo(GetMfeOutputData());
        }

        [Fact]
        public void ConvertMfeToBesOutputDataSucceeds()
        {
            IDictionary<string, JobOutput> result = _target.ToInternal(GetMfeOutputData());

            result.Should().BeEquivalentTo(GetBesOutputData());
        }

        [Fact]
        public void ConvertMfeToBesOutputDatasetNull()
        {
            DataVersionOld input = null;
            Assert.Null(_target.ToInternal(input));
        }

        [Fact]
        public void ConvertBesToMfeOutputDatasetNull()
        {
            DatastorePath input = null;
            Assert.Null(_target.ToExternal(input));
        }

        [Fact]
        public void ConvertMfeToBesOutputDatasetSuccess()
        {
            DataVersionOld input = new DataVersionOld { DatastoreId = "ds1", Path = "path/to/success" };
            DatastorePath output = _target.ToInternal(input);

            Assert.Equal(input.DatastoreId, output.DatastoreId);
            Assert.Equal(input.Path, output.Path);
        }

        [Fact]
        public void ConvertBesToMfeOutputDatasetSuccess()
        {
            DatastorePath input = new DatastorePath { DatastoreId = "ds1", Path = "path/to/success" };
            DataVersionOld output = _target.ToExternal(input);

            Assert.Equal(input.DatastoreId, output.DatastoreId);
            Assert.Equal(input.Path, output.Path);
        }

        private IDictionary<string, JobOutput> GetBesOutputData()
            => new Dictionary<string, JobOutput>
            {
                {
                    "my-output", new UriFolderJobOutput
                    {
                        Uri = "https://microsoft.com",
                        Mode = OutputDeliveryMode.Upload
                    }
                }
            };

        private IDictionary<string, MfeJobOutput> GetMfeOutputData()
            => new Dictionary<string, MfeJobOutput>
            {
                {
                    "my-output", new MfeUriFolderJobOutput
                    {
                        Uri = "https://microsoft.com",
                        Mode = InternalSharedServices.Contracts.OutputDataDeliveryMode.Upload
                    }
                }
            };
    }
}
