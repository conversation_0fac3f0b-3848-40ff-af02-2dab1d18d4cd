﻿// <copyright file="BatchJobInputConverterV2Tests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Collections.Generic;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Endpoints.Batch.Contracts.DataPlane.V1;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters;
using Xunit;
using BesInferenceDataInputBase = Microsoft.MachineLearning.Endpoints.Batch.Contracts.DataPlane.V1.InferenceDataInputBase;
using MfeInferenceDataInputBase = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.InferenceDataInputBase;
using MfeJobInput = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.JobInput;
using MfeUriFolderJobInput = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.UriFolderJobInput;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Converters
{
    [MfeTest(TestCategory.Unit, ScenarioScope.BatchServices)]
    public class BatchJobInputConverterV2Tests
    {
        private readonly IBatchJobInputConverterV2 _target;

        public BatchJobInputConverterV2Tests()
        {
            _target = new BatchJobInputConverterV2();
        }

        [Fact]
        public void ConvertBesToMfeInputDataSucceeds()
        {
            IDictionary<string, MfeJobInput> result = _target.ToExternal(GetBesInputData());

            result.Should().BeEquivalentTo(GetMfeInputData());
        }

        [Fact]
        public void ConvertMfeToBesInputDatasetNull()
        {
            MfeInferenceDataInputBase input = null;
            Assert.Null(_target.ToInternal(input));
        }

        [Fact]
        public void ConvertBesToMfeInputDatasetNull()
        {
            BesInferenceDataInputBase input = null;
            Assert.Null(_target.ToExternal(input));
        }

        [Fact]
        public void ConvertMfeToBesInputDataSucceeds()
        {
            IDictionary<string, JobInput> result = _target.ToInternal(GetMfeInputData());

            result.Should().BeEquivalentTo(GetBesInputData());
        }

        private IDictionary<string, JobInput> GetBesInputData()
            => new Dictionary<string, JobInput>
            {
                {
                    "my-input", new UriFolderJobInput
                    {
                        Uri = "https://microsoft.com",
                        Mode = InputDeliveryMode.Download
                    }
                }
            };

        private IDictionary<string, MfeJobInput> GetMfeInputData()
            => new Dictionary<string, MfeJobInput>
            {
                {
                    "my-input", new MfeUriFolderJobInput
                    {
                        Uri = "https://microsoft.com",
                        Mode = InternalSharedServices.Contracts.InputDataDeliveryMode.Download
                    }
                }
            };
    }
}
