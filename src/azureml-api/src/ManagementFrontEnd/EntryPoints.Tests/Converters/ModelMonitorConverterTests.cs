﻿// <copyright file="ModelMonitorConverterTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using FakeItEasy;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Common.WebApi.Exceptions;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Helpers;
using Microsoft.MachineLearning.ModelMonitor.Contracts.V1;
using Microsoft.MachineLearning.ModelMonitor.Converters;
using Newtonsoft.Json.Linq;
using Xunit;
using InternalContracts = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using InternalContractsMonitors = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Schedules.Monitors;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.UnitTests
{
    [MfeTest(TestCategory.Unit, ScenarioScope.ModelMonitors)]
    public class ModelMonitorConverterTests
    {
        private const string _baselineUri = "baseline";
        private const string _preprocessingComponent = "my_preprocessing_component";
        private const string _signalName = "my_signal";
        private const string _targetColumn = "my_target";
        private const string _targetUri = "target";

        private readonly TimeSpan _lookbackPeriod = TimeSpan.FromDays(1);
        private readonly IModelMonitorConverter _converter;

        public static IEnumerable<object[]> InvalidPropertiesAndTaskType =
            new List<object[]>
            {
                new object[] {
                    InternalContractsMonitors.ModelTaskType.Regression,
                    null,
                    "Properties should not be null." },
                new object[] {
                    InternalContractsMonitors.ModelTaskType.Regression,
                    new Dictionary<string, string>(),
                    "Properties should not be null." },
                new object[] {
                    InternalContractsMonitors.ModelTaskType.Regression,
                    new Dictionary<string, string>()
                    {
                        { "testkye", "testvalue"}
                    },
                    $"Properties should contain {ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey}." },
                new object[] {
                    InternalContractsMonitors.ModelTaskType.Regression,
                    new Dictionary<string, string>()
                    {
                        { ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey, "testvalue"}
                    },
                    $"The value of the property {ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey} should be an json array but got testvalue." },
                 new object[] {
                     InternalContractsMonitors.ModelTaskType.Regression,
                     new Dictionary<string, string>()
                     {
                         { ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey, "[{\"modelType\":\"regression\",\"metric\":\"Error\",\"threshold\":{\"value\":0.1}},{\"modelType\":\"regression\",\"metric\":\"RootMeanSquaredError\",\"threshold\":{\"value\":0.1}}]"},
                     },
                     $"The item in the property {ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey} value should be in RegressionModelPerformanceMetricThreshold format." },
                 new object[] {
                     InternalContractsMonitors.ModelTaskType.Regression,
                     new Dictionary<string, string>()
                     {
                         { ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey, "[{\"modelType\":\"classification\",\"metric\":\"Error\",\"threshold\":{\"value\":0.1}},{\"modelType\":\"regression\",\"metric\":\"RootMeanSquaredError\",\"threshold\":{\"value\":0.1}}]"},
                     },
                     $"The item in the property {ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey} value should be in RegressionModelPerformanceMetricThreshold format." },
                 new object[] {
                     null,
                     new Dictionary<string, string>()
                     {
                         { ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey, "[{\"modelType\":\"classification\",\"metric\":\"Error\",\"threshold\":{\"value\":0.1}},{\"modelType\":\"regression\",\"metric\":\"RootMeanSquaredError\",\"threshold\":{\"value\":0.1}}]"},
                     },
                     "TaskType should not be null." },
                 new object[] {
                     InternalContractsMonitors.ModelTaskType.QuestionAnswering,
                     new Dictionary<string, string>()
                     {
                         { ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey, "[{\"modelType\":\"classification\",\"metric\":\"Error\",\"threshold\":{\"value\":0.1}},{\"modelType\":\"regression\",\"metric\":\"RootMeanSquaredError\",\"threshold\":{\"value\":0.1}}]"},
                     },
                     "TaskType should be either regression or classification." },
            };

        public ModelMonitorConverterTests()
        {
            _converter = new ModelMonitorConverter(A.Fake<ILoggerFactory>());
        }

        [Theory]
        [InlineData(true, true, true)]
        [InlineData(false, true, true)]
        [InlineData(false, false, true)]
        [InlineData(false, false, false)]
        public void TestConvertToCreateModelMonitorRequestForNotificationTypesSucceeds(bool isNull, bool isEmailNotificationSettingsNull, bool isEmailListNull)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    false,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            monitorSchedule.MonitorDefinition.AlertNotificationSettings = ModelMonitorTestDataGenerator.GetInternalMonitorNotificationSettings(isNull, isEmailNotificationSettingsNull, isEmailListNull);

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            if (!isEmailListNull)
            {
                Assert.IsType<EmailAlertNotificationSettings>(createModelMonitorRequest.AlertNotificationSetting);
                Assert.Equal(new List<string> { "email1", "email2" }, (createModelMonitorRequest.AlertNotificationSetting as EmailAlertNotificationSettings).EmailNotificationSettings.Emails);
            }
            else
            {
                Assert.Null(createModelMonitorRequest.AlertNotificationSetting);
            }
        }

        [Theory]
        [InlineData(AlertNotificationType.AzureMonitor, false)]
        [InlineData(AlertNotificationType.Email, true)]
        [InlineData(AlertNotificationType.Email, false)]
        public void TestConvertToCreateModelMonitorRequestForLegacyNotificationTypesSucceeds(
            AlertNotificationType alertNotificationType,
            bool populateRequiredPropertiesOnly)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly,
                    monitoringSignals,
                    MfeApiVersions.Apr2023Preview,
                    alertNotificationType: alertNotificationType),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            Assert.Equivalent(
                ModelMonitorTestDataGenerator.GetAlertNotificationSettings(alertNotificationType, populateRequiredPropertiesOnly, isLegacy: true),
                createModelMonitorRequest.AlertNotificationSetting);
        }

        [Theory]
        [InlineData(MonitoringSignalType.DataDrift, true)]
        [InlineData(MonitoringSignalType.DataDrift, false)]
        [InlineData(MonitoringSignalType.DataQuality, true)]
        [InlineData(MonitoringSignalType.DataQuality, false)]
        [InlineData(MonitoringSignalType.FeatureAttributionDrift, true)]
        [InlineData(MonitoringSignalType.FeatureAttributionDrift, false)]
        [InlineData(MonitoringSignalType.PredictionDrift, true)]
        [InlineData(MonitoringSignalType.PredictionDrift, false)]
        public void ConvertApr2023ToCreateModelMonitorRequestForSignalTypesSucceeds(
            MonitoringSignalType signalType,
            bool populateRequiredPropertiesOnly)
        {
            // Arrange
            string signalName = signalType.ToString();
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { signalName, signalType }
            };

            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly,
                    monitoringSignals,
                    MfeApiVersions.Apr2023Preview),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            AssertCreateModelMonitorRequestProperties(
                createModelMonitorRequest,
                populateRequiredPropertiesOnly,
                monitoringSignals,
                AlertNotificationType.Email,
                MfeApiVersions.Apr2023Preview.ToSerializedValue(),
                useAmlClusterCompute: false);
        }

        [Theory]
        [InlineData(MonitoringSignalType.DataDrift, true)]
        [InlineData(MonitoringSignalType.DataDrift, false)]
        [InlineData(MonitoringSignalType.DataQuality, true)]
        [InlineData(MonitoringSignalType.DataQuality, false)]
        [InlineData(MonitoringSignalType.FeatureAttributionDrift, true)]
        [InlineData(MonitoringSignalType.FeatureAttributionDrift, false)]
        [InlineData(MonitoringSignalType.PredictionDrift, true)]
        [InlineData(MonitoringSignalType.PredictionDrift, false)]
        [InlineData(MonitoringSignalType.Custom, true)]
        [InlineData(MonitoringSignalType.Custom, false)]
        [InlineData(MonitoringSignalType.ModelPerformance, true)]
        [InlineData(MonitoringSignalType.ModelPerformance, false)]
        [InlineData(MonitoringSignalType.GenerationSafety, true)]
        [InlineData(MonitoringSignalType.GenerationSafety, false)]
        [InlineData(MonitoringSignalType.GenerationTokenUsage, true)]
        [InlineData(MonitoringSignalType.GenerationTokenUsage, false)]
        public void ConvertAug2023ToCreateModelMonitorRequestForSignalTypesSucceeds(
            MonitoringSignalType signalType,
            bool populateRequiredPropertiesOnly)
        {
            // Arrange
            string signalName = signalType.ToString();
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { signalName, signalType }
            };

            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            if (signalType.Equals(MonitoringSignalType.ModelPerformance))
            {
                monitorSchedule.MonitorDefinition = GetValidModelPerformanceSignal(
                    populateRequiredPropertiesOnly,
                    monitorSchedule.MonitorDefinition);
            }
            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            AssertCreateModelMonitorRequestProperties(
                createModelMonitorRequest,
                populateRequiredPropertiesOnly,
                monitoringSignals,
                AlertNotificationType.Email,
                MfeApiVersions.Aug2023Preview.ToSerializedValue(),
                useAmlClusterCompute: false);
        }

        // Todo - all types of input data.

        [Fact]
        public void TestConvertToJobDefinitionSuccess()
        {
            // Arrange
            ModelMonitorJob monitorJob = GetModelMonitorJob();

            // Act
            InternalContracts.JobBase pipelineJob = _converter.ConvertToJobDefinition(monitorJob, A.Fake<AccountRP.Contracts.Resource>());

            // Assert
            Assert.NotNull(pipelineJob);
            Assert.True(pipelineJob is InternalContracts.PipelineJob);
            AssertJobEquality(monitorJob, pipelineJob as InternalContracts.PipelineJob);
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequestDataDriftLegacySucceeds()
        {
            // Arrange
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = CreateEmptyMonitorSchedule();
            monitorSchedule.MonitorDefinition.ApiVersion = MfeApiVersions.Apr2023Preview.ToSerializedValue();
            monitorSchedule.MonitorDefinition.Signals[_signalName] = CreateDataDriftLegacySignal();

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert

            var signalEntry = Assert.Single(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(_signalName, signalEntry.Key);

            DataDriftMonitoringSignal dataDriftSignal = signalEntry.Value as DataDriftMonitoringSignal;
            Assert.NotNull(dataDriftSignal);
            Assert.Null(dataDriftSignal.BaselineData);
            Assert.Null(dataDriftSignal.TargetData);

            Assert.NotNull(dataDriftSignal.ProductionData);
            var productionData = Assert.IsType<TrailingInputData>(dataDriftSignal.ProductionData);
            Assert.Equal(TimeSpan.Zero, productionData.WindowOffset);
            Assert.Equal(this._lookbackPeriod, productionData.WindowSize);
            Assert.DoesNotContain(ModelMonitorConverter.TargetColumnKey, productionData.Columns.Keys);
            Assert.Equal(_preprocessingComponent, productionData.PreprocessingComponentId);

            Assert.NotNull(dataDriftSignal.ReferenceData);
            var referenceData = Assert.IsType<FixedInputData>(dataDriftSignal.ReferenceData);
            Assert.Contains(ModelMonitorConverter.TargetColumnKey, referenceData.Columns.Keys);
            Assert.Equal(_targetColumn, referenceData.Columns[ModelMonitorConverter.TargetColumnKey]);
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequestDataDriftLegacyTrailingBaselineSucceeds()
        {
            // Arrange
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = CreateEmptyMonitorSchedule();
            monitorSchedule.MonitorDefinition.ApiVersion = MfeApiVersions.Apr2023Preview.ToSerializedValue();

            monitorSchedule.MonitorDefinition.Signals[_signalName] = CreateDataDriftLegacySignal();
            monitorSchedule.Tags = new Dictionary<string, string>
            {
                [$"{_signalName}.baselinedata.datarange.type"] = "Trailing",
                [$"{_signalName}.baselinedata.datarange.window_size"] = XmlConvert.ToString(this._lookbackPeriod),
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            var signalEntry = Assert.Single(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(_signalName, signalEntry.Key);

            DataDriftMonitoringSignal dataDriftSignal = signalEntry.Value as DataDriftMonitoringSignal;
            Assert.NotNull(dataDriftSignal);
            Assert.Null(dataDriftSignal.BaselineData);
            Assert.Null(dataDriftSignal.TargetData);

            Assert.NotNull(dataDriftSignal.ProductionData);
            var productionData = Assert.IsType<TrailingInputData>(dataDriftSignal.ProductionData);
            Assert.Equal(TimeSpan.Zero, productionData.WindowOffset);
            Assert.Equal(this._lookbackPeriod, productionData.WindowSize);
            Assert.Equal(_preprocessingComponent, productionData.PreprocessingComponentId);

            Assert.NotNull(dataDriftSignal.ReferenceData);
            var referenceData = Assert.IsType<TrailingInputData>(dataDriftSignal.ReferenceData);
            Assert.Equal(TimeSpan.Zero, referenceData.WindowOffset);
            Assert.Equal(this._lookbackPeriod, referenceData.WindowSize);
            Assert.Equal(_preprocessingComponent, referenceData.PreprocessingComponentId);
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequestDataDriftLegacyFixedBaselineSucceeds()
        {
            // Arrange
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = CreateEmptyMonitorSchedule();
            monitorSchedule.MonitorDefinition.ApiVersion = MfeApiVersions.Apr2023Preview.ToSerializedValue();

            DateTime windowStart = new DateTime(2023, 1, 1);
            DateTime windowEnd = new DateTime(2023, 2, 1);
            monitorSchedule.MonitorDefinition.Signals[_signalName] = CreateDataDriftLegacySignal();
            monitorSchedule.Tags = new Dictionary<string, string>
            {
                [$"{_signalName}.baselinedata.datarange.type"] = "Fixed",
                [$"{_signalName}.baselinedata.datarange.window_start_date"] = XmlConvert.ToString(windowStart, XmlDateTimeSerializationMode.Local),
                [$"{_signalName}.baselinedata.datarange.window_end_date"] = XmlConvert.ToString(windowEnd, XmlDateTimeSerializationMode.Local),
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            var signalEntry = Assert.Single(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(_signalName, signalEntry.Key);

            DataDriftMonitoringSignal dataDriftSignal = signalEntry.Value as DataDriftMonitoringSignal;
            Assert.NotNull(dataDriftSignal);
            Assert.Null(dataDriftSignal.BaselineData);
            Assert.Null(dataDriftSignal.TargetData);

            Assert.NotNull(dataDriftSignal.ProductionData);
            var productionData = Assert.IsType<TrailingInputData>(dataDriftSignal.ProductionData);
            Assert.Equal(TimeSpan.Zero, productionData.WindowOffset);
            Assert.Equal(this._lookbackPeriod, productionData.WindowSize);
            Assert.Equal(_preprocessingComponent, productionData.PreprocessingComponentId);

            Assert.NotNull(dataDriftSignal.ReferenceData);
            var referenceData = Assert.IsType<StaticInputData>(dataDriftSignal.ReferenceData);
            Assert.Equal(windowStart, referenceData.WindowStart);
            Assert.Equal(windowEnd, referenceData.WindowEnd);
            Assert.Equal(_preprocessingComponent, referenceData.PreprocessingComponentId);
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequestCustomSignalSucceeds()
        {
            // Arrange
            string expectedCustomMetricName = "myCustomMetric";
            float expectedCustomMetricThreshold = 10;
            string expectedInputName = "my_literal_input";
            string expectedInputDataName = "my_custom_input_data";
            TimeSpan expectedWindowOffset = TimeSpan.FromDays(1);
            TimeSpan expectedWindowSize = TimeSpan.FromDays(7);
            string expectedLiteralValue = "my_value";
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { _signalName, MonitoringSignalType.Custom }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview,
                    AlertNotificationType.Email),
                Properties = new Dictionary<string, string>()
                {
                    [ModelMonitorConstants.AmlComputeClusterIdPropertyKey] = MfeTestingConstants.MonitoringComputeId,
                },
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            var signalEntry = Assert.Single(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(_signalName, signalEntry.Key);

            CustomMonitoringSignal result = Assert.IsType<CustomMonitoringSignal>(signalEntry.Value);

            Assert.Contains(expectedInputDataName, result.InputAssetsV2.Keys);
            MonitoringInputDataBase inputData = Assert.Single(result.InputAssetsV2.Values);
            RollingInputData rollingInputData = Assert.IsType<RollingInputData>(inputData);
            Assert.Equal(JobInputType.UriFolder, rollingInputData.JobInputType);
            Assert.Equal(_targetUri, rollingInputData.Uri);
            Assert.Equal(expectedWindowSize, rollingInputData.WindowSize);
            Assert.Equal(expectedWindowOffset, rollingInputData.WindowOffset);

            Assert.Contains(expectedInputName, result.Inputs.Keys);
            JobInput input = Assert.Single(result.Inputs.Values);
            LiteralJobInput literalJobInput = Assert.IsType<LiteralJobInput>(input);
            Assert.Equal(expectedLiteralValue, literalJobInput.Value);

            var metricThreshold = Assert.Single(result.MetricThresholds);
            Assert.Equal(expectedCustomMetricName, metricThreshold.Metric);
            Assert.Equal(expectedCustomMetricThreshold, metricThreshold.Threshold.Value);
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequestGenerationSafetyQualitySignalSucceeds()
        {
            // Arrange
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = CreateEmptyMonitorSchedule();
            monitorSchedule.MonitorDefinition.ApiVersion = MfeApiVersions.Jun2023Preview.ToSerializedValue();

            double expectedSamplingRate = 0.4;
            double expectedMetricThreshold = 0.5;
            string expectedWorkspaceConnectionId = "myconnectionid";
            KeyValuePair<string, string> expectedPropertyKvp = new KeyValuePair<string, string>("myKey", "myValue");
            var gsqSignal = new InternalContractsMonitors.GenerationSafetyQualityMonitoringSignal
            {
                Mode = InternalContractsMonitors.MonitoringNotificationMode.Enabled,
                SamplingRate = expectedSamplingRate,
                MetricThresholds = new List<InternalContractsMonitors.GenerationSafetyQualityMetricThreshold>
                {
                    new InternalContractsMonitors.GenerationSafetyQualityMetricThreshold()
                    {
                        Metric = InternalContractsMonitors.GenerationSafetyQualityMetric.AggregatedSimilarityPassRate,
                        Threshold = new InternalContractsMonitors.MonitoringThreshold
                        {
                            Value = expectedMetricThreshold
                        }
                    },
                    new InternalContractsMonitors.GenerationSafetyQualityMetricThreshold()
                    {
                        Metric = InternalContractsMonitors.GenerationSafetyQualityMetric.AggregatedCoherencePassRate,
                        Threshold = new InternalContractsMonitors.MonitoringThreshold
                        {
                            Value = expectedMetricThreshold
                        }
                    }
                },
                ProductionData = new List<InternalContractsMonitors.MonitoringInputDataBase>
                {
                    new InternalContractsMonitors.TrailingInputData
                    {
                        JobInputType = InternalContracts.JobInputType.UriFolder,
                        Uri = _targetUri,
                    },
                    new InternalContractsMonitors.TrailingInputData
                    {
                        JobInputType = InternalContracts.JobInputType.UriFolder,
                        Uri = _targetUri,
                    },
                },
                WorkspaceConnectionId = expectedWorkspaceConnectionId,
                Properties = new Dictionary<string, string>
                {
                    [expectedPropertyKvp.Key] = expectedPropertyKvp.Value
                }
            };

            monitorSchedule.MonitorDefinition.Signals[_signalName] = gsqSignal;

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert

            var signalEntry = Assert.Single(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(_signalName, signalEntry.Key);

            GenerationSafetyQualityMonitoringSignal result = Assert.IsType<GenerationSafetyQualityMonitoringSignal>(signalEntry.Value);

            Assert.Equal(expectedWorkspaceConnectionId, result.WorkspaceConnectionId);
            Assert.Equal(NotificationMode.Enabled, result.NotificationMode);
            Assert.Equal(expectedSamplingRate, result.SamplingRate);
            Assert.Null(result.LookbackPeriod);

            var property = Assert.Single(result.Properties);
            Assert.Equal(expectedPropertyKvp.Key, property.Key);
            Assert.Equal(expectedPropertyKvp.Value, property.Value);

            Assert.Equal(2, result.MetricThresholds.Count);
            Assert.Equal(GenerationSafetyQualityMetric.AggregatedSimilarityPassRate, result.MetricThresholds[0].Metric);
            Assert.Equal(expectedMetricThreshold, result.MetricThresholds[0].Threshold.Value);
            Assert.Equal(GenerationSafetyQualityMetric.AggregatedCoherencePassRate, result.MetricThresholds[1].Metric);
            Assert.Equal(expectedMetricThreshold, result.MetricThresholds[1].Threshold.Value);

            Assert.Equal(2, result.ProductionData.Count);
            foreach (var inputData in result.ProductionData)
            {
                var trailingData = Assert.IsType<TrailingInputData>(inputData);
                Assert.Equal(JobInputType.UriFolder, trailingData.JobInputType);
                Assert.Equal(_targetUri, trailingData.Uri);
            }
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequestModelPerformanceSignalSucceeds()
        {
            // Arrange
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = CreateEmptyMonitorSchedule();
            monitorSchedule.MonitorDefinition.ApiVersion = MfeApiVersions.Jun2023Preview.ToSerializedValue();
            var referenceUri = "azureml://uri";
            var expectedMetricThreshold = 0.1;
            var modelPerformanceSignal = new InternalContractsMonitors.ModelPerformanceSignal
            {
                Mode = InternalContractsMonitors.MonitoringNotificationMode.Enabled,
                MetricThresholds = new List<InternalContractsMonitors.ModelPerformanceMetricThresholdBase>
                {
                    new InternalContractsMonitors.RegressionModelPerformanceMetricThreshold()
                    {
                        Metric = InternalContractsMonitors.RegressionModelPerformanceMetric.MeanSquaredError,
                        Threshold = new InternalContractsMonitors.MonitoringThreshold
                        {
                            Value = expectedMetricThreshold
                        }
                    },
                },
                ProductionData = new List<InternalContractsMonitors.MonitoringInputDataBase>
                {
                    new InternalContractsMonitors.TrailingInputData
                    {
                        JobInputType = InternalContracts.JobInputType.UriFolder,
                        Uri = _targetUri,
                        Columns = new Dictionary<string, string>()
                        {
                            { "join_column", "correlationid" },
                            { "production_data_target_column", "regression-targetvalue" }
                        }
                    },
                },
                ReferenceData = new InternalContractsMonitors.TrailingInputData
                {
                    JobInputType = InternalContracts.JobInputType.UriFolder,
                    Uri = referenceUri,
                    Columns = new Dictionary<string, string>()
                    {
                        { "join_column", "correlationid" },
                        { "baseline_data_target_column", "regression" }
                    }
                },
                Properties = new Dictionary<string, string>
                {
                    [ModelMonitorConstants.ModelPerformanceThresholdsPropertyKey] = "[{\"modelType\":\"regression\",\"metric\":\"MeanAbsoluteError\",\"threshold\":{\"value\":0.1}},{\"modelType\":\"regression\",\"metric\":\"RootMeanSquaredError\",\"threshold\":{\"value\":0.1}}]",
                },
            };
            var monitoringTarget = new InternalContractsMonitors.MonitoringTarget
            {
                ModelId = "id",
                DeploymentId = "id",
                TaskType = InternalContractsMonitors.ModelTaskType.Regression,
            };
            monitorSchedule.MonitorDefinition.Signals[_signalName] = modelPerformanceSignal;
            monitorSchedule.MonitorDefinition.MonitoringTargets = monitoringTarget;

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);
            var signalEntry = Assert.Single(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(_signalName, signalEntry.Key);

            ModelPerformanceSignal result = Assert.IsType<ModelPerformanceSignal>(signalEntry.Value);

            Assert.Equal(NotificationMode.Enabled, result.NotificationMode);

            Assert.Null(result.LookbackPeriod);

            Assert.Null(result.Properties);

            Assert.Equal(2, result.MetricThresholds.Count);
            Assert.Equal(MonitoringModelType.Regression, result.MetricThresholds[0].ModelType);
            Assert.Equal(expectedMetricThreshold, result.MetricThresholds[0].Threshold.Value);
            Assert.Equal(MonitoringModelType.Regression, result.MetricThresholds[1].ModelType);
            Assert.Equal(expectedMetricThreshold, result.MetricThresholds[1].Threshold.Value);

            Assert.Equal(1, result.ProductionData.Count);
            foreach (var inputData in result.ProductionData)
            {
                var trailingData = Assert.IsType<TrailingInputData>(inputData);
                Assert.Equal(JobInputType.UriFolder, trailingData.JobInputType);
                Assert.Equal(_targetUri, trailingData.Uri);
            }
            Assert.Equal(referenceUri, result.ReferenceData.Uri);
            Assert.Equal(JobInputType.UriFolder, result.ReferenceData.JobInputType);
            Assert.Equal("correlationid", result.ReferenceData.Columns["join_column"]);
            Assert.Equal("regression", result.ReferenceData.Columns["baseline_data_target_column"]);
            Assert.Equal("correlationid", result.ReferenceData.Columns["join_column"]);
            Assert.Equal("regression", result.ReferenceData.Columns["baseline_data_target_column"]);
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequest_UseAmlComputeCluster_Succeeds()
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Jun2023Preview,
                    AlertNotificationType.Email),
                Properties = new Dictionary<string, string>()
                {
                    [ModelMonitorConstants.AmlComputeClusterIdPropertyKey] = MfeTestingConstants.MonitoringComputeId,
                },
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            AssertCreateModelMonitorRequestProperties(
                createModelMonitorRequest,
                populateRequiredPropertiesOnly: false,
                monitoringSignals,
                AlertNotificationType.Email,
                MfeApiVersions.Jun2023Preview.ToSerializedValue(),
                useAmlClusterCompute: true);
        }

        [Theory]
        [MemberData(nameof(InvalidPropertiesAndTaskType))]
        public void ConvertToCreateModelMonitorRequestModelPerformanceSignal_PropertiesValidationFailed(InternalContractsMonitors.ModelTaskType? taskType, IDictionary<string, string> test_properties, string errormessage)
        {
            // Arrange
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = CreateEmptyMonitorSchedule();
            monitorSchedule.MonitorDefinition.ApiVersion = MfeApiVersions.Jun2023Preview.ToSerializedValue();
            var referenceUri = "azureml://uri";
            var expectedMetricThreshold = 0.1;
            var modelPerformanceSignal = new InternalContractsMonitors.ModelPerformanceSignal
            {
                Mode = InternalContractsMonitors.MonitoringNotificationMode.Enabled,
                MetricThresholds = new List<InternalContractsMonitors.ModelPerformanceMetricThresholdBase>
                {
                    new InternalContractsMonitors.RegressionModelPerformanceMetricThreshold()
                    {
                        Metric = InternalContractsMonitors.RegressionModelPerformanceMetric.MeanSquaredError,
                        Threshold = new InternalContractsMonitors.MonitoringThreshold
                        {
                            Value = expectedMetricThreshold
                        }
                    },
                },
                ProductionData = new List<InternalContractsMonitors.MonitoringInputDataBase>
                {
                    new InternalContractsMonitors.TrailingInputData
                    {
                        JobInputType = InternalContracts.JobInputType.UriFolder,
                        Uri = _targetUri,
                        Columns = new Dictionary<string, string>()
                        {
                            { "join_column", "correlationid" },
                            { "production_data_target_column", "regression-targetvalue" }
                        }
                    },
                },
                ReferenceData = new InternalContractsMonitors.TrailingInputData
                {
                    JobInputType = InternalContracts.JobInputType.UriFolder,
                    Uri = referenceUri,
                    Columns = new Dictionary<string, string>()
                    {
                        { "join_column", "correlationid" },
                        { "baseline_data_target_column", "regression" }
                    }
                },
                Properties = test_properties,
            };
            var monitoringTarget = new InternalContractsMonitors.MonitoringTarget
            {
                ModelId = "id",
                DeploymentId = "id",
                TaskType = taskType,
            };
            monitorSchedule.MonitorDefinition.Signals[_signalName] = modelPerformanceSignal;
            monitorSchedule.MonitorDefinition.MonitoringTargets = monitoringTarget;

            try
            {
                // Act
                CreateModelMonitorRequest createModelMonitorRequest = _converter
                    .ConvertToCreateModelMonitorRequest(monitorSchedule);
                Assert.Fail("Expecting validation errors, but we do not get any.");
            } catch (Exception ex)
            {
                Assert.Contains(errormessage, ex.Message);
            }
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("  ")]
        public void ConvertToCreateModelMonitorRequest_AmlComputeIdMissingNullOrEmpty_Throws(string propertyValue)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Jun2023Preview,
                    AlertNotificationType.Email),
                Properties = new Dictionary<string, string> { [ModelMonitorConstants.AmlComputeClusterIdPropertyKey] = propertyValue },
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            // Act & Assert
            Func<CreateModelMonitorRequest> act = () => _converter.ConvertToCreateModelMonitorRequest(monitorSchedule);
            act.Should()
                .Throw<BadRequestException>()
                .WithMessage($"The value of property '{ModelMonitorConstants.AmlComputeClusterIdPropertyKey}' is null, empty or whitespaces.");
        }

        [Fact]
        public void ConvertToCreateModelMonitorRequest_NullComputeConfiguration_Succeeds()
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Jun2023Preview,
                    AlertNotificationType.Email),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            monitorSchedule.MonitorDefinition.ComputeConfiguration = null;

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            AssertCreateModelMonitorRequestProperties(
                createModelMonitorRequest,
                populateRequiredPropertiesOnly: false,
                monitoringSignals,
                AlertNotificationType.Email,
                MfeApiVersions.Jun2023Preview.ToSerializedValue(),
                useAmlClusterCompute: false,
                assertOnServerlessCompute: false);
        }

        [Theory]
        [InlineData(MonitorComputeIdentityType.AmlToken, ManagedServiceIdentityType.SystemAssigned)]
        [InlineData(MonitorComputeIdentityType.ManagedIdentity, ManagedServiceIdentityType.SystemAssigned)]
        [InlineData(MonitorComputeIdentityType.ManagedIdentity, ManagedServiceIdentityType.UserAssigned)]
        [InlineData(MonitorComputeIdentityType.ManagedIdentity, ManagedServiceIdentityType.SystemAssignedUserAssigned)]
        [InlineData(MonitorComputeIdentityType.ManagedIdentity, ManagedServiceIdentityType.None)]
        public void ConvertToCreateModelMonitorRequest_ServerlessSparkCompute_ComputeIdentity(
            MonitorComputeIdentityType monitorComputeIdentityType,
            ManagedServiceIdentityType managedServiceIdentityType)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview,
                    AlertNotificationType.Email),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };

            (monitorSchedule.MonitorDefinition.ComputeConfiguration as InternalSharedServices.Contracts.Schedules.Monitors.MonitorServerlessSparkCompute).ComputeIdentity 
                = ModelMonitorTestDataGenerator.GetInternalComputeIdentity(monitorComputeIdentityType, managedServiceIdentityType);

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert

            Assert.Equivalent(
                ModelMonitorTestDataGenerator.GetComputeIdentity(monitorComputeIdentityType, managedServiceIdentityType),
                (createModelMonitorRequest.ComputeSettings as ServerlessSparkComputeSettings).ComputeIdentity);
        }


        [Theory]
        [InlineData(InternalContractsMonitors.MonitoringNotificationMode.Enabled, NotificationMode.Enabled)]
        [InlineData(InternalContractsMonitors.MonitoringNotificationMode.Disabled, NotificationMode.Disabled)]
        public void Test_ConvertNotifacationMode_NotificationTypesNull_ShouldTakeValueFromMode(InternalContractsMonitors.MonitoringNotificationMode mode, NotificationMode expectedNotificationMode)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview,
                    AlertNotificationType.Email),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };
            monitorSchedule.MonitorDefinition.Signals["signalName"].NotificationTypes = null;
            monitorSchedule.MonitorDefinition.Signals["signalName"].Mode = mode;

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            Assert.Equal(expectedNotificationMode, createModelMonitorRequest.MonitoringSignals["signalName"].NotificationMode);
        }

        [Theory]
        [InlineData(true, NotificationMode.Enabled)]
        [InlineData(false, NotificationMode.Disabled)]
        public void Test_ConvertNotifacationMode_ShouldTakeValueFromNotificationTypes(
            bool hasAmlNotification, NotificationMode expectedNotificationMode)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview,
                    AlertNotificationType.Email),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };
            if (hasAmlNotification)
            {
                monitorSchedule.MonitorDefinition.Signals["signalName"].NotificationTypes = new List<InternalContractsMonitors.MonitoringNotificationType>() 
                { 
                    InternalContractsMonitors.MonitoringNotificationType.AmlNotification,
                    InternalContractsMonitors.MonitoringNotificationType.AzureMonitor
                };
            }
            else
            {
                monitorSchedule.MonitorDefinition.Signals["signalName"].NotificationTypes = new List<InternalContractsMonitors.MonitoringNotificationType>()
                {
                    InternalContractsMonitors.MonitoringNotificationType.AzureMonitor
                };
            }

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            Assert.Equal(expectedNotificationMode, createModelMonitorRequest.MonitoringSignals["signalName"].NotificationMode);
        }


        [Theory]
        [InlineData(null)]
        [InlineData(InternalContractsMonitors.ModelTaskType.Classification)]
        [InlineData(InternalContractsMonitors.ModelTaskType.Regression)]
        [InlineData(InternalContractsMonitors.ModelTaskType.QuestionAnswering)]
        public void Test_ConvertMonitoringTargets_TaskType(InternalContractsMonitors.ModelTaskType? modelTaskType)
        {
            // Arrange
            IDictionary<string, MonitoringSignalType> monitoringSignals = new Dictionary<string, MonitoringSignalType>
            {
                { "signalName", MonitoringSignalType.DataDrift }
            };
            InternalContracts.Schedules.MonitorSchedule monitorSchedule = new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = GetMonitorDefinition(
                    populateRequiredPropertiesOnly: false,
                    monitoringSignals,
                    MfeApiVersions.Aug2023Preview,
                    AlertNotificationType.Email),
                Tags = new Dictionary<string, string> { ["key"] = "value" }
            };
            monitorSchedule.MonitorDefinition.MonitoringTargets = new InternalContractsMonitors.MonitoringTarget()
            {
                ModelId = "model_id",
                DeploymentId = "deployment_id",
                TaskType = modelTaskType
            };

            // Act
            CreateModelMonitorRequest createModelMonitorRequest = _converter
                .ConvertToCreateModelMonitorRequest(monitorSchedule);

            // Assert
            Assert.NotNull(createModelMonitorRequest.MonitoringTargets);
            Assert.Equal("model_id", createModelMonitorRequest.MonitoringTargets.ModelId);
            Assert.Equal("deployment_id", createModelMonitorRequest.MonitoringTargets.DeploymentId);
            if (modelTaskType == null)
            {
                Assert.Null(createModelMonitorRequest.MonitoringTargets.TaskType);
            }
            else
            {
                Assert.Equal(modelTaskType.ToString(), createModelMonitorRequest.MonitoringTargets.TaskType.ToString());
            }
        }

        #region Helpers
        private InternalContracts.Schedules.MonitorSchedule CreateEmptyMonitorSchedule()
            => new InternalContracts.Schedules.MonitorSchedule
            {
                MonitorDefinition = new InternalContractsMonitors.MonitorDefinition
                {
                    Signals = new Dictionary<string, InternalContractsMonitors.MonitoringSignalBase>()
                }
            };

        private InternalContracts.Schedules.Monitors.DataDriftMonitoringSignal CreateDataDriftLegacySignal()
            => new InternalContractsMonitors.DataDriftMonitoringSignal
            {
                BaselineData = new InternalContractsMonitors.MonitoringInputData
                {
                    TargetColumnName = _targetColumn,
                    Asset = new InternalContracts.MLTableJobInput
                    {
                        Uri = _baselineUri
                    }
                },
                TargetData = new InternalContractsMonitors.MonitoringInputData
                {
                    Asset = new InternalContracts.UriFolderJobInput
                    {
                        Uri = _targetUri
                    },
                    PreprocessingComponentId = _preprocessingComponent
                },
                ProductionData = null,
                ReferenceData = null,
                LookbackPeriod = this._lookbackPeriod,
                MetricThresholds = new List<InternalContractsMonitors.DataDriftMetricThresholdBase>
                {
                    new InternalContractsMonitors.NumericalDataDriftMetricThreshold()
                    {
                        Metric = InternalContractsMonitors.NumericalDataDriftMetric.PopulationStabilityIndex,
                        Threshold = new InternalContractsMonitors.MonitoringThreshold
                        {
                            Value = 0.1
                        }
                    },
                    new InternalContractsMonitors.CategoricalDataDriftMetricThreshold()
                    {
                        Metric = InternalContractsMonitors.CategoricalDataDriftMetric.PopulationStabilityIndex,
                        Threshold = new InternalContractsMonitors.MonitoringThreshold
                        {
                            Value = 0.1
                        }
                    }
                }
            };

        private void AssertJobEquality(
            ModelMonitorJob monitorJob,
            InternalContracts.PipelineJob pipelineJob)
        {
            Assert.Equal(monitorJob.ComponentId, pipelineJob.ComponentId);
            Assert.Equal(monitorJob.Description, pipelineJob.Description);
            Assert.Equal(monitorJob.DisplayName, pipelineJob.DisplayName);
            Assert.Equal(monitorJob.ExperimentName, pipelineJob.ExperimentName);
            pipelineJob.InputBindings.Should().BeEquivalentTo(monitorJob.InputBindings);

            pipelineJob.Jobs.Should().BeEquivalentTo(
                monitorJob.Jobs?
                    .ToDictionary(
                        j => j.Key,
                        j => JObject.FromObject(j.Value, ModelMonitorHelper.GenerateModelMonitorChildJobJsonSerializer())));

            if (monitorJob?.NotificationSetting == null)
            {
                Assert.Null(pipelineJob.NotificationSetting);
            }
            else if (monitorJob.NotificationSetting.AlertNotificationType == AlertNotificationType.Email)
            {
                AssertEmailNotificationSettingEquality(
                    (monitorJob.NotificationSetting as EmailAlertNotificationSettings).EmailNotificationSettings,
                    pipelineJob.NotificationSetting);
            }

            pipelineJob.OutputBindings.Should().BeEquivalentTo(monitorJob.OutputBindings);

            pipelineJob.Settings.Should()
                .BeEquivalentTo(monitorJob.Settings == null ? null : JObject.FromObject(monitorJob.Settings));

            pipelineJob.Tags.Should().BeEquivalentTo(monitorJob.Tags);
        }

        private void AssertEmailNotificationSettingEquality(
            EmailNotificationSettings emailNotificationSettings,
            InternalContracts.NotificationSetting notificationSetting)
        {
            emailNotificationSettings.Emails.Should().BeEquivalentTo(notificationSetting.Emails);
            emailNotificationSettings.EmailOn.Should().BeEquivalentTo(notificationSetting.EmailOn, opt => opt.ComparingEnumsByName());

            emailNotificationSettings.Webhooks.ToList().ForEach(kvPair =>
            {
                notificationSetting.Webhooks[kvPair.Key].Should().BeEquivalentTo(kvPair.Value);
            });
        }

        private InternalContractsMonitors.MonitorDefinition GetMonitorDefinition(
            bool populateRequiredPropertiesOnly,
            IDictionary<string, MonitoringSignalType> monitoringSignals,
            MfeApiVersions mfeApiVersion,
            AlertNotificationType alertNotificationType = AlertNotificationType.Email)
        {
            InternalContractsMonitors.MonitorDefinition monitorDefinition = new InternalContractsMonitors.MonitorDefinition
            {
                AlertNotificationSetting = mfeApiVersion <= MfeApiVersions.Jun2023Preview 
                    ? ModelMonitorTestDataGenerator.GetInternalAlertNotificationSettings(alertNotificationType, populateRequiredPropertiesOnly)
                    : null,
                ApiVersion = mfeApiVersion.ToSerializedValue(),
                ComputeConfiguration = new InternalContractsMonitors.MonitorServerlessSparkCompute
                {
                    InstanceType = MfeTestingConstants.MonitoringInstanceType,
                    RuntimeVersion = MfeTestingConstants.MonitoringRuntime,
                },
                MonitoringTarget = MfeTestingConstants.MonitoringTarget,
                Signals = new Dictionary<string, InternalContractsMonitors.MonitoringSignalBase>(),
                AlertNotificationSettings = mfeApiVersion <= MfeApiVersions.Jun2023Preview || populateRequiredPropertiesOnly
                ? null 
                : ModelMonitorTestDataGenerator.GetInternalMonitorNotificationSettings(false, false, false)
            };

            foreach (KeyValuePair<string, MonitoringSignalType> monitoringSignal in monitoringSignals)
            {
                monitorDefinition.Signals.Add(
                    monitoringSignal.Key,
                    mfeApiVersion <= MfeApiVersions.Jun2023Preview
                        ? ModelMonitorTestDataGenerator
                            .GetInternalLegacyMonitoringSignal(monitoringSignal.Value, populateRequiredPropertiesOnly)
                        : ModelMonitorTestDataGenerator
                            .GetInternalMonitoringSignal(monitoringSignal.Value, populateRequiredPropertiesOnly));
            }

            return monitorDefinition;
        }

        private InternalContractsMonitors.MonitorDefinition GetValidModelPerformanceSignal(
            bool populateRequiredPropertiesOnly,
            InternalContractsMonitors.MonitorDefinition monitorDefinition)
        {
            monitorDefinition.MonitoringTargets = new InternalContractsMonitors.MonitoringTarget { TaskType = InternalContractsMonitors.ModelTaskType.Regression };
            return monitorDefinition;
        }

        private ModelMonitorJob GetModelMonitorJob()
            => new ModelMonitorJob
            {
                ComputeId = MfeTestingConstants.MonitoringComputeId,
                ComponentId = MfeTestingConstants.MonitoringComponentId,
                Description = "hello",
                DisplayName = "datadrift",
                ExperimentName = "my-experiment",
                InputBindings = new Dictionary<string, InternalContracts.InputDataBinding>(),
                NotificationSetting = new EmailAlertNotificationSettings
                {
                    EmailNotificationSettings = new EmailNotificationSettings
                    {
                        EmailOn = new List<EmailNotificationEnableType> { EmailNotificationEnableType.JobCompleted },
                        Emails = new List<string> { "<EMAIL>" },
                        Webhooks = new Dictionary<string, Webhook>()
                    }
                },
                OutputBindings = new Dictionary<string, InternalContracts.OutputDataBinding>(),
            };

        private void AssertCreateModelMonitorRequestProperties(
            CreateModelMonitorRequest createModelMonitorRequest,
            bool populateRequiredPropertiesOnly,
            IDictionary<string, MonitoringSignalType> monitoringSignals,
            AlertNotificationType alertNotificationType,
            string expectedMfeApiVersion,
            bool useAmlClusterCompute,
            bool assertOnServerlessCompute = true)
        {
            Assert.NotNull(createModelMonitorRequest);

            // Assert alert notification settings.
            if (expectedMfeApiVersion.ParseAsMfeApiVersion() > MfeApiVersions.Jun2023Preview && populateRequiredPropertiesOnly)
            {
                // the allertNotificationSetting has been changed since Aug2023Preview
                Assert.Null(createModelMonitorRequest.AlertNotificationSetting);
            }
            else
            {
                Assert.NotNull(createModelMonitorRequest.AlertNotificationSetting);
                Assert.Equivalent(
                    ModelMonitorTestDataGenerator.GetAlertNotificationSettings(
                        alertNotificationType,
                        populateRequiredPropertiesOnly,
                        isLegacy: expectedMfeApiVersion.ParseAsMfeApiVersion() <= MfeApiVersions.Jun2023Preview),
                    createModelMonitorRequest.AlertNotificationSetting);
            }

            // Assert signals
            foreach (KeyValuePair<string, MonitoringSignalType> monitoringSignal in monitoringSignals)
            {
                Assert.True(createModelMonitorRequest.MonitoringSignals.ContainsKey(monitoringSignal.Key));

                createModelMonitorRequest.MonitoringSignals[monitoringSignal.Key]
                    .Should()
                    .BeEquivalentTo(ModelMonitorTestDataGenerator.GetMonitoringSignal(
                        monitoringSignal.Value,
                        populateRequiredPropertiesOnly,
                        expectedMfeApiVersion.ParseAsMfeApiVersion() <= MfeApiVersions.Jun2023Preview));
            }

            // Assert compute settings
            Assert.NotNull(createModelMonitorRequest.ComputeSettings);
            if (useAmlClusterCompute)
            {
                Assert.Equal(ComputeType.AzureMLComputeCluster, createModelMonitorRequest.ComputeSettings.ComputeType);
                Assert.Equal(MfeTestingConstants.MonitoringComputeId, (createModelMonitorRequest.ComputeSettings as AmlClusterComputeSettings).ComputeId);
            }
            else
            {
                Assert.Equal(ComputeType.ServerlessSpark, createModelMonitorRequest.ComputeSettings.ComputeType);

                if (assertOnServerlessCompute)
                {
                    Assert.Null((createModelMonitorRequest.ComputeSettings as ServerlessSparkComputeSettings).ComputeIdentity);
                    Assert.Equal(MfeTestingConstants.MonitoringInstanceType, (createModelMonitorRequest.ComputeSettings as ServerlessSparkComputeSettings).InstanceType);
                    Assert.Equal(MfeTestingConstants.MonitoringRuntime, (createModelMonitorRequest.ComputeSettings as ServerlessSparkComputeSettings).RuntimeVersion);
                }
            }

            // Assert other properties.
            Assert.Equal(expectedMfeApiVersion, createModelMonitorRequest.ApiVersion);
            Assert.NotEmpty(createModelMonitorRequest.MonitoringSignals);
            Assert.Equal(MfeTestingConstants.MonitoringTarget, createModelMonitorRequest.MonitoringTarget);
        }

        #endregion
    }
}
