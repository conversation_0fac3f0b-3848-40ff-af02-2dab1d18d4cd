﻿// <copyright file="BatchJobConverterV2Tests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using FakeItEasy;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Endpoints.Batch.Contracts.DataPlane.V1;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters.Tests
{
    [MfeTest(TestCategory.Unit, ScenarioScope.BatchServices)]
    public class BatchJobConverterV2Tests
    {
        private readonly IBatchJobInputConverterV2 _inputDataConverter;
        private readonly IBatchInteractionEndpointsConverterV2 _interactionEndpointsConverter;
        private readonly IBatchJobOutputConverterV2 _outputDataConverter;
        private readonly IBatchJobConverterV2 _target;
        private readonly WorkspaceContext2 _testWorkspace;

        public BatchJobConverterV2Tests()
        {
            _inputDataConverter = A.Fake<IBatchJobInputConverterV2>();
            _interactionEndpointsConverter = A.Fake<IBatchInteractionEndpointsConverterV2>();
            _outputDataConverter = A.Fake<IBatchJobOutputConverterV2>();

            _target = new BatchJobConverterV2(
                _inputDataConverter,
                _interactionEndpointsConverter,
                _outputDataConverter);

            _testWorkspace = new WorkspaceContext2(
                Guid.Empty,
                "rgName",
                "wsName",
                Guid.Empty,
                "eastus",
                "msfttenant",
                new Dictionary<string, string>());
        }

        [Fact]
        public void TestConvertToResourceArmDtoSuccess()
        {
            // Arrange
            JobResponse jobResponse = GetJobResponse();
            InternalSharedServices.Contracts.Resource<BatchJob> expectedResult = GetResourceWithDeployment(GetBatchJob());

            // Act
            InternalSharedServices.Contracts.Resource<BatchJob> result = _target.ConvertToResourceArmDto(
                _testWorkspace,
                jobResponse);

            // Assert
            result.Should().NotBeNull();
            result.Name.Should().BeEquivalentTo(expectedResult.Name);
            result.Id.Should().BeEquivalentTo(expectedResult.Id);
            result.SystemData.Should().BeEquivalentTo(expectedResult.SystemData);
            result.Type.Should().BeEquivalentTo(expectedResult.Type);

            result.Properties.Compute.Should().BeEquivalentTo(expectedResult.Properties.Compute);
            result.Properties.Compute.Target.Should().BeEquivalentTo(expectedResult.Properties.Compute.Target);
            result.Properties.Description.Should().BeEquivalentTo(expectedResult.Properties.Description);
            result.Properties.ErrorThreshold.Equals(expectedResult.Properties.ErrorThreshold);
            result.Properties.LoggingLevel.Equals(expectedResult.Properties.LoggingLevel);
            result.Properties.MaxConcurrencyPerInstance.Equals(expectedResult.Properties.MaxConcurrencyPerInstance);
            result.Properties.MiniBatchSize.Equals(expectedResult.Properties.MiniBatchSize);
            result.Properties.Output.Should().BeEquivalentTo(expectedResult.Properties.Output);
            result.Properties.OutputFileName.Should().BeEquivalentTo(expectedResult.Properties.OutputFileName);
            result.Properties.PartitionKeys.Should().BeEquivalentTo(expectedResult.Properties.PartitionKeys);
            result.Properties.RetrySettings.Should().BeEquivalentTo(expectedResult.Properties.RetrySettings);
            result.Properties.Status.Equals(expectedResult.Properties.Status);
            result.Properties.Tags.Should().BeEquivalentTo(expectedResult.Properties.Tags);
        }

        [Fact]
        public void TestConvertToCreateJobRequestSuccess()
        {
            // Arrange
            CreateJobRequest expectedResult = new CreateJobRequest();
            SetCommonPropertiesInJobRequestAndResponse(expectedResult);
            InternalSharedServices.Contracts.Resource<BatchJob> batchJob = GetResourceWithDeployment(GetBatchJob());

            // Act
            CreateJobRequest result = _target.ConvertToCreateRequest(batchJob);

            // Assert
            result.Description.Should().BeEquivalentTo(expectedResult.Description);
            result.ErrorThreshold.Equals(expectedResult.ErrorThreshold);
            result.InstanceCount.Equals(expectedResult.InstanceCount);
            result.LoggingLevel.Equals(expectedResult.LoggingLevel);
            result.MaxConcurrencyPerInstance.Equals(expectedResult.MaxConcurrencyPerInstance);
            result.MiniBatchSize.Equals(expectedResult.MiniBatchSize);
            result.PartitionKeys.Should().BeEquivalentTo(expectedResult.PartitionKeys);
            result.Properties.Should().BeEquivalentTo(expectedResult.Properties);
            result.RetryConfiguration.Should().BeEquivalentTo(expectedResult.RetryConfiguration);
            result.Tags.Should().BeEquivalentTo(expectedResult.Tags);
        }

        [Fact]
        public void TestConvertToCreateJobRequestWithMissingRequiredFieldsSuccess()
        {
            // Arrange
            InternalSharedServices.Contracts.Resource<BatchJob> batchJob = GetResourceWithDeployment(new BatchJob());

            // Act
            CreateJobRequest result = _target.ConvertToCreateRequest(batchJob);

            // Assert
            result.Should().NotBeNull();
        }

        private JobResponse GetJobResponse()
        {
            JobResponse jobResponse = new JobResponse
            {
                DebugOutput = new JobOutputArtifacts
                {
                    DatastoreId = "my-datastore",
                    Path = "/"
                },
                ComputeTarget = "an/arm/id/of/compute/cluster",
                InteractionEndpoints = null, // Tested separately
                Id = "12345",
                Status = JobStatus.Completed,
                SystemData = new Endpoints.Batch.Contracts.SystemData
                {
                    CreatedTime = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromHours(-7)),
                    CreatedBy = "user-123",
                    LastModifiedTime = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromHours(-7)),
                    LastModifiedBy = "user-456"
                }
            };

            SetCommonPropertiesInJobRequestAndResponse(jobResponse);

            return jobResponse;
        }

        private void SetCommonPropertiesInJobRequestAndResponse(CreateJobRequest input)
        {
            input.Description = "my-job";
            input.DeploymentName = "deploymentName";
            input.EndpointName = "endpointName";
            input.ErrorThreshold = 3;
            input.InstanceCount = 3;
            input.InputData = null; // Tested separately
            input.LoggingLevel = LoggingLevel.Debug;
            input.MaxConcurrencyPerInstance = 3;
            input.MiniBatchSize = 3;
            input.OutputData = null; // Tested separately
            input.OutputFileName = "output.csv";
            input.PartitionKeys = new List<string>();
            input.Properties = new Dictionary<string, string>();
            input.RetryConfiguration = new RetryConfiguration
            {
                RetryCount = 3,
                RetryInterval = new TimeSpan(5)
            };
            input.Tags = new Dictionary<string, string>();
        }

        private InternalSharedServices.Contracts.Resource<T> GetResourceWithDeployment<T>(T input)
            => new InternalSharedServices.Contracts.Resource<T>
            {
                Id = MfeResourceArmScope.ToString(
                    _testWorkspace,
                    MfeConstants.ArmTypeBatchEndpoint,
                    "endpointName",
                    MfeConstants.ArmTypeBatchEndpointDeployment,
                    "deploymentName",
                    MfeConstants.ArmTypeBatchJob,
                    "12345"),
                Name = "12345",
                Properties = input,
                SystemData = new InternalSharedServices.Contracts.SystemData
                {
                    CreatedAt = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromHours(-7)),
                    CreatedBy = "user-123",
                    LastModifiedAt = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromHours(-7)),
                    LastModifiedBy = "user-456"
                },
                Type = MfeConstants.ArmFullTypeBatchDeploymentJobs
            };

        private BatchJob GetBatchJob()
           => new BatchJob
           {
                Compute = new InternalSharedServices.Contracts.ComputeConfiguration
                {
                    InstanceCount = 3,
                    Target = "an/arm/id/of/compute/cluster"
                },
                Description = "my-job",
                ErrorThreshold = 3,
                InputData = null, // Tested separately
                InteractionEndpoints = null, // Tested separately
                LoggingLevel = InternalSharedServices.Contracts.BatchLoggingLevel.Debug,
                MaxConcurrencyPerInstance = 3,
                MiniBatchSize = 3,
                Output = new InternalSharedServices.Contracts.JobOutputArtifacts
                {
                    DatastoreId = "my-datastore",
                    Path = "/"
                },
                OutputData = null, // Tested separately
                OutputFileName = "output.csv",
                PartitionKeys = new List<string>(),
                Properties = new Dictionary<string, string>(),
                RetrySettings = new InternalSharedServices.Contracts.Endpoints.BatchInference.BatchRetrySettings
                {
                    MaxRetries = 3,
                    Timeout = new TimeSpan(5)
                },
                Status = InternalSharedServices.Contracts.JobStatus.Completed,
                Tags = new Dictionary<string, string>()
           };
    }
}