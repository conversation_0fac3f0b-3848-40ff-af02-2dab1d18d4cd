﻿// <copyright file="BatchDeploymentConverterV2Tests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using FakeItEasy;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core.Contracts;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.Endpoints.Batch.Contracts.ControlPlane.V1;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.BatchInference;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Services.MfeResourceArm;
using Xunit;
using InternalDeploymentProvisioningState = Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Endpoints.OnlineEndpointDeployment.DeploymentProvisioningState;

namespace Microsoft.MachineLearning.ManagementFrontEnd.Services.Converters.Tests
{
    [MfeTest(TestCategory.Unit, ScenarioScope.BatchServices)]
    public class BatchDeploymentConverterV2Tests
    {
        private readonly IArmContextAccessor _armContextAccessor;
        private readonly IApiVersionAccessor _apiVersionAccessor;
        private readonly IBatchDeploymentConverterV2 _target;
        private readonly WorkspaceContext2 _testWorkspace;

        public BatchDeploymentConverterV2Tests()
        {
            _armContextAccessor = A.Fake<IArmContextAccessor>();
            _apiVersionAccessor = A.Fake<IApiVersionAccessor>();
            _target = new BatchDeploymentConverterV2(
                _armContextAccessor,
                _apiVersionAccessor);
            _testWorkspace = new WorkspaceContext2(
                Guid.Empty,
                "rgName",
                "wsName",
                Guid.Empty,
                "eastus",
                "msfttenant",
                new Dictionary<string, string>());
        }

        [Theory]
        [InlineData(true, true)]
        [InlineData(false, true)]
        [InlineData(false, false)]
        public void TestConvertToCreateModelDeploymentRequestSucceeds(bool isCustomModel, bool isModelIdAssetReference)
        {
            // Arrange
            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(
                GetBatchDeployment(isCustomModel, isModelIdAssetReference));

            CreateModelDeploymentRequest expectedResult = GetCreateDeploymentRequest(isCustomModel, isModelIdAssetReference);

            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToCreateModelDeploymentRequestSucceedsWithRequiredFieldsMissing()
        {
            // Arrange
            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(new BatchDeployment());

            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().NotBeNull();
        }

        [Fact]
        public void TestConvertToCreateComponentDeploymentRequestPrivatePreviewSucceeds()
        {
            // Arrange
            CreateComponentDeploymentRequest expectedResult = GetCreateComponentDeploymentRequest();

            Dictionary<string, string> properties = new Dictionary<string, string>
            {
                { "ComponentDeployment.ComponentId", expectedResult.ComponentId },
                { "ComponentDeployment.Description", expectedResult.JobSettings.Description },
                { "ComponentDeployment.Tags.myTag1.cool", expectedResult.JobSettings.Tags["myTag1.cool"] },
                { "ComponentDeployment.Tags.myTag2", expectedResult.JobSettings.Tags["myTag2"] },
                { "ComponentDeployment.Settings.default_datastore", expectedResult.JobSettings.RunSettings.default_datastore },
                { "ComponentDeployment.Settings.default_compute", expectedResult.JobSettings.RunSettings.default_compute },
                { "ComponentDeployment.Settings.continue_on_step_failure", expectedResult.JobSettings.RunSettings.continue_on_step_failure.ToString() },
                { "ComponentDeployment.Settings.force_rerun", expectedResult.JobSettings.RunSettings.force_rerun.ToString()}
            };

            expectedResult.Properties = properties;

            BatchDeployment deployment = new BatchDeployment
            {
                Description = expectedResult.Description,
                Properties = properties,
            };

            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(deployment);
            trackedResourceBatchDeployment.Location = expectedResult.Location;
            trackedResourceBatchDeployment.Tags = (CaseInsensitiveDictionary)expectedResult.Tags;
            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToCreateComponentDeploymentRequestNoTagsNoSettingsPrivatePreviewSucceeds()
        {
            // Arrange
            CreateComponentDeploymentRequest expectedResult = GetCreateComponentDeploymentRequest();
            expectedResult.JobSettings.Tags = new Dictionary<string, string>();
            expectedResult.JobSettings.RunSettings = new RunSettings();

            Dictionary<string, string> properties = new Dictionary<string, string>
            {
                { "ComponentDeployment.ComponentId", expectedResult.ComponentId },
                { "ComponentDeployment.Description", expectedResult.JobSettings.Description },
            };

            expectedResult.Properties = properties;

            BatchDeployment deployment = new BatchDeployment
            {
                Description = expectedResult.Description,
                Properties = properties,
            };

            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(deployment);
            trackedResourceBatchDeployment.Location = expectedResult.Location;
            trackedResourceBatchDeployment.Tags = (CaseInsensitiveDictionary)expectedResult.Tags;
            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToCreateComponentDeploymentRequestWrongCasingIgnoredPrivatePreviewSucceeds()
        {
            // Arrange
            CreateComponentDeploymentRequest expectedResult = GetCreateComponentDeploymentRequest();
            expectedResult.JobSettings.Tags = new Dictionary<string, string>();
            expectedResult.JobSettings.RunSettings = new RunSettings();

            Dictionary<string, string> properties = new Dictionary<string, string>
            {
                { "ComponentDeployment.componentId", Guid.NewGuid().ToString() },
                { "ComponentDeployment.ComponentId", expectedResult.ComponentId },
                { "ComponentDeployment.ComponenTId", Guid.NewGuid().ToString() },
                { "ComponentDeployment.Description", expectedResult.JobSettings.Description },
            };

            expectedResult.Properties = properties;

            BatchDeployment deployment = new BatchDeployment
            {
                Description = expectedResult.Description,
                Properties = properties,
            };

            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(deployment);
            trackedResourceBatchDeployment.Location = expectedResult.Location;
            trackedResourceBatchDeployment.Tags = (CaseInsensitiveDictionary)expectedResult.Tags;
            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData(Contracts.MfeApiVersions.Feb2023Preview)]
        [InlineData(Contracts.MfeApiVersions.Apr2023Preview)]
        [InlineData(Contracts.MfeApiVersions.Jan2024Preview)]
        [InlineData(Contracts.MfeApiVersions.Apr2025)]
        public void TestConvertToCreateComponentDeploymentRequestPublicPreviewSucceeds(Contracts.MfeApiVersions mfeApiVersion)
        {
            // Arrange
            A.CallTo(() => _apiVersionAccessor.MfeApiVersion).Returns(mfeApiVersion);

            CreateComponentDeploymentRequest expectedResult = GetCreateComponentDeploymentRequest();

            BatchDeployment deployment = new BatchDeployment
            {
                Description = expectedResult.Description,
                DeploymentConfiguration = new BatchPipelineComponentDeploymentConfiguration
                {
                    ComponentId = new InternalSharedServices.Contracts.Assets.IdAssetReference
                    {
                        Id = expectedResult.ComponentId
                    },
                    Description = expectedResult.JobSettings.Description,
                    Settings = new Dictionary<string, string>
                    {
                        ["default_datastore"] = "myDatastore",
                        ["default_compute"] = "myCompute",
                        ["force_rerun"] = "true",
                        ["continue_on_step_failure"] = "true"
                    },
                    Tags = new Dictionary<string, string>
                    {
                        ["myTag1.cool"] = "myTagValue1",
                        ["myTag2"] = "myTagValue2"
                    }
                }
            };

            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(deployment);
            trackedResourceBatchDeployment.Location = expectedResult.Location;
            trackedResourceBatchDeployment.Tags = (CaseInsensitiveDictionary)expectedResult.Tags;

            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToCreateComponentDeploymentRequestPublicPreviewEmptyRunSettingsSucceeds()
        {
            // Arrange
            A.CallTo(() => _apiVersionAccessor.MfeApiVersion).Returns(Contracts.MfeApiVersions.Feb2023Preview);

            CreateComponentDeploymentRequest expectedResult = GetCreateComponentDeploymentRequest();
            expectedResult.JobSettings.RunSettings = new RunSettings();

            BatchDeployment deployment = new BatchDeployment
            {
                Description = expectedResult.Description,
                DeploymentConfiguration = new BatchPipelineComponentDeploymentConfiguration
                {
                    ComponentId = new InternalSharedServices.Contracts.Assets.IdAssetReference
                    {
                        Id = expectedResult.ComponentId
                    },
                    Description = expectedResult.JobSettings.Description,
                    Tags = new Dictionary<string, string>
                    {
                        ["myTag1.cool"] = "myTagValue1",
                        ["myTag2"] = "myTagValue2"
                    }
                }
            };

            TrackedResource<BatchDeployment> trackedResourceBatchDeployment = GetTrackedResource(deployment);
            trackedResourceBatchDeployment.Location = expectedResult.Location;
            trackedResourceBatchDeployment.Tags = (CaseInsensitiveDictionary)expectedResult.Tags;

            // Act
            CreateDeploymentRequestBase result = _target.ConvertToCreateRequest(trackedResourceBatchDeployment);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToResourceArmDtoComponentDeploymentPrivatePreviewSuccess()
        {
            // Arrange
            ComponentDeploymentResponse componentResponse = (ComponentDeploymentResponse)GetComponentDeploymentResponse();

            TrackedResource<BatchDeployment> expectedResult = new TrackedResource<BatchDeployment>
            {
                Id = "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/wsName/providers/Microsoft.MachineLearningServices/workspaces/rgName/batchEndpoints/endpointName/deployments/deploymentName",
                Location = componentResponse.Location,
                Name = componentResponse.Name,
                Properties = new BatchDeployment
                {
                    DeploymentConfiguration = new BatchPipelineComponentDeploymentConfiguration(),
                    Description = componentResponse.Description,
                    Properties = componentResponse.Properties,
                    ProvisioningState = InternalDeploymentProvisioningState.Succeeded
                },
                Tags = (CaseInsensitiveDictionary)componentResponse.Tags,
                Type = "Microsoft.MachineLearningServices/workspaces/batchEndpoints/deployments"
            };

            expectedResult.Properties.ProvisioningState = InternalDeploymentProvisioningState.Succeeded;

            // Act
            TrackedResource<BatchDeployment> result = _target.ConvertToResourceArmDto(
                _testWorkspace,
                componentResponse);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToResourceArmDtoComponentDeploymentPublicPreviewSuccess()
        {
            // Arrange
            ComponentDeploymentResponse componentResponse = (ComponentDeploymentResponse)GetComponentDeploymentResponse();

            TrackedResource<BatchDeployment> expectedResult = new TrackedResource<BatchDeployment>
            {
                Id = "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/wsName/providers/Microsoft.MachineLearningServices/workspaces/rgName/batchEndpoints/endpointName/deployments/deploymentName",
                Location = componentResponse.Location,
                Name = componentResponse.Name,
                Properties = new BatchDeployment
                {
                    DeploymentConfiguration = new BatchPipelineComponentDeploymentConfiguration()
                    {
                        ComponentId = new InternalSharedServices.Contracts.Assets.IdAssetReference
                        {
                            Id = componentResponse.ComponentId
                        },
                        Description = componentResponse.Description,
                        Settings = new Dictionary<string, string>
                        {
                            ["continue_on_step_failure"] = "true"
                        },
                        Tags = componentResponse.Tags
                    },
                    Description = componentResponse.Description,
                    Properties = componentResponse.Properties,
                    ProvisioningState = InternalDeploymentProvisioningState.Succeeded
                },
                Tags = (CaseInsensitiveDictionary)componentResponse.Tags,
                Type = "Microsoft.MachineLearningServices/workspaces/batchEndpoints/deployments"
            };

            expectedResult.Properties.ProvisioningState = InternalDeploymentProvisioningState.Succeeded;

            // Act
            TrackedResource<BatchDeployment> result = _target.ConvertToResourceArmDto(
                _testWorkspace,
                componentResponse);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData(true, true)]
        [InlineData(false, false)]
        public void TestConvertModelDeploymentResponseToResourceArmDtoSuccess(bool isCustomModel, bool isModelIdAssetReference)
        {
            // Arrange
            string expectedOperationUri = "https://www.microsoft.com/golden-armadillo";
            A.CallTo(() => _armContextAccessor.GetOperationsStatusUri(A<WorkspaceContext2>._, A<string>._, A<string>._))
                .Returns(new Uri(expectedOperationUri));

            DeploymentResponseBase deploymentResponse = GetModelDeploymentResponse(isCustomModel, isModelIdAssetReference);

            TrackedResource<BatchDeployment> expectedResult = GetTrackedResource<BatchDeployment>(
                GetBatchDeployment(isCustomModel, isModelIdAssetReference));
            expectedResult.Properties.ProvisioningState = InternalSharedServices.Contracts.Endpoints.OnlineEndpointDeployment.DeploymentProvisioningState.Succeeded;
            expectedResult.Properties.Properties[MfeConstants.AzureAsyncOperationUriProperty] = expectedOperationUri;

            // Act
            TrackedResource<BatchDeployment> result = _target.ConvertToResourceArmDto(
                _testWorkspace,
                deploymentResponse);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public void TestConvertToUpdateRequestSuccess()
        {
            // Arrange
            PartialTrackedResource<PartialBatchDeployment> partialBatchDeployment = new ()
            {
                Properties = new PartialBatchDeployment
                {
                    Description = "Updated description"
                },
                Tags = new CaseInsensitiveDictionary() { ["newTagKey"] = "value" },
            };

            UpdateDeploymentRequest expectedResult = new UpdateDeploymentRequest
            {
                Description = "Updated description",
                Tags = new Dictionary<string, string> { ["newTagKey"] = "value" }
            };

            // Act
            UpdateDeploymentRequest result = _target.ConvertToUpdateRequest(partialBatchDeployment);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Theory]
        [InlineData("Cancelled")]
        [InlineData("Failed")]
        [InlineData("NotStarted")]
        [InlineData("Running")]
        [InlineData("Succeeded")]
        [InlineData("Unknown")]
        public void TestConvertToDeploymentProvisioningStateSuccess(string operationStatus)
        {
            InternalDeploymentProvisioningState result = _target.ConvertToDeploymentProvisioningState(operationStatus);
            result.Should().BeOneOf(
                InternalDeploymentProvisioningState.Canceled,
                InternalDeploymentProvisioningState.Failed,
                InternalDeploymentProvisioningState.Succeeded,
                InternalDeploymentProvisioningState.Updating);
        }

        [Fact]
        public void TestConvertToDeploymentProvisioningStateInvalid()
        {
            Func<InternalDeploymentProvisioningState> act = () => _target.ConvertToDeploymentProvisioningState("invalid");

            act.Should()
                .Throw<Exception>()
                .WithMessage("Unexpected operation status of 'invalid'.");
        }

        #region Helper
        private static BatchDeployment GetBatchDeployment(
            bool isCustomModel = true,
            bool isModelIdAssetReference = true)
            => new BatchDeployment
            {
                CodeConfiguration = isCustomModel
                ? new InternalSharedServices.Contracts.CodeConfiguration
                {
                    CodeId = "codeId",
                    ScoringScript = "score.py",
                }
                : null,
                Compute = new ComputeConfiguration
                {
                    InstanceCount = 4,
                    Target = "computeId",
                },
                Description = "test deployment",
                EnvironmentId = isCustomModel ? "environmentId" : null,
                EnvironmentVariables = new Dictionary<string, string> { ["env"] = "var" },
                ErrorThreshold = 4,
                LoggingLevel = BatchLoggingLevel.Warning,
                MaxConcurrencyPerInstance = 4,
                Model = isModelIdAssetReference
                ? new InternalSharedServices.Contracts.Assets.IdAssetReference
                {
                    Id = "modelId",
                }
                : new InternalSharedServices.Contracts.Assets.OutputPathAssetReference
                {
                    JobId = "jobId",
                    Path = "path",
                },
                MiniBatchSize = 10,
                OutputConfiguration = new BatchOutputConfiguration
                {
                    AppendRowFileName = "file.csv",
                    OutputAction = BatchOutputAction.SummaryOnly
                },
                PartitionKeys = new List<string> { "paritionKey" },
                Properties = new Dictionary<string, string> { ["key"] = "value" },
                RetrySettings = new BatchRetrySettings
                {
                    MaxRetries = 1,
                    Timeout = TimeSpan.FromSeconds(1)
                },
            };

        private static CreateModelDeploymentRequest GetCreateDeploymentRequest(
            bool isCustomModel = true,
            bool isModelIdAssetReference = true)
        {
            CreateModelDeploymentRequest deploymentRequest = new CreateModelDeploymentRequest();

            deploymentRequest.Description = "test deployment";
            deploymentRequest.ComputeConfiguration = new AmlComputeConfiguration
            {
                ComputeId = "computeId",
                InstanceCount = 4,
            };
            deploymentRequest.EnvironmentVariables = new Dictionary<string, string> { ["env"] = "var" };
            deploymentRequest.Location = "westus2";
            deploymentRequest.ModelConfiguration = isCustomModel
                ? new CustomModelConfiguration
                {
                    CodeConfiguration = new Endpoints.Batch.Contracts.ControlPlane.V1.CodeConfiguration
                    {
                        CodeId = "codeId",
                        ScoringScript = "score.py",
                    },
                    EnvironmentId = "environmentId",
                    ModelReference = GetModelReference(isModelIdAssetReference)
                }
                : new MLFlowModelConfiguration
                {
                    ModelReference = GetModelReference(isModelIdAssetReference),
                };
            deploymentRequest.OutputConfiguration = new OutputConfiguration
            {
                OutputAction = OutputAction.SummaryOnly,
                OutputFileName = "file.csv",
            };
            deploymentRequest.ProcessConfiguration = new ProcessConfiguration
            {
                ErrorThreshold = 4,
                LoggingLevel = LoggingLevel.Warning,
                MaxConcurrencyPerInstance = 4,
                MiniBatchSize = 10,
                PartitionKeys = new List<string> { "paritionKey" },
            };
            deploymentRequest.Properties = new Dictionary<string, string> { ["key"] = "value" };
            deploymentRequest.RetryConfiguration = new RetryConfiguration
            {
                RetryCount = 1,
                RetryInterval = TimeSpan.FromSeconds(1),
            };
            deploymentRequest.Tags = new CaseInsensitiveDictionary()
            {
                ["tag1"] = "value1",
                ["TAG2"] = "value2",
            };

            return deploymentRequest;
        }

        private static DeploymentResponseBase GetModelDeploymentResponse(
            bool isCustomModel = true,
            bool isModelIdAssetReference = true)
        {
            ModelDeploymentResponse deploymentResponse = new ModelDeploymentResponse
            {
                EndpointName = "endpointName",
                Name = "deploymentName",
                ProvisioningState = DeploymentProvisioningState.Succeeded,
                SystemData = new Endpoints.Batch.Contracts.SystemData
                {
                    CreatedTime = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromMinutes(1)),
                    CreatedBy = "user-123",
                    LastModifiedTime = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromMinutes(1)),
                }
            };

            deploymentResponse.Description = "test deployment";
            deploymentResponse.ComputeConfiguration = new AmlComputeConfiguration
            {
                ComputeId = "computeId",
                InstanceCount = 4,
            };
            deploymentResponse.EnvironmentVariables = new Dictionary<string, string> { ["env"] = "var" };
            deploymentResponse.Location = "westus2";
            deploymentResponse.ModelConfiguration = isCustomModel
                ? new CustomModelConfiguration
                {
                    CodeConfiguration = new Endpoints.Batch.Contracts.ControlPlane.V1.CodeConfiguration
                    {
                        CodeId = "codeId",
                        ScoringScript = "score.py",
                    },
                    EnvironmentId = "environmentId",
                    ModelReference = GetModelReference(isModelIdAssetReference)
                }
                : new MLFlowModelConfiguration
                {
                    ModelReference = GetModelReference(isModelIdAssetReference),
                };
            deploymentResponse.OutputConfiguration = new OutputConfiguration
            {
                OutputAction = OutputAction.SummaryOnly,
                OutputFileName = "file.csv",
            };
            deploymentResponse.PartitionKeys = new List<string> { "paritionKey" };
            deploymentResponse.ProcessConfiguration = new ProcessConfiguration
            {
                ErrorThreshold = 4,
                LoggingLevel = LoggingLevel.Warning,
                MaxConcurrencyPerInstance = 4,
                MiniBatchSize = 10,
            };
            deploymentResponse.Properties = new Dictionary<string, string>
            {
                ["key"] = "value",
                ["AzureAsyncOperationUri"] = Guid.NewGuid().ToString(),
            };
            deploymentResponse.RetryConfiguration = new RetryConfiguration
            {
                RetryCount = 1,
                RetryInterval = TimeSpan.FromSeconds(1),
            };
            deploymentResponse.Tags = new CaseInsensitiveDictionary()
            {
                ["tag1"] = "value1",
                ["TAG2"] = "value2",
            };

            return deploymentResponse;
        }

        private static DeploymentResponseBase GetComponentDeploymentResponse()
            => new ComponentDeploymentResponse
            {
                EndpointName = "endpointName",
                Name = "deploymentName",
                ProvisioningState = DeploymentProvisioningState.Succeeded,
                Description = "test deployment",
                Location = "westus2",
                Properties = new Dictionary<string, string>
                {
                    { "ComponentDeployment.ComponentId", "mycomponentid" },
                    { "ComponentDeployment.Description", "my component" },
                    { "ComponentDeployment.Settings.continue_on_step_failure", "true" }
                },
                Tags = new CaseInsensitiveDictionary()
                {
                    ["tag1"] = "value1",
                    ["TAG2"] = "value2",
                },
                ComponentId = "mycomponentid",
                JobSettings = new JobSettings
                {
                    Description = "my component",
                    JobType = Endpoints.Batch.Contracts.DataPlane.V1.JobType.Pipeline,
                    RunSettings = new RunSettings
                    {
                        continue_on_step_failure = true
                    }
                }
            };

        private static AssetReferenceBase GetModelReference(bool isModelIdAssetReference)
            => isModelIdAssetReference
                ? new IdAssetReference { AssetId = "modelId" }
                : new OutputPathAssetReference { JobId = "jobId", Path = "path" };

        private TrackedResource<T> GetTrackedResource<T>(T input)
            => new TrackedResource<T>
            {
                Id = MfeResourceArmScope.ToString(
                    _testWorkspace,
                    MfeConstants.ArmTypeBatchEndpoint,
                    "endpointName",
                    MfeConstants.ArmTypeBatchEndpointDeployment,
                    "deploymentName"),
                Name = "deploymentName",
                Location = "westus2",
                Properties = input,
                SystemData = new SystemData
                {
                    CreatedAt = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromMinutes(1)),
                    CreatedBy = "user-123",
                    LastModifiedAt = new DateTimeOffset(2022, 5, 1, 8, 6, 32, TimeSpan.FromMinutes(1)),
                },
                Tags = new CaseInsensitiveDictionary()
                {
                    ["tag1"] = "value1",
                    ["TAG2"] = "value2",
                },
                Type = MfeConstants.ArmFullTypeBatchDeployment
            };

        private CreateComponentDeploymentRequest GetCreateComponentDeploymentRequest()
            => new CreateComponentDeploymentRequest
            {
                ComponentId = "azureml:my_component:1",
                JobSettings = new JobSettings
                {
                    Description = "My job description",
                    RunSettings = new RunSettings
                    {
                        default_datastore = "myDatastore",
                        default_compute = "myCompute",
                        continue_on_step_failure = true,
                        force_rerun = true
                    },
                    JobType = Endpoints.Batch.Contracts.DataPlane.V1.JobType.Pipeline,
                    Tags = new Dictionary<string, string>
                    {
                        { "myTag1.cool", "myTagValue1" },
                        { "myTag2", "myTagValue2" },
                    }
                },
                Location = "centraluseuap",
                Tags = new CaseInsensitiveDictionary
                {
                    { "Tag1", "value1" },
                    { "Tag2", "value2" },
                },
                Description = "My Description"
            };

        #endregion
    }
}
