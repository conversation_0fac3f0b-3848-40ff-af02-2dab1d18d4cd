﻿// <copyright file="MfeTestContractResolver.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Reflection;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.Attributes;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.CustomSerializers;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests
{
    public static class MfeTestContractResolverExtensions
    {
        /// <summary>
        /// Extension method for setting up a JSON serializer to be used by the ManagementFrontEndClient.
        /// </summary>
        public static JsonSerializerSettings UseMfeTestContractResolver(this JsonSerializerSettings serializerSettings)
        {
            serializerSettings.ContractResolver = new MfeTestContractResolver();
            return serializerSettings;
        }
    }

    public class MfeTestContractResolver : DefaultContractResolver
    {
        private readonly CaseRandomizerConverter _caseRandomizerConverter;
        // TODO: Make this a hash set?
        private readonly List<PropertyInfo> _propertiesToExcludeFromSerialization;

        public MfeTestContractResolver()
        {
            _caseRandomizerConverter = new CaseRandomizerConverter();
            _propertiesToExcludeFromSerialization = new List<PropertyInfo>();
        }

        public List<PropertyInfo> PropertiesToExcludeFromSerialization
        {
            get { return _propertiesToExcludeFromSerialization; }
        }

        protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
        {
            var properties = base.CreateProperties(type, memberSerialization);

            foreach (var property in properties)
            {
                // Assigns a custom converter to request properties marked [CaseInsensitive].
                // This sort of thing would normally be done using a [JsonConverter] attribute on the property,
                // but that would apply the case-randomization in all cases, even in production or if
                // the contracts were used by some other service. Doing it like this means the custom converter
                // is only used for the MFE integration tests.
                if (property.Converter == null && property.PropertyType == typeof(string)
                    && property.DeclaringType.GetProperty(property.UnderlyingName).GetCustomAttribute(typeof(CaseInsensitiveAttribute)) != null)
                {
                    property.Converter = _caseRandomizerConverter;
                }
            }

            return properties;
        }

        protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
        {
            var jsonProperty = base.CreateProperty(member, memberSerialization);

            if (member is PropertyInfo propertyInfo)
            {
                // Keep MFE service honest best failing loudly on improper null value presence
                ContractResolverUtilities.ConfigureXNullableDeserialization(jsonProperty, propertyInfo);

                ContractResolverUtilities.ConfigureXNullableSerialization(jsonProperty, propertyInfo);
            }

            // Skip serialization of this property if it's in the exclude list
            var baseShouldSerialize = jsonProperty.ShouldSerialize;
            jsonProperty.ShouldSerialize = (obj) =>
            {
                foreach (var propertyInfo in _propertiesToExcludeFromSerialization)
                {
                    if (member.ReflectedType == propertyInfo.ReflectedType
                        && member.Name == propertyInfo.Name)
                    {
                        return false;
                    }
                }

                // Fall back on the base implementation of ShouldSerialize, if one exists
                return baseShouldSerialize != null ? baseShouldSerialize(obj) : true;
            };

            return jsonProperty;
        }
    }
}
