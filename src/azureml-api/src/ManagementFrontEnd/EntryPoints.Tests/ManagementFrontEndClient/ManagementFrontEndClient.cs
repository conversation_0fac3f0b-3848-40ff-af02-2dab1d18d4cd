﻿// <copyright file="ManagementFrontEndClient.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using System.Web;
using System.Xml;
using Common.Core.Contracts;
using FluentAssertions;
using Microsoft.MachineLearning.Common.Core;
using Microsoft.MachineLearning.Common.WebApi.Client;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.CustomSerializers;
using Microsoft.MachineLearning.ManagementFrontEnd.InternalSharedServices.Contracts.Common;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests
{
    public class ManagementFrontEndClient
    {
        private readonly Guid _subscriptionId;
        private readonly string _resourceGroupName;
        private readonly string _workspaceName;
        private readonly HttpClient _httpClient;
        private readonly HttpClient _s2sClient;

        private readonly IAsyncPolicy _retryPolicy;
        private readonly JsonSerializerSettings _serializerSettings;

        public ManagementFrontEndClient(
            HttpClient httpClient,
            Guid subscriptionId,
            string resourceGroupName,
            string workspaceName,
            HttpClient s2sClient)
        {
            _httpClient = httpClient;
            _s2sClient = s2sClient;
            _subscriptionId = subscriptionId;
            _resourceGroupName = resourceGroupName;
            _workspaceName = workspaceName;

            _retryPolicy = Policy
                .Handle<MfeResponseValidationException>(e => CanRetry(e.Response, null))
                .Or<ServiceInvocationException>(e => CanRetry(e.Response, e.ErrorResponse))
                .Or<ServiceTimeoutException>()
                .WaitAndRetryAsync(3, i => TimeSpan.FromSeconds(5));

            _serializerSettings = new JsonSerializerSettings()
                .AddAllCustomSerializerSettings()
                .UseMfeTestContractResolver();
        }

        /// <summary>
        /// The serializer settings used for calls to the ManagementFrontEnd client.
        /// </summary>
        public JsonSerializerSettings SerializerSettings
        {
            get
            {
                return _serializerSettings!;
            }
        }

        /// <summary>
        /// Gets a service invoker client for a controller.
        /// </summary>
        public TController GetController<TController>(Func<HttpRequestMessage, Task> beforeRequest = null, Action<HttpResponseMessage> afterResponse = null, bool randomizeCase = true)
        {
            return (TController)GetController(typeof(TController), beforeRequest, afterResponse, randomizeCase: randomizeCase);
        }

        public TController GetControllerNoSerializerSettings<TController>(Func<HttpRequestMessage, Task> beforeRequest = null, Action<HttpResponseMessage> afterResponse = null, bool randomizeCase = true)
        {
            return (TController)GetController(typeof(TController), null, beforeRequest, afterResponse, randomizeCase: randomizeCase);
        }

        public object GetController(Type controllerType, Func<HttpRequestMessage, Task> beforeRequest = null, Action<HttpResponseMessage> afterResponse = null, bool randomizeCase = true)
        {
            return GetController(controllerType, _serializerSettings, beforeRequest, afterResponse, randomizeCase);
        }

        public object GetController(Type controllerType, JsonSerializerSettings serializerSettings, Func<HttpRequestMessage, Task> beforeRequest = null, Action<HttpResponseMessage> afterResponse = null, bool randomizeCase = true)
        {
            return ServiceInvoker.Create(
                controllerType,
                _httpClient,
                _httpClient.BaseAddress,
                requestTimeout: Debugger.IsAttached ? TimeSpan.FromMinutes(60) : TimeSpan.FromMinutes(5),
                beforeRequestAsync: async (request, method) =>
                {
                    if (beforeRequest != null)
                    {
                        await beforeRequest(request).ConfigureAwait(false);
                    }

                    if (randomizeCase)
                    {
                        CaseRandomizer.RandomizeCase(request);
                    }
                },
                afterResponse: (response, method) =>
                {
#pragma warning disable VSTHRD002 // Avoid problematic synchronous waits
                    ValidateServiceResponse(response).GetAwaiter().GetResult();
#pragma warning restore VSTHRD002 // Avoid problematic synchronous waits
                    afterResponse?.Invoke(response);
                },
                retryPolicy: _retryPolicy,
                serializerSettings: serializerSettings);
        }

        private static async Task ValidateServiceResponse(HttpResponseMessage response)
        {
            var strApiVersion = HttpUtility.ParseQueryString(response.RequestMessage.RequestUri.Query).Get(MfeConstants.ApiVersionParamName);
            var mfeApiVersion = ApiVersions.Latest();
            try
            {
                mfeApiVersion = ApiVersions.ParseAsMfeApiVersion(strApiVersion);
            }
            catch (SerializationException)
            { // Default unknown api versions to latest test suite
            }

            // Make sure all error responses contain the common error info.
            if (!response.IsSuccessStatusCode)
            {
                var body = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                body.Should().NotBeNullOrEmpty("response body should not be empty with status code {0}", response.StatusCode);

                try
                {
                    if (mfeApiVersion != MfeApiVersions.Oct2021DataplanePreview)
                    {
                        if (mfeApiVersion >= MfeApiVersions.Mar2021Preview)
                        {
                            ValidateBodyAsArmErrorResponseV2(body);
                        }
                        else
                        {
                            ValidateBodyAsErrorResponse(body);
                        }
                    }
                }
                catch (Exception e)
                {
                    throw new MfeResponseValidationException($"Validation failed for response with status code {response.StatusCode} and body {body}.", e, response);
                }
            }

            // Check async operation responses.
            // Skip checking no api-version requests. E.g. LRO may return Accepted with no Location.
            if (strApiVersion != null && response.StatusCode == HttpStatusCode.Accepted)
            {
                response.Headers.Location.Should().NotBeNull("202 Accepted responses must include a Location header");
            }

            if (await IsAsyncResponse(response).ConfigureAwait(false))
            {
                response.Headers.TryGetValues("x-ms-async-operation-timeout", out var timeoutHeaders).Should().BeTrue("asynchronous create and delete operations should return the async timeout header (use the Accepted() and Created() overloads inherited from ResourceControllerBase)");

                XmlConvert.ToTimeSpan(timeoutHeaders.Single()).Should()
                    .BeGreaterOrEqualTo(TimeSpan.FromMinutes(1))
                    .And
                    .BeLessOrEqualTo(response.RequestMessage.Method == HttpMethod.Delete ? TimeSpan.FromHours(1) : TimeSpan.FromDays(6));

                if (response.StatusCode == HttpStatusCode.Created)
                {
                    if (mfeApiVersion >= MfeApiVersions.Mar2021Preview && response.Headers.Location != null)
                    {
                        response.Headers.Location.AbsolutePath.Should().BeEquivalentTo(response.RequestMessage.RequestUri.AbsolutePath, "the Location header on asynchronous CreateOrUpdates MUST point to the resource itself");
                    }

                    response.Headers.TryGetValues("Azure-AsyncOperation", out var asyncOperationHeaders).Should().BeTrue("asynchronous CreateOrUpdate responses MUST respond with an Azure-AsyncOperation header");
                    asyncOperationHeaders.Single().Should().MatchRegex("/(locations|registries)/[^/]+/mfeOperationsStatus/", "the Azure-AsyncOperation header MUST point to the top-level OperationsStatus polling uri");
                }
            }
        }

        private static bool CanRetry(HttpResponseMessage httpResponse, ErrorResponse errorResponse)
        {
            // 503 (GatewayTimeout) will almost always be a (hopefully transient) issue with a backend service.
            return httpResponse?.StatusCode == HttpStatusCode.GatewayTimeout
                || errorResponse?.Error?.Message == "Received 503 from a service request";
        }

        private static void ValidateBodyAsArmErrorResponseV2(string body)
        {
            var errorResponse = JsonConvert.DeserializeObject<ArmErrorResponseV2>(body, new JsonSerializerSettings() { MissingMemberHandling = MissingMemberHandling.Error });
            errorResponse.Should().NotBeNull("response body must deserialize as ArmErrorResponseV2 object");

            var debugInfo = errorResponse.Error?.GetAdditionalInfo<DebugInfoResponse>("DebugInfo").FirstOrDefault();

            if (debugInfo?.Type == typeof(ServiceInvocationException).FullName)
            {
                // Allow passthrough of other services' errors (so we're not testing their error handling).
                debugInfo.Message.Should().StartWith("Service invocation failed!");
            }
            else
            {
                // MFE-sourced errors must conform.
                errorResponse.Error.Should().NotBeNull();
                errorResponse.Error.Code.Should().BeOneOf("SystemError", "UserError");

                var innerError = errorResponse.Error.GetAdditionalInfo<InnerErrorResponse>("InnerError").FirstOrDefault();
                innerError.Should().NotBeNull();
                innerError.Code.Should().NotBeNullOrWhiteSpace();

                // messageFormat is string type
                var messageFormat = errorResponse.Error.AdditionalInfo.Where(i => i.Type == "MessageFormat").Single().Info["value"].ToString();
                messageFormat.Should().NotBeNullOrWhiteSpace();
            }
        }

        private static void ValidateBodyAsErrorResponse(string body)
        {
            var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(body);
            errorResponse.Should().NotBeNull("response body must deserialize as ErrorResponse object");

            if (errorResponse.Error?.DebugInfo?.Type == typeof(ServiceInvocationException).FullName)
            {
                // Allow passthrough of other services' errors (so we're not testing their error handling).
                errorResponse.Error.DebugInfo.Message.Should().StartWith("Service invocation failed!");
            }
            else
            {
                // MFE-sourced errors must conform.
                errorResponse.Error.Should().NotBeNull();
                errorResponse.Error.Code.Should().BeOneOf("SystemError", "UserError");
                errorResponse.Error.InnerError.Should().NotBeNull();
                errorResponse.Error.InnerError.Code.Should().NotBeNullOrWhiteSpace();
                errorResponse.Error.MessageFormat.Should().NotBeNullOrWhiteSpace();
            }
        }

        private static async Task<bool> IsAsyncResponse(HttpResponseMessage response)
        {
            if (!MfeTestingConstants.AsyncTimeoutMethods.Contains(response.RequestMessage.Method))
            {
                return false;
            }

            if (response.StatusCode == HttpStatusCode.Accepted || response.Headers.TryGetValues(MfeConstants.AzureAsyncOperation, out var _))
            {
                return true;
            }

            if (response.StatusCode == HttpStatusCode.Created)
            {
                var body = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                var bodyJson = JToken.Parse(body);
                var provisioningState = bodyJson["properties"]["provisioningState"]?.Value<string>();
                return provisioningState != null && !MfeTestingConstants.TerminalProvisoningStates.Contains(provisioningState);
            }

            return false;
        }
    }

    public class MfeResponseValidationException : Exception
    {
        public MfeResponseValidationException()
        {
        }

        public MfeResponseValidationException(string message)
            : base(message)
        {
        }

        public MfeResponseValidationException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        public MfeResponseValidationException(string message, Exception innerException, HttpResponseMessage response)
            : base(message, innerException)
        {
            Response = response;
        }

        public HttpResponseMessage Response { get; }
    }
}
