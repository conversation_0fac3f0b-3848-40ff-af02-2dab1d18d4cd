﻿// <copyright file="FluentAssertionsExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using FluentAssertions;
using FluentAssertions.Equivalency;
using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using Microsoft.MachineLearning.ManagementFrontEnd.Contracts.Attributes;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions
{
    public static class FluentAssertionsExtensions
    {
        /// <summary>
        /// Simplified variant of StringAssertions.StartWith() from https://github.com/fluentassertions/fluentassertions/blob/4.19.0/Src/Core/Primitives/StringAssertions.cs
        /// </summary>
        public static AndConstraint<StringAssertions> StartWith(this StringAssertions assertions, string expected, StringComparison comparison, string because = "", params object[] becauseArgs)
        {
            if (string.IsNullOrEmpty(expected))
            {
                throw new ArgumentException("Cannot compare start of string with <null> or empty string.", nameof(expected));
            }

            if (assertions.Subject == null || !assertions.Subject.StartsWith(expected, comparison))
            {
                Execute.Assertion.BecauseOf(because, becauseArgs)
                    .FailWith($"Expected {{context:string}} to start with {{0}} according to {comparison}{{reason}}, but {{1}} differs near {IndexOfFirstMismatch(assertions.Subject, expected, comparison)}.");
            }

            return new AndConstraint<StringAssertions>(assertions);
        }

        /// <summary>
        /// Fluent extension method for doing a case-insensitive comparison for the right contract properties.
        /// </summary>
        public static EquivalencyAssertionOptions<TSelf> UsingCaseInsensitiveProperties<TSelf>(this EquivalencyAssertionOptions<TSelf> options)
        {
            return options.Using(new CaseInsensitivePropertiesEquivalencyStep());
        }

        /// <summary>
        /// Fluent extension method for doing a case-insensitive comparison for the values of generic dictionaries with a String value.
        /// </summary>
        public static EquivalencyAssertionOptions<TSelf> UsingCaseInsensitiveDictionaryValues<TSelf>(this EquivalencyAssertionOptions<TSelf> options)
        {
            return options.Using(new CaseInsensitiveDictionaryValuesEquivalencyStep());
        }

        // Borrowed from the https://github.com/fluentassertions/fluentassertions/blob/834a2db00a60e2626655a28a129c084831b85880/Src/FluentAssertions/Common/StringExtensions.cs#L13-L32
        private static int IndexOfFirstMismatch(string value, string expected, StringComparison stringComparison)
        {
            Func<char, char, bool> comparer = GetCharComparer(stringComparison);

            for (int index = 0; index < value.Length; index++)
            {
                if ((index >= expected.Length) || !comparer(value[index], expected[index]))
                {
                    return index;
                }
            }

            return -1;
        }

        private static Func<char, char, bool> GetCharComparer(StringComparison stringComparison) =>
            stringComparison == StringComparison.Ordinal
                ? ((x, y) => x == y)
                : (x, y) => char.ToUpperInvariant(x) == char.ToUpperInvariant(y);

        /// <summary>
        /// Custom comparison class for FluentAssertions, checking for the [CaseInsensitive] attribute and acting accordingly.
        /// </summary>
        private class CaseInsensitivePropertiesEquivalencyStep : IEquivalencyStep
        {
            public EquivalencyResult Handle(Comparands comparands, IEquivalencyValidationContext context, IEquivalencyValidator nestedValidator)
            {
                if (context.CurrentNode is Property property && property.Type == typeof(string) &&
                    comparands.Subject is string &&
                    property.DeclaringType.GetProperty(property.Name)!.CustomAttributes.Any(a => a.AttributeType == typeof(CaseInsensitiveAttribute)))
                {
                    if (string.Equals((string)comparands.Subject, (string)comparands.Expectation, StringComparison.OrdinalIgnoreCase))
                    {
                        return EquivalencyResult.AssertionCompleted;
                    }
                }

                return EquivalencyResult.ContinueWithNext;
            }
        }

        /// <summary>
        /// Custom comparison class for FluentAssertions, checking for Dictionaries{T,string} and equating them Case-Insensitively.
        /// </summary>
        private class CaseInsensitiveDictionaryValuesEquivalencyStep : IEquivalencyStep
        {
            public EquivalencyResult Handle(Comparands comparands, IEquivalencyValidationContext context, IEquivalencyValidator nestedValidator)
            {
                IDictionary subjectDictionary = comparands.Subject as IDictionary;
                IDictionary expectationDictionary = comparands.Expectation as IDictionary;

                if (subjectDictionary == null || expectationDictionary == null ||
                    subjectDictionary.Count != expectationDictionary.Count)
                {
                    return EquivalencyResult.ContinueWithNext;
                }

                Type subjectType = subjectDictionary.GetType();
                Type expectationType = expectationDictionary.GetType();

                if (subjectType.IsGenericType &&
                    (subjectType.GetGenericTypeDefinition() == typeof(Dictionary<,>) ||
                     subjectType.GetGenericTypeDefinition() == typeof(IDictionary<,>))
                    &&
                    expectationType.IsGenericType &&
                    (expectationType.GetGenericTypeDefinition() == typeof(Dictionary<,>) ||
                     expectationType.GetGenericTypeDefinition() == typeof(IDictionary<,>)))
                {
                    Type subjectValueType = subjectType.GetGenericArguments()[1];
                    Type expectationValueType = expectationType.GetGenericArguments()[1];

                    if (subjectValueType != typeof(string) ||
                        expectationValueType != typeof(string))
                    {
                        return EquivalencyResult.ContinueWithNext;
                    }

                    foreach (DictionaryEntry subjectEntry in subjectDictionary)
                    {
                        if (!expectationDictionary.Contains(subjectEntry.Key))
                        {
                            return EquivalencyResult.ContinueWithNext;
                        }

                        string expectationString = (string)expectationDictionary[subjectEntry.Key];

                        if (!string.Equals((string)subjectEntry.Value, expectationString, StringComparison.OrdinalIgnoreCase))
                        {
                            return EquivalencyResult.ContinueWithNext;
                        }
                    }
                }

                return EquivalencyResult.AssertionCompleted;
            }
        }
    }
}
