﻿// <copyright file="TypeExtensions.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.Extensions
{
    public static class TypeExtensions
    {
        public static IEnumerable<Type> GetTypeHierarchy(this Type type)
        {
            do
            {
                yield return type;
                type = type.BaseType;
            }
            while (type != null);
        }
    }
}
