﻿// <copyright file="EnumExtensionsTests.cs" company="Microsoft">
// Copyright (c) Microsoft. All rights reserved.
// </copyright>

using System.Runtime.Serialization;
using Microsoft.MachineLearning.Common.Testing;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Api;
using Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests.XunitExtensions;
using Microsoft.MachineLearning.ManagementFrontEnd.Services.Extensions;
using Microsoft.MachineLearning.RegistryManagement.Contracts.ArmCommon;
using Xunit;

namespace Microsoft.MachineLearning.ManagementFrontEnd.EntryPoints.Tests
{
    [MfeTest(TestCategory.Unit, ScenarioScope.MfeCommon)]
    public class EnumExtensionsTests
    {
        [Theory]
        [InlineData("systemAssigned", ManagedServiceIdentityType.SystemAssigned)]
        [InlineData("SYSTEMASSIGNED", ManagedServiceIdentityType.SystemAssigned)]
        [InlineData("SystemAssigned", ManagedServiceIdentityType.SystemAssigned)]
        public void ParseAsEnumUsingMemberIgnoreCaseSucceeds(string enumValue, ManagedServiceIdentityType expectedType)
        {
            Assert.Equal(expectedType, enumValue.ParseAsEnumUsingMember<ManagedServiceIdentityType>(true));
        }

        [Theory]
        [InlineData(" systemAssigned ", ManagedServiceIdentityType.SystemAssigned)]
        [InlineData(" SystemAssigned", ManagedServiceIdentityType.SystemAssigned)]
        [InlineData("SystemAssigned ", ManagedServiceIdentityType.SystemAssigned)]
        public void ParseAsEnumUsingMemberIgnoreWhitespaceSucceeds(string enumValue, ManagedServiceIdentityType expectedType)
        {
            Assert.Equal(expectedType, enumValue.ParseAsEnumUsingMember<ManagedServiceIdentityType>(true));
        }

        [Fact]
        public void ParseAsEnumUsingMemberSucceeds()
        {
            string msiType = "SystemAssigned";
            Assert.Equal(ManagedServiceIdentityType.SystemAssigned, msiType.ParseAsEnumUsingMember<ManagedServiceIdentityType>(false));
        }

        [Theory]
        [InlineData("systemAssigned ")]
        [InlineData("systemAssigned")]
        public void ParseAsEnumUsingMemberWrongCasingFails(string msiTypeWithWrongCasing)
        {
            Assert.Throws<SerializationException>(() => msiTypeWithWrongCasing.ParseAsEnumUsingMember<ManagedServiceIdentityType>(false));
        }
    }
}
