﻿namespace Microsoft.MachineLearning.AzureResourceManager.ARM.DataFactory
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Net.Http;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.MachineLearning.Net.Http;
    using Newtonsoft.Json;
    using Polly;

    public class DataFactoryClient : IDataFactoryClient
    {
        private const string DataFactoryARMPath = "subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.DataFactory/factories/{2}?api-version={3}";

        private IHttpClientFactory factory;
        private ILogger<IDataFactoryClient> logger;
        private IAsyncPolicy retryPolicy;
        private RmEndpoint fallbackEndpoint;
        private string managementEndpoint;
        private string apiVersion;

        public DataFactoryClient(string managementEndpoint, IHttpClientFactory factory, ILogger<IDataFactoryClient> logger, string apiVersion = "2018-06-01", RmEndpoint fallbackEndpoint = null)
        {
            this.managementEndpoint = Argument.NotNullOrWhitespace(managementEndpoint, nameof(managementEndpoint));
            this.factory = Argument.NotNull(factory, nameof(factory));
            this.logger = Argument.NotNull(logger, nameof(logger));
            this.apiVersion = Argument.NotNullOrWhitespace(apiVersion, nameof(apiVersion));
            this.fallbackEndpoint = fallbackEndpoint;

            this.retryPolicy = Policy.Handle<Exception>(ex => ex.IsHttpTransient())
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: _ => TimeSpan.FromMilliseconds(200));
        }

        public async Task<Factory> GetDataFactoryAsync(
            string subscriptionId,
            string resourceGroupName,
            string resourceName,
            string token,
            CancellationToken cancellationToken)
        {
            Argument.NotNullOrWhitespace(subscriptionId, nameof(subscriptionId));
            Argument.NotNullOrWhitespace(resourceGroupName, nameof(resourceGroupName));
            Argument.NotNullOrWhitespace(resourceName, nameof(resourceName));
            Argument.NotNullOrWhitespace(token, nameof(token));

            var relativePath = string.Format(
                CultureInfo.InvariantCulture,
                DataFactoryARMPath,
                Uri.EscapeDataString(subscriptionId),
                Uri.EscapeDataString(resourceGroupName),
                Uri.EscapeDataString(resourceName),
                Uri.EscapeDataString(this.apiVersion));

            var endpoint = this.FromName(this.managementEndpoint);

            if (endpoint.LocationUri != null)
            {
                try
                {
                    var resp = await this.GetAsync<Factory>(
                        endpoint.LocationUri,
                        relativePath,
                        token,
                        cancellationToken).ConfigureAwait(false);

                    this.logger.LogInformation("Get datafactory to '{locationUri}' endpoint. {responseObject}", endpoint.LocationUri, JsonConvert.SerializeObject(resp));

                    return resp;
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    this.logger.LogError(ex, "Failed to get datafactory to '{locationUri}' endpoint.", endpoint.LocationUri);
                }
            }

            try
            {
                var resp = await this.GetAsync<Factory>(
                    endpoint.EnvironmentUri,
                    relativePath,
                    token,
                    cancellationToken).ConfigureAwait(false);

                this.logger.LogInformation("Get datafactory to '{locationUri}' endpoint. {responseObject}", endpoint.EnvironmentUri, JsonConvert.SerializeObject(resp));

                return resp;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                this.logger.LogError(ex, "Failed to get datafactory  to '{locationUri}' endpoint.", endpoint.EnvironmentUri);

                throw;
            }
        }

        public async Task DeleteDataFactoryAsync(
            string subscriptionId,
            string resourceGroupName,
            string resourceName,
            string token,
            CancellationToken cancellationToken)
        {
            Argument.NotNullOrWhitespace(subscriptionId, nameof(subscriptionId));
            Argument.NotNullOrWhitespace(resourceGroupName, nameof(resourceGroupName));
            Argument.NotNullOrWhitespace(resourceName, nameof(resourceName));
            Argument.NotNullOrWhitespace(token, nameof(token));

            var relativePath = string.Format(
                CultureInfo.InvariantCulture,
                DataFactoryARMPath,
                Uri.EscapeDataString(subscriptionId),
                Uri.EscapeDataString(resourceGroupName),
                Uri.EscapeDataString(resourceName),
                Uri.EscapeDataString(this.apiVersion));

            var endpoint = this.FromName(this.managementEndpoint);

            if (endpoint.LocationUri != null)
            {
                try
                {
                    await this.DeleteAsync(
                        endpoint.LocationUri,
                        relativePath,
                        token,
                        cancellationToken).ConfigureAwait(false);

                    this.logger.LogInformation("Delete datafactory to '{locationUri}' endpoint.", endpoint.LocationUri);

                    return;
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    this.logger.LogError(ex, "Failed to get datafactory to '{locationUri}' endpoint.", endpoint.LocationUri);
                }
            }

            try
            {
                await this.DeleteAsync(
                    endpoint.EnvironmentUri,
                    relativePath,
                    token,
                    cancellationToken).ConfigureAwait(false);

                this.logger.LogInformation("Delete datafactory to '{locationUri}' endpoint.", endpoint.EnvironmentUri);

                return;
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                this.logger.LogError(ex, "Failed to delete datafactory  to '{locationUri}' endpoint.", endpoint.EnvironmentUri);

                throw;
            }
        }

        private Task<T> GetAsync<T>(Uri baseUri, string uriPath, string token, CancellationToken cancellationToken)
        {
            return this.retryPolicy.ExecuteAsync(
                async (_) =>
                {
                    using (var client = this.factory.CreateClient())
                    {
                        using (var request = new HttpRequestMessage(HttpMethod.Get, new Uri(baseUri, uriPath)))
                        {
                            request.Headers.AddBearerAuthorization(token);
                            request.Headers.AddAcceptJsonContentType();

                            using (var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false))
                            {
                                return await response.ReadJsonAsAsync<T>().ConfigureAwait(false);
                            }
                        }
                    }
                },
                cancellationToken);
        }

        private Task DeleteAsync(Uri baseUri, string uriPath, string token, CancellationToken cancellationToken)
        {
            return this.retryPolicy.ExecuteAsync(
                async (_) =>
                {
                    using (var client = this.factory.CreateClient())
                    {
                        using (var request = new HttpRequestMessage(HttpMethod.Delete, new Uri(baseUri, uriPath)))
                        {
                            request.Headers.AddBearerAuthorization(token);
                            request.Headers.AddAcceptJsonContentType();

                            using (var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false))
                            {
                                await response.EnsureSuccessStatusCodeAsync().ConfigureAwait(false);
                            }
                        }
                    }
                },
                cancellationToken);
        }

        private RmEndpoint FromName(string environmentOrLocation)
        {
            var endpoint = RmEndpoint.FromName(environmentOrLocation);

            if (endpoint == null)
            {
                this.logger.LogWarning("Failed to resolve RmEndpoint for '{environmentOrLocation}' environment or location.", environmentOrLocation);

                endpoint = this.fallbackEndpoint;

                if (endpoint == null)
                {
                    throw new ArgumentException($"Resource manager endpoint is not available for the provided '{environmentOrLocation}' environment or location.");
                }
            }

            return endpoint;
        }
    }
}
