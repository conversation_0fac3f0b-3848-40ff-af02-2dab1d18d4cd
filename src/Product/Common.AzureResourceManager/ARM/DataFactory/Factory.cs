﻿namespace Microsoft.MachineLearning.AzureResourceManager.ARM.DataFactory
{
    using System;
    using System.Collections.Generic;
    using Newtonsoft.Json;

    /// <summary>
    /// Factory resource type.
    /// </summary>
    public class Factory
    {
        /// <summary>
        /// Gets or sets unmatched properties from the message are deserialized
        /// this collection.
        /// </summary>
        [JsonExtensionData]
        public IDictionary<string, object> AdditionalProperties
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets managed service identity of the factory.
        /// </summary>
        [JsonProperty(PropertyName = "identity")]
        public FactoryIdentity Identity
        {
            get;
            set;
        }

        [JsonProperty(PropertyName = "properties")]
        public Properties Properties
        {
            get;
            set;
        }
     }

    public class Properties
    {
        /// <summary>
        /// Gets factory provisioning state, example Succeeded.
        /// </summary>
        [JsonProperty(PropertyName = "provisioningState")]
        public string ProvisioningState
        {
            get;
            private set;
        }

        /// <summary>
        /// Gets or sets time the factory was created in ISO8601 format.
        /// </summary>
        [JsonProperty(PropertyName = "createTime")]
        public DateTime? CreateTime
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets version of the factory.
        /// </summary>
        [JsonProperty(PropertyName = "version")]
        public string Version
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets git repo information of the factory.
        /// </summary>
        [JsonProperty(PropertyName = "repoConfiguration")]
        public FactoryRepoConfiguration RepoConfiguration
        {
            get;
            set;
        }
    }

    public class FactoryIdentity
    {
        /// <summary>
        /// Gets or sets the principal id of the identity.
        /// </summary>
        [JsonProperty(PropertyName = "principalId")]
        public Guid? PrincipalId
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets the client tenant id of the identity.
        /// </summary>
        [JsonProperty(PropertyName = "tenantId")]
        public Guid? TenantId
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets the identity type. Currently the only supported type is
        /// 'SystemAssigned'.
        /// </summary>
        [JsonProperty(PropertyName = "type")]
        public static string Type
        {
            get;
            set;
        }
    }

    public class FactoryRepoConfiguration
    {
        /// <summary>
        /// Gets or sets account name.
        /// </summary>
        [JsonProperty(PropertyName = "accountName")]
        public string AccountName
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets rrepository name.
        /// </summary>
        [JsonProperty(PropertyName = "repositoryName")]
        public string RepositoryName
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets collaboration branch.
        /// </summary>
        [JsonProperty(PropertyName = "collaborationBranch")]
        public string CollaborationBranch
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets root folder.
        /// </summary>
        [JsonProperty(PropertyName = "rootFolder")]
        public string RootFolder
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or sets last commit id.
        /// </summary>
        [JsonProperty(PropertyName = "lastCommitId")]
        public string LastCommitId
        {
            get;
            set;
        }
    }
}