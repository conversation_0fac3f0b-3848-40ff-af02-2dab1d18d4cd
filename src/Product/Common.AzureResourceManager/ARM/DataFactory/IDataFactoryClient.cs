﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.MachineLearning.AzureResourceManager.ARM.DataFactory
{
    public interface IDataFactoryClient
    {
        Task<Factory> GetDataFactoryAsync(
                    string subscriptionId,
                    string resourceGroupName,
                    string resourceName,
                    string token,
                    CancellationToken cancellationToken);

        Task DeleteDataFactoryAsync(
            string subscriptionId,
            string resourceGroupName,
            string resourceName,
            string token,
            CancellationToken cancellationToken);
    }
}
