# This file is used as a global file to control hash calculation of all targets to build.
# Hence it can be used to abandon old cache. Any change to this file will trigger full rebuild.
# By convention:
# - use the "minor version" for instances where you are bumping the version as a workaround for bugs.
# - use the "major version" for instances where there are changes in the Cache service that need a cache epoch update.
v=2.1