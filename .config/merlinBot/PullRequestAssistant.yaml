# This file is mostly a fork of the "defaults" following instructions from here:
# https://eng.ms/docs/products/1es-merlinbot/extensions/prassistant#want-to-update-default-settings

enabled: true                   # Enable pull request assistant.
branches:                       # Extension runs only on pull requests when target branch is in list of branches, by default runs on all branches
- master                        # Run extension only when target branch is either main or master
- main 
onPullRequestCreate:            # List of automated actions to take on a pull request create.
- prompt: 'copilot: review'     # Perform code review on create of a pull request. 

  # autoResolve: false          # Auto resolve code review feedback comment.
  autoResolve: true # default is false, changed to true for vienna repo
  
  detailed: false               # Should the generated review feedback be detailed.
  exclude:                      # Restrict the PR Assistant from  sending any files to the AI that may contain sensitive or proprietary information.
    pathFilters:
    - '*.ini'                   # Exclude files with these extensions
    - '*.json'
    - '*.yml'
    - '*.yaml'
    - '*.bicep'
    - '*.csproj'
    - '*.sln'
    - '*.xml'
    - '*.resx'
    - '*.proj'
- prompt: 'copilot: summary'    # Append pull request summary with an AI-Generated description.
  autoResolve: true             # Auto resolve a comment posted after pull request summary is generated.