parameters:
- name: jobName
  type: string
  default: ''
- name: displayName
  type: string
  default: ''
- name: scriptCommand
  type: string
  default: ''

jobs:
- job: ${{ parameters.jobName }}
  displayName: ${{ coalesce(parameters.displayName, parameters.jobName) }}
  pool:
    type: linux
  # disable build output (this is validation-only)
  variables:
    # dir is mostly empty, but we have to give *somehting* as output apparently
    ob_outputDirectory: vienna/build
    # trying a few ways to get CG to stop running or at least ignore vienna and stop taking forever.
    ob_sdl_cg_enabled: false
    ob_sdl_cg_ignoreDirectories: 'vienna'
    skipComponentGovernanceDetection: true
  steps:
  - script: |
      echo "##vso[task.logissue type=error]Error: jobName and scriptCommand may not be empty."
      echo "##vso[task.complete result=Failed]FAIL"
    displayName: 'Fail the job if required parameters are empty'
    condition: or(eq('${{ parameters.scriptCommand }}', ''), eq('${{ parameters.jobName }}', ''))
  - checkout: self
    fetchDepth: 1
    fetchTags: false
  # NOTE: might make sense to parameterize these later
  - task: NuGetAuthenticate@1
  - task: PipAuthenticate@1
    inputs:
      artifactFeeds: 'Vienna'
  - task: UseDotNet@2
    displayName: 'Use .NET SDK from global.json'
    inputs:
      packageType: sdk
      useGlobalJson: true
  - task: Bash@3
    inputs:
      # need to set this because we technically clone multiple repos (OB templates)
      workingDirectory: '$(Build.SourcesDirectory)/vienna'
      targetType: 'inline'
      script: |
        echo 'Running command: ${{ parameters.scriptCommand }}'
        pwd
        ls -A
        ${{ parameters.scriptCommand }}