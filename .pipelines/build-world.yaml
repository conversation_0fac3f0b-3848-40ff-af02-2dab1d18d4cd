# This pipeline runs on ANY changes to the vienna repository and does parallel build jobs to ensure
# baseline "zero build break" status. this pipeline is experimental (-clocke, 2024-10-24, remove me later?)
# and structured to nudge teams to "opt in" in the event of surprise build breaks. the permissions to add are very
# restrictive to ensure review prior to inserting possibly very expensive jobs.
#
# guidelines / restrictions:
# - builds ONLY. no testing/etc.
# - no msftkube drop builds for now, sticking to 1ES container builds -- second level breaks (e.g. docker build breaks etc) are not in scope
# - minimal builds are best, pick debug configs and/or avoid any expensive compile time optimization flags (no funroll loops!)
# - if your build cannot be expressed as a single line in a script, write a wrapper `.sh` and bring that
# - only Linux builds at present
# - no AzueMlCli submodule.

variables:
- name: LinuxContainerImage
  value: 'mcr.microsoft.com/onebranch/azurelinux/build:3.0'

trigger:
  branches:
    exclude:
    - master
    - releases/*

pr:
  branches:
    include:
    - master

resources:
  repositories:
  - repository: templates
    type: git
    name: OneBranch.Pipelines/GovernedTemplates
    ref: refs/heads/main

extends:
  template: v2/OneBranch.NonOfficial.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    git:
      submodules: false # Avoid bringing in AzureMlCli submodule because it's full of credscan violations
      fetchDepth: 1
      fetchTags: false
    globalSdl:
      # disabling scans to improve build speed, real builds that actually produce artifacts would do the proper scanning
      antimalwareScan:
        enabled: false
      cg:
        enabled: false
      tsa:
        enabled: false
      # a really bad hack to avoid credscan detections which aren't properly fixed in source, need a better file home for this thing?
      credscan:
        suppressionsFile: src/azureml-infra/.config/CredScanSuppressions.json
    stages:
    - stage: Build_World
      jobs:
      - template: /.pipelines/templates/build-world-job.yaml@self
        parameters:
          jobName: Infra
          displayName: 'Build src/azureml-infra (.NET)'
          scriptCommand: 'dotnet build --configuration Debug src/azureml-infra/dirs.proj'
      - template: /.pipelines/templates/build-world-job.yaml@self
        parameters:
          jobName: Aether
          displayName: 'Build src/aether (.NET)'
          scriptCommand: 'dotnet build --configuration Debug src/aether/dirs.proj'
      - template: /.pipelines/templates/build-world-job.yaml@self
        parameters:
          jobName: AzureML_API
          displayName: 'Build src/azureml-api (.NET)'
          scriptCommand: 'dotnet build --configuration Debug src/azureml-api/src/dirs.proj'
